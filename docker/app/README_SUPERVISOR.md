
## Supervisor configuration

add to supervisor config (set num of processes by numprocs property):

```
[program:es_front_instance]
command=/bin/bash -c "php /var/www/html/bin/console messenger:consume elasticFront --limit=200"
numprocs=10
process_name=%(program_name)s_%(process_num)02d
autostart=true
autorestart=true
redirect_stderr=true
environment=CONSUMER_ID=%(process_num)d
```

- open supervisor in CLI "`supervisorctl`"
- run supervisor with "`supervisord -c /etc/supervisor/supervisord.conf`"
- update supervisor configfile with "`supervisorctl reread`"
- restart/update supervisor processes with "`supervisorctl update`"
- get status of processes with "`supervisorctl status`"
- get actual logs from process with "`supervisorctl tail -f process_name`"
- stop all processes "`supervisorctl stop all`"
- retart all processes "`supervisorctl restart all`"
