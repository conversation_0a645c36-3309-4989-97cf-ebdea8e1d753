#!/bin/bash

CONFIG_PATH="app/config/environment.docker.neon"

# Extract password from local config
DB_PASS=$(grep -A 3 'database:' $CONFIG_PATH | grep 'password:' | awk '{print $2}' | xargs)
if [ $? -ne 0 ] || [ -z "$DB_PASS" ]; then
		echo "Error retrieving password or no password found."
		exit 1
fi

# Extract database name from local config
DB_NAME=$(grep -A 3 'database:' $CONFIG_PATH | grep 'database:' | awk '{print $2}' | xargs)
if [ $? -ne 0 ] || [ -z "$DB_NAME" ]; then
		echo "Error retrieving database name or no database name found."
		exit 1
fi

# Extract user from local config
DB_USER=$(grep -A 3 'database:' $CONFIG_PATH | grep 'user:' | awk '{print $2}' | xargs)
if [ $? -ne 0 ] || [ -z "$DB_USER" ]; then
		echo "Error retrieving user or no user found."
		exit 1
fi

DB_HOST='127.0.0.1'

mysql -h "$DB_HOST" -u"$DB_USER" -p"$DB_PASS" -e "DROP DATABASE IF EXISTS $DB_NAME;"
