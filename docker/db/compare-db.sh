#!/bin/bash

CONFIG_PATH="app/config/environment.docker.neon"

# Extract password from local config
DB_PASS=$(grep -A 3 'database:' $CONFIG_PATH | grep 'password:' | awk '{print $2}' | xargs)
if [ $? -ne 0 ] || [ -z "$DB_PASS" ]; then
		echo "Error retrieving password or no password found."
		exit 1
fi

# Extract database name from local config
DB_NAME=$(grep -A 3 'database:' $CONFIG_PATH | grep 'database:' | awk '{print $2}' | xargs)
if [ $? -ne 0 ] || [ -z "$DB_NAME" ]; then
		echo "Error retrieving database name or no database name found."
		exit 1
fi

# Extract user from local config
DB_USER=$(grep -A 3 'database:' $CONFIG_PATH | grep 'user:' | awk '{print $2}' | xargs)
if [ $? -ne 0 ] || [ -z "$DB_USER" ]; then
		echo "Error retrieving user or no user found."
		exit 1
fi

DB_HOST='127.0.0.1'

OLD_DB="temp_old_db"
NEW_DB="temp_new_db"
OLD_DUMP="docker/db/dump.sql"
NEW_DUMP="docker/db/docker_dump.sql"
INSERTED_CSV="inserted_rows.csv"
UPDATED_CSV="updated_rows.csv"
INSERT_MIGRATION_SQL="migrations/other/basic-data/$(date +%Y-%m-%d-%H%M%S)-inserts.sql"
UPDATE_MIGRATION_SQL="migrations/other/basic-data/$(date +%Y-%m-%d-%H%M%S)-updates.sql"

echo "Creating a fresh dump from the current database..."
mysqldump -h "$DB_HOST" -u"$DB_USER" -p"$DB_PASS" "$DB_NAME" > "$NEW_DUMP"

echo "Removing temporary databases..."
mysql -h "$DB_HOST" -u"$DB_USER" -p"$DB_PASS" -e "DROP DATABASE IF EXISTS $OLD_DB;"
mysql -h "$DB_HOST" -u"$DB_USER" -p"$DB_PASS" -e "DROP DATABASE IF EXISTS $NEW_DB;"

echo "Creating temporary databases..."
mysql -h "$DB_HOST" -u"$DB_USER" -p"$DB_PASS" -e "CREATE DATABASE IF NOT EXISTS $OLD_DB;"
mysql -h "$DB_HOST" -u"$DB_USER" -p"$DB_PASS" -e "CREATE DATABASE IF NOT EXISTS $NEW_DB;"

echo "Restoring old dump to $OLD_DB..."
mysql -h "$DB_HOST" -u"$DB_USER" -p"$DB_PASS" $OLD_DB < "$OLD_DUMP"

echo "Restoring fresh dump to $NEW_DB..."
mysql -h "$DB_HOST" -u"$DB_USER" -p"$DB_PASS" $NEW_DB < "$NEW_DUMP"

if ! mysql -h "$DB_HOST" -u"$DB_USER" -p"$DB_PASS" -e "USE $OLD_DB;" 2>/dev/null; then
    echo "Error: Old database ($OLD_DB) does not exist."
    exit 1
fi

if ! mysql -h "$DB_HOST" -u"$DB_USER" -p"$DB_PASS" -e "USE $NEW_DB;" 2>/dev/null; then
    echo "Error: New database ($NEW_DB) does not exist."
    exit 1
fi

TABLES=$(mysql -h "$DB_HOST" -u"$DB_USER" -p"$DB_PASS" -B -e "SHOW TABLES IN $NEW_DB;" | tail -n +2)

for TABLE in $TABLES; do
    echo "Processing table: $TABLE"

    COLUMNS=$(mysql -h "$DB_HOST" -u"$DB_USER" -p"$DB_PASS" -e "SHOW COLUMNS FROM $NEW_DB.$TABLE;" | awk 'NR > 1 {print $1}' | tr '\n' ',' | sed 's/,$//')

  	QUERY_INSERTED="
        SELECT $COLUMNS
        FROM $NEW_DB.$TABLE t1
        WHERE NOT EXISTS (
            SELECT 1 FROM $OLD_DB.$TABLE t2 WHERE t1.id = t2.id
        );
    "
    mysql -h "$DB_HOST" -u"$DB_USER" -p"$DB_PASS" -B -e "$QUERY_INSERTED" | \
    awk -F'\t' 'NR > 1 {for (i=1; i<=NF; i++) if ($i ~ /^[0-9.]+$/) printf $i";"; else printf "\""$i"\";"; print ""}' >> "$INSERTED_CSV"

   # Generate UPDATED rows CSV
       WHERE_CLAUSE=""
       for FIELD in $(echo $COLUMNS | tr ',' ' '); do
           WHERE_CLAUSE+="t1.$FIELD != t2.$FIELD OR (t1.$FIELD IS NULL AND t2.$FIELD IS NOT NULL) OR (t1.$FIELD IS NOT NULL AND t2.$FIELD IS NULL) OR "
       done
       WHERE_CLAUSE=${WHERE_CLAUSE% OR }
       QUERY_UPDATED="
           SELECT t1.*
           FROM $NEW_DB.$TABLE t1
           INNER JOIN $OLD_DB.$TABLE t2
           ON t1.id = t2.id
           WHERE $WHERE_CLAUSE;
       "
       mysql -h "$DB_HOST" -u"$DB_USER" -p"$DB_PASS" -B -e "$QUERY_UPDATED" | \
       awk -F'\t' 'NR > 1 {for (i=1; i<=NF; i++) if ($i ~ /^[0-9.]+$/) printf $i";"; else printf "\""$i"\";"; print ""}' >> "$UPDATED_CSV"


    # Generate SQL Migration for INSERTED rows
   if [ -s "$INSERTED_CSV" ]; then
       echo "Generating INSERT statements for $TABLE..."
       echo "INSERT INTO $TABLE ($COLUMNS) VALUES" >> "$INSERT_MIGRATION_SQL"
       while IFS=';' read -r ROW; do
           # Initialize the INSERT row
           INSERT_ROW="("

           # Split the row into fields
           IFS=';' read -r -a FIELD_VALUES <<< "$ROW"

           for VALUE in "${FIELD_VALUES[@]}"; do
               if [[ "$VALUE" == "\"NULL\"" || -z "$VALUE" ]]; then
                   INSERT_ROW+="NULL,"
               else
                   # Aggressive quote stripping and cleaning
                   CLEANED_VALUE=$(echo "$VALUE" | sed -e 's/^"*//g' -e 's/"*$//g' -e "s/^'*//g" -e "s/'*$//g")

                   # Escape single quotes for SQL
                   ESCAPED_VALUE=$(printf "%s" "$CLEANED_VALUE" | sed "s/'/''/g")
                   INSERT_ROW+="'$ESCAPED_VALUE',"
               fi
           done

           # Remove trailing comma and close the row
           INSERT_ROW=${INSERT_ROW%,}
           INSERT_ROW+="),"

           # Append the INSERT row to the migration SQL file
           echo "$INSERT_ROW" >> "$INSERT_MIGRATION_SQL"
       done < "$INSERTED_CSV"

       # Replace the final trailing comma with a semicolon
       sed -i -E 's/,$/;/' "$INSERT_MIGRATION_SQL"
   fi

    # Generate SQL Migration for UPDATED rows
    if [ -s "$UPDATED_CSV" ]; then
        echo "Generating UPDATE statements for $TABLE..."

       while IFS=';' read -r ROW; do
               IFS=';' read -r -a FIELD_VALUES <<< "$ROW"

               UPDATE_QUERY="UPDATE \`$TABLE\` SET "

               for ((i=0; i<${#FIELD_VALUES[@]}; i++)); do
                   FIELD=$(echo "$COLUMNS" | cut -d',' -f$((i+1)))
                   FIELD_VALUE="${FIELD_VALUES[$i]}"

                   if [[ "$FIELD_VALUE" == "\"NULL\"" || -z "$FIELD_VALUE" ]]; then
                       UPDATE_QUERY+="\`$FIELD\`=NULL,"
                   else
                       # Aggressive quote stripping and cleaning
                       CLEANED_VALUE=$(echo "$FIELD_VALUE" | sed -e 's/^"*//g' -e 's/"*$//g' -e "s/^'*//g" -e "s/'*$//g")

                       # Escape single quotes for SQL
                       ESCAPED_VALUE=$(printf "%s" "$CLEANED_VALUE" | sed "s/'/''/g")

                       UPDATE_QUERY+="\`$FIELD\`='$ESCAPED_VALUE',"
                   fi
               done

               UPDATE_QUERY="${UPDATE_QUERY%,} WHERE id='${FIELD_VALUES[0]}';"
               echo "$UPDATE_QUERY" >> "$UPDATE_MIGRATION_SQL"
           done < "$UPDATED_CSV"


    fi
    rm -f "$INSERTED_CSV" "$UPDATED_CSV"
    rm -f "$INSERT_MIGRATION_SQL-E"
    rm -f "$UPDATE_MIGRATION_SQL-E"
done

# if file is empty, remove it or show message
if [ ! -s "$INSERT_MIGRATION_SQL" ]; then
    echo "No INSERT migration SQL generated."
    rm -f "$INSERT_MIGRATION_SQL"
    else
		echo "Migration file generated at $INSERT_MIGRATION_SQL"
fi

if [ ! -s "$UPDATE_MIGRATION_SQL" ]; then
    echo "No UPDATE migration SQL generated."
    rm -f "$UPDATE_MIGRATION_SQL"
    else
    			echo "Migration file generated at $UPDATE_MIGRATION_SQL"
fi
