Index: vendor/contributte/elastica/src/Diagnostics/Panel.php
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/vendor/contributte/elastica/src/Diagnostics/Panel.php b/vendor/contributte/elastica/src/Diagnostics/Panel.php
--- a/vendor/contributte/elastica/src/Diagnostics/Panel.php	
+++ b/vendor/contributte/elastica/src/Diagnostics/Panel.php	(date 1664330036000)
@@ -86,7 +86,8 @@
 
 		$processedQueries = [];
 		$allQueries = $this->queries;
-
+		$totalTime = $this->totalTime;
+		
 		foreach ($allQueries as $authority => $requests) {
 			/** @var Request[] $item */
 			foreach ($requests as $i => $item) {
