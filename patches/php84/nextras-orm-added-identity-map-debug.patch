Index: vendor/nextras/orm/src/Repository/Repository.php
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/vendor/nextras/orm/src/Repository/Repository.php b/vendor/nextras/orm/src/Repository/Repository.php
--- a/vendor/nextras/orm/src/Repository/Repository.php	
+++ b/vendor/nextras/orm/src/Repository/Repository.php	(date 1734209122000)
@@ -365,6 +365,12 @@
 		}
 		$this->onAfterPersist($entity);
 	}
+	
+	public function getIdentityMap(): IdentityMap
+	{
+		return $this->identityMap;
+		
+	}
 
 
 	public function remove(IEntity $entity, bool $withCascade = true): IEntity
