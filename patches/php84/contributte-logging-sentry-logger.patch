Index: vendor/contributte/logging/src/Sentry/SentryLogger.php
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/vendor/contributte/logging/src/Sentry/SentryLogger.php b/vendor/contributte/logging/src/Sentry/SentryLogger.php
--- a/vendor/contributte/logging/src/Sentry/SentryLogger.php	
+++ b/vendor/contributte/logging/src/Sentry/SentryLogger.php	
@@ -44,6 +44,10 @@
 			$configuration[self::CONFIG_OPTIONS] = [];
 		}
 
+		foreach ($configuration[self::CONFIG_OPTIONS]['integrations'] ?? [] as $key => $class) {
+			$configuration[self::CONFIG_OPTIONS]['integrations'][$key] = new $class();
+		}
+
 		$this->configuration = $configuration;
 	}
 
