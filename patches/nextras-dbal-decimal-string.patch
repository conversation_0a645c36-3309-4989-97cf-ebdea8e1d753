Index: vendor/nextras/dbal/src/Drivers/Mysqli/MysqliResultAdapter.php
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/vendor/nextras/dbal/src/Drivers/Mysqli/MysqliResultAdapter.php b/vendor/nextras/dbal/src/Drivers/Mysqli/MysqliResultAdapter.php
--- a/vendor/nextras/dbal/src/Drivers/Mysqli/MysqliResultAdapter.php	
+++ b/vendor/nextras/dbal/src/Drivers/Mysqli/MysqliResultAdapter.php	(date 1641760783000)
@@ -30,8 +30,8 @@
 		MYSQLI_TYPE_LONGLONG => self::TYPE_INT,
 		MYSQLI_TYPE_YEAR => self::TYPE_INT,
 
-		MYSQLI_TYPE_DECIMAL => self::TYPE_FLOAT,
-		MYSQLI_TYPE_NEWDECIMAL => self::TYPE_FLOAT,
+		MYSQLI_TYPE_DECIMAL => self::TYPE_STRING,
+		MYSQLI_TYPE_NEWDECIMAL => self::TYPE_STRING,
 		MYSQLI_TYPE_DOUBLE => self::TYPE_FLOAT,
 		MYSQLI_TYPE_FLOAT => self::TYPE_FLOAT,
 
