Index: vendor/contributte/elastica/src/DI/ElasticaExtension.php
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/vendor/contributte/elastica/src/DI/ElasticaExtension.php b/vendor/contributte/elastica/src/DI/ElasticaExtension.php
--- a/vendor/contributte/elastica/src/DI/ElasticaExtension.php	
+++ b/vendor/contributte/elastica/src/DI/ElasticaExtension.php	(date 1664330036000)
@@ -62,7 +62,7 @@
 		$elastica = $builder->addDefinition($this->prefix('client'))
 			->setFactory(ContributteClient::class, [$this->config->config]);
 
-		if ($this->config->debug) {
+		if ($this->config->debug && php_sapi_name() !== 'cli') {
 			$builder->addDefinition($this->prefix('panel'))
 				->setFactory(Panel::class);
 
