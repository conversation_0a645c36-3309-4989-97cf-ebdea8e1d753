const config = require('./tasks/helpers/getConfig.js');
const plugin = require('tailwindcss/plugin');

module.exports = {
	content: ['./app/FrontModule/**/*.latte', './app/PostType/**/*.latte'],
	prefix: 'tw-',
	safelist: ['tw-text-left', 'tw-text-center', 'tw-text-right', 'tw-hidden'],
	corePlugins: {
		preflight: false,
	},
	theme: {
		screens: config.mediaQueries.breakpoints,
		extend: {
			colors: {
				primary: 'var(--color-primary)',
				secondary: 'var(--color-secondary)',
				'primary-hover': 'var(--color-primary-hover)',
				'secondary-hover': 'var(--color-secondary-hover)',
				black: 'var(--color-black)',
				white: 'var(--color-white)',
				green: 'var(--color-green)',
				'dark-blue': 'var(--color-dark-blue)',
				'medior-blue': {
					DEFAULT: 'var(--color-medior-blue)',
					30: 'var(--color-medior-blue-30)',
				},
				orange: 'var(--color-orange)',
				red: 'var(--color-red)',
				bd: 'var(--color-bd)',
				bg: 'var(--color-bg)',
				link: 'var(--color-link)',
				hover: 'var(--color-hover)',
				gray: {
					DEFAULT: 'var(--color-gray)',
					50: 'var(--color-gray-50)',
					20: 'var(--color-gray-20)',
					10: 'var(--color-gray-10)',
				},
				disabled: 'var(--color-disabled)',
				blue: {
					DEFAULT: 'var(--color-blue)',
					hover: 'var(--color-blue-hover)',
					10: 'var(--color-blue-10)',
					20: 'var(--color-blue-20)',
					30: 'var(--color-blue-30)',
				},
				text: 'var(--color-text)',
				brown: {
					DEFAULT: 'var(--color-brown)',
					hover: 'var(--color-brown-hover)',
				},
				beige: 'var(--color-beige)',
				rust: {
					DEFAULT: 'var(--color-rust)',
					hover: 'var(--color-rust-hover)',
					10: 'var(--color-rust-10)',
					'hover-15': 'var(--color-rust-hover-15)',
				},
				yellow: {
					DEFAULT: 'var(--color-yellow)',
					15: 'var(--color-yellow-15)',
				},
				teal: {
					DEFAULT: 'var(--color-teal)',
					hover: 'var(--color-teal-hover)',
					10: 'var(--color-teal-10)',
					20: 'var(--color-teal-20)',
				},
				azure: 'var(--color-azure)',
				cyan: 'var(--color-cyan)',
			},
			fontFamily: {
				primary: 'var(--font-primary)',
				secondary: 'var(--font-secondary)',
				tertiary: 'var(--font-tertiary)',
			},
			spacing: {
				xs: 'var(--spacing-xs)',
				sm: 'var(--spacing-sm)',
				md: 'var(--spacing-md)',
				lg: 'var(--spacing-lg)',
				xl: 'var(--spacing-xl)',
				'2xl': 'var(--spacing-2xl)',
			},
		},
	},
	plugins: [
		require('@tailwindcss/container-queries'),
		plugin(function ({ addVariant, e }) {
			addVariant('hover', ({ modifySelectors, separator }) => {
				modifySelectors(({ className }) => {
					return `.hoverevents .${e(`hover${separator}${className}`)}:hover`;
				});
			});
		}),
	],
};
