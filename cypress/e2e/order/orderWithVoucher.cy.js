describe('order process', () => {
	it('opens', () => {
		cy.visit('/e-shop');
		cy.rejectCookies();

		// navigate to the first and only product
		cy.get('.b-product__link').first().click({ force: true });
		cy.contains('.f-add-to-cart__price', '128 Kč');
		cy.contains('.f-add-to-cart__availability', 'Skladem na prodejně');

		// add the product to cart
		cy.get('.f-add-to-cart__btn').first().click();

		cy.basketFlow({voucherCode: 'test3ELAQL', price: 28, deliveryPrice: 8});

	});
});
