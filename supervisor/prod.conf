[program:elastic]
command=php{$DEPLOYMENT_PHP_VERSION} -d memory_limit=4G {$DEPLOYMENT_PATH}/current/bin/console messenger:consume elasticPriorityFront elasticFront --limit=20 --time-limit=180
user={$DEPLOYMENT_USER}
numprocs=5
startsecs=0
autostart=true
autorestart=true
startretries=15
process_name=%(program_name)s_%(process_num)02d
environment=MESSENGER_CONSUMER_NAME=%(program_name)s%(process_num)02d

[program:erp]
command=php{$DEPLOYMENT_PHP_VERSION} -d memory_limit=4G {$DEPLOYMENT_PATH}/current/bin/console messenger:consume erpFront --limit=5 --time-limit=180
user={$DEPLOYMENT_USER}
numprocs=1
startsecs=0
autostart=true
autorestart=true
startretries=15
process_name=%(program_name)s_%(process_num)02d
environment=MESSENGER_CONSUMER_NAME=%(program_name)s%(process_num)02d

[program:default]
command=php{$DEPLOYMENT_PHP_VERSION} -d memory_limit=4G {$DEPLOYMENT_PATH}/current/bin/console messenger:consume defaultFront --limit=10 --time-limit=180
user={$DEPLOYMENT_USER}
numprocs=2
startsecs=0
autostart=true
autorestart=true
startretries=15
process_name=%(program_name)s_%(process_num)02d
environment=MESSENGER_CONSUMER_NAME=%(program_name)s%(process_num)02d

[program:failure]
command=php{$DEPLOYMENT_PHP_VERSION} {$DEPLOYMENT_PATH}/current/bin/console messenger:consume failure --limit=5 --time-limit=30
user={$DEPLOYMENT_USER}
numprocs=5
startsecs=0
autostart=true
autorestart=true
startretries=10
process_name=%(program_name)s_%(process_num)02d
environment=MESSENGER_CONSUMER_NAME=%(program_name)s%(process_num)02d

[program:scheduler-default]
command=php{$DEPLOYMENT_PHP_VERSION} {$DEPLOYMENT_PATH}/current/bin/console messenger:consume scheduler_default --memory-limit=128M --time-limit=180
user={$DEPLOYMENT_USER}
numprocs=2
startsecs=0
autostart=true
autorestart=true
startretries=10
process_name=%(program_name)s_%(process_num)02d
environment=MESSENGER_CONSUMER_NAME=%(program_name)s%(process_num)02d

[program:scheduler-cron-commands]
command=php{$DEPLOYMENT_PHP_VERSION} -d memory_limit=4G {$DEPLOYMENT_PATH}/current/bin/console messenger:consume cronCommands --limit=1
user={$DEPLOYMENT_USER}
numprocs=10
startsecs=0
autostart=true
autorestart=true
startretries=10
process_name=%(program_name)s_%(process_num)02d
environment=MESSENGER_CONSUMER_NAME=%(program_name)s%(process_num)02d
