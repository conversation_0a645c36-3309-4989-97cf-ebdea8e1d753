import http from 'k6/http';
import {sleep, check} from 'k6';
import { parseHTML } from 'k6/html';
import { Trend } from 'k6/metrics';
const extractedValueTrend = new Trend('memory_usage');

// k6 run --vus 30 --duration 30s --insecure-skip-tls-verify testK6/hp.js

// ?disabledParts=header,footer,content,productBox,structuredData,speculationRules

export default function () {
	const res = http.get('https://superadmin:<EMAIL>/?throw');
	const doc = parseHTML(res.body); // equivalent to res.html()
	const memory = doc.find('span[data-infom]').attr('data-infom');
	if (memory !== undefined) {
		extractedValueTrend.add(memory);
	}

	check(res, {'status was 200': (r) => r.status === 200})
	sleep(0.5);
}
