


INSERT INTO `delivery_method` (`id`, `externalId`, `deliveryMethodUniqueIdentifier`, `name`, `desc`, `pageText`, `tooltip`, `sort`, `public`, `isRecommended`, `pageShow`, `isGift`, `calculateFree`, `deliveryDayFrom`, `deliveryDayTo`, `deliveryHourByStock`, `mutationId`, `vats`, `customFieldsJson`) VALUES
	(1, NULL, 'PPL', 'Kurýr PPL', '', '<p><span>Zásilky odeslané přes PPL dorazí adresátovi do druhého dne po odeslání. PPL nabízí možnost dodání na adresu, případně do jednoho z 600 výdejních boxů (ParcelBox) nebo na výdejní místo (ParcelShop). O expedici objednávky Vás informuje PPL prostřednictvím e-mailové a SMS zprávy, je proto zkontrolovat správnost kontaktních údajů. Stejným způsobem budete informování o doručování zásilky. V boxu na Vás zásilka počká 3 dny, na výdejním místě až 7 dní. Pokud nebude zásilka vyzvednuta v uvedené lhůtě, nebo ji odmítnete převzít, Zásilkovna ji vrací zpět na náš sklad. V košíku najdete i možnost doručení na Slovensko.</span></p>', NULL, 1, 1, 0, 1, 1, 1, 1, 2, '{"shop":"13:00","supplier_store":"13:00"}', 1, '{"1":"standard"}', '{"deliveryPayment":[{"icon":["527106"]}]}');
INSERT INTO `delivery_method` (`id`, `externalId`, `deliveryMethodUniqueIdentifier`, `name`, `desc`, `pageText`, `tooltip`, `sort`, `public`, `isRecommended`, `pageShow`, `isGift`, `calculateFree`, `deliveryDayFrom`, `deliveryDayTo`, `deliveryHourByStock`, `mutationId`, `vats`, `customFieldsJson`) VALUES
	(2, NULL, 'Zasilkovna', 'Zásilkovna na adresu', '', NULL, NULL, 0, 1, 1, 0, 0, 1, 1, 2, '{"shop":"15:30","supplier_store":"17:30"}', 1, '{"1":"standard"}', '{"deliveryPayment":[{"icon":["527105"]}]}');
INSERT INTO `delivery_method` (`id`, `externalId`, `deliveryMethodUniqueIdentifier`, `name`, `desc`, `pageText`, `tooltip`, `sort`, `public`, `isRecommended`, `pageShow`, `isGift`, `calculateFree`, `deliveryDayFrom`, `deliveryDayTo`, `deliveryHourByStock`, `mutationId`, `vats`, `customFieldsJson`) VALUES
	(3, NULL, 'ZasilkovnaPickup', 'Zásilkovna výdejní místa a boxy', '', '<p>BLABLABLABLABLA</p>', NULL, 0, 1, 1, 1, 0, 1, 1, 2, '{"shop":"15:30","supplier_store":"15:30"}', 1, '{"1":"standard"}', '{"deliveryPayment":[{"icon":["527105"],"marker":"527105"}]}');
INSERT INTO `delivery_method` (`id`, `externalId`, `deliveryMethodUniqueIdentifier`, `name`, `desc`, `pageText`, `tooltip`, `sort`, `public`, `isRecommended`, `pageShow`, `isGift`, `calculateFree`, `deliveryDayFrom`, `deliveryDayTo`, `deliveryHourByStock`, `mutationId`, `vats`, `customFieldsJson`) VALUES
	(4, NULL, 'BalikovnaPickup', 'Balíkovna', '', NULL, NULL, 0, 1, 0, 0, 0, 1, 1, 2, '{"shop":"15:30","supplier_store":"15:30"}', 1, '{"1":"standard"}', '{"deliveryPayment":[{"icon":["527111"]}]}');
INSERT INTO `delivery_method` (`id`, `externalId`, `deliveryMethodUniqueIdentifier`, `name`, `desc`, `pageText`, `tooltip`, `sort`, `public`, `isRecommended`, `pageShow`, `isGift`, `calculateFree`, `deliveryDayFrom`, `deliveryDayTo`, `deliveryHourByStock`, `mutationId`, `vats`, `customFieldsJson`) VALUES
	(5, NULL, 'CzechPost', 'Balíkovna na adresu', '', NULL, NULL, 0, 1, 0, 1, 0, 1, 1, 2, '{"shop":"15:30","supplier_store":"15:30"}', 1, '{"1":"standard"}', '{"deliveryPayment":[{"icon":["527110"]}]}');
INSERT INTO `delivery_method` (`id`, `externalId`, `deliveryMethodUniqueIdentifier`, `name`, `desc`, `pageText`, `tooltip`, `sort`, `public`, `isRecommended`, `pageShow`, `isGift`, `calculateFree`, `deliveryDayFrom`, `deliveryDayTo`, `deliveryHourByStock`, `mutationId`, `vats`, `customFieldsJson`) VALUES
	(6, NULL, 'CzechPostPickup', 'Balík na poštu', '', NULL, NULL, 0, 1, 0, 1, 0, 1, 1, 2, '{"shop":"15:30","supplier_store":"15:30"}', 1, '{"1":"standard"}', '{"deliveryPayment":[{"icon":["527109"]}]}');
INSERT INTO `delivery_method` (`id`, `externalId`, `deliveryMethodUniqueIdentifier`, `name`, `desc`, `pageText`, `tooltip`, `sort`, `public`, `isRecommended`, `pageShow`, `isGift`, `calculateFree`, `deliveryDayFrom`, `deliveryDayTo`, `deliveryHourByStock`, `mutationId`, `vats`, `customFieldsJson`) VALUES
	(7, NULL, 'PPLPickup', 'PPL výdejní místa a boxy', '', NULL, NULL, 0, 1, 0, 0, 0, 1, 1, 2, '{"shop":"13:00","supplier_store":"13:00"}', 1, '{"1":"standard"}', '{"deliveryPayment":[{"icon":["527108"]}]}');
INSERT INTO `delivery_method` (`id`, `externalId`, `deliveryMethodUniqueIdentifier`, `name`, `desc`, `pageText`, `tooltip`, `sort`, `public`, `isRecommended`, `pageShow`, `isGift`, `calculateFree`, `deliveryDayFrom`, `deliveryDayTo`, `deliveryHourByStock`, `mutationId`, `vats`, `customFieldsJson`) VALUES
	(8, NULL, 'Store', 'Prodejna Praha 7, V Háji 15', '', NULL, NULL, 0, 1, 0, 0, 0, 0, 1, 1, '{"shop":"18","supplier_store":"18"}', 1, '{"1":"standard"}', '{"deliveryPayment":[{"icon":["1813"]}]}');
INSERT INTO `delivery_method` (`id`, `externalId`, `deliveryMethodUniqueIdentifier`, `name`, `desc`, `pageText`, `tooltip`, `sort`, `public`, `isRecommended`, `pageShow`, `isGift`, `calculateFree`, `deliveryDayFrom`, `deliveryDayTo`, `deliveryHourByStock`, `mutationId`, `vats`, `customFieldsJson`) VALUES
	(9, NULL, 'Electronic', 'Elektronicky', 'Kupujete elektronický produkt.\nPo zaplacení uvidíte obsah ve svém účtu', NULL, NULL, 0, 1, 0, 0, 0, 0, 0, NULL, '{"shop":null,"supplier_store":null}', 1, '{"1":"standard"}', '{"deliveryPayment":[{"icon":["1350113"]}]}');
INSERT INTO `delivery_method` (`id`, `externalId`, `deliveryMethodUniqueIdentifier`, `name`, `desc`, `pageText`, `tooltip`, `sort`, `public`, `isRecommended`, `pageShow`, `isGift`, `calculateFree`, `deliveryDayFrom`, `deliveryDayTo`, `deliveryHourByStock`, `mutationId`, `vats`, `customFieldsJson`) VALUES
	(10, NULL, 'Legacy', 'Importovaná doprava z ERP', '', NULL, NULL, 0, 1, 0, 0, 0, 0, 0, NULL, '{"shop":null,"supplier_store":null}', 1, '[]', '{}');

INSERT INTO `delivery_method_currency` (`id`, `deliveryMethodId`, `currency`) VALUES
	(1, 2, 'CZK');
INSERT INTO `delivery_method_currency` (`id`, `deliveryMethodId`, `currency`) VALUES
	(2, 3, 'CZK');
INSERT INTO `delivery_method_currency` (`id`, `deliveryMethodId`, `currency`) VALUES
	(3, 4, 'CZK');
INSERT INTO `delivery_method_currency` (`id`, `deliveryMethodId`, `currency`) VALUES
	(4, 1, 'CZK');
INSERT INTO `delivery_method_currency` (`id`, `deliveryMethodId`, `currency`) VALUES
	(5, 1, 'EUR');
INSERT INTO `delivery_method_currency` (`id`, `deliveryMethodId`, `currency`) VALUES
	(6, 5, 'CZK');
INSERT INTO `delivery_method_currency` (`id`, `deliveryMethodId`, `currency`) VALUES
	(7, 6, 'CZK');
INSERT INTO `delivery_method_currency` (`id`, `deliveryMethodId`, `currency`) VALUES
	(8, 7, 'CZK');
INSERT INTO `delivery_method_currency` (`id`, `deliveryMethodId`, `currency`) VALUES
	(9, 7, 'EUR');
INSERT INTO `delivery_method_currency` (`id`, `deliveryMethodId`, `currency`) VALUES
	(10, 8, 'CZK');
INSERT INTO `delivery_method_currency` (`id`, `deliveryMethodId`, `currency`) VALUES
	(11, 9, 'CZK');
INSERT INTO `delivery_method_currency` (`id`, `deliveryMethodId`, `currency`) VALUES
	(12, 9, 'EUR');
INSERT INTO `delivery_method_currency` (`id`, `deliveryMethodId`, `currency`) VALUES
	(13, 10, 'CZK');

INSERT INTO `delivery_method_price` (`id`, `externalId`, `deliveryMethodId`, `priceLevelId`, `stateId`, `price_amount`, `price_currency`, `freeFrom`, `maxWeight`, `maxCodPrice`) VALUES
	(1, NULL, 1, 1, 1, 99.0000, 'CZK', 1199.0000, NULL, NULL);
INSERT INTO `delivery_method_price` (`id`, `externalId`, `deliveryMethodId`, `priceLevelId`, `stateId`, `price_amount`, `price_currency`, `freeFrom`, `maxWeight`, `maxCodPrice`) VALUES
	(2, NULL, 2, 1, 1, 69.0000, 'CZK', 1199.0000, 15000, 5000);
INSERT INTO `delivery_method_price` (`id`, `externalId`, `deliveryMethodId`, `priceLevelId`, `stateId`, `price_amount`, `price_currency`, `freeFrom`, `maxWeight`, `maxCodPrice`) VALUES
	(3, NULL, 3, 1, 1, 69.0000, 'CZK', 1199.0000, 15000, 5000);
INSERT INTO `delivery_method_price` (`id`, `externalId`, `deliveryMethodId`, `priceLevelId`, `stateId`, `price_amount`, `price_currency`, `freeFrom`, `maxWeight`, `maxCodPrice`) VALUES
	(4, NULL, 4, 1, 1, 39.0000, 'CZK', 1199.0000, 30000, NULL);
INSERT INTO `delivery_method_price` (`id`, `externalId`, `deliveryMethodId`, `priceLevelId`, `stateId`, `price_amount`, `price_currency`, `freeFrom`, `maxWeight`, `maxCodPrice`) VALUES
	(5, NULL, 5, 1, 1, 89.0000, 'CZK', 1199.0000, 30000, NULL);
INSERT INTO `delivery_method_price` (`id`, `externalId`, `deliveryMethodId`, `priceLevelId`, `stateId`, `price_amount`, `price_currency`, `freeFrom`, `maxWeight`, `maxCodPrice`) VALUES
	(6, NULL, 6, 1, 1, 99.0000, 'CZK', 1199.0000, 30000, NULL);
INSERT INTO `delivery_method_price` (`id`, `externalId`, `deliveryMethodId`, `priceLevelId`, `stateId`, `price_amount`, `price_currency`, `freeFrom`, `maxWeight`, `maxCodPrice`) VALUES
	(7, NULL, 7, 1, 1, 79.0000, 'CZK', 1199.0000, NULL, NULL);
INSERT INTO `delivery_method_price` (`id`, `externalId`, `deliveryMethodId`, `priceLevelId`, `stateId`, `price_amount`, `price_currency`, `freeFrom`, `maxWeight`, `maxCodPrice`) VALUES
	(8, NULL, 8, 1, 1, 0.0000, 'CZK', 0.0000, NULL, NULL);
INSERT INTO `delivery_method_price` (`id`, `externalId`, `deliveryMethodId`, `priceLevelId`, `stateId`, `price_amount`, `price_currency`, `freeFrom`, `maxWeight`, `maxCodPrice`) VALUES
	(9, NULL, 9, 1, 1, 0.0000, 'CZK', NULL, NULL, NULL);
INSERT INTO `delivery_method_price` (`id`, `externalId`, `deliveryMethodId`, `priceLevelId`, `stateId`, `price_amount`, `price_currency`, `freeFrom`, `maxWeight`, `maxCodPrice`) VALUES
	(10, NULL, 10, 1, 1, 0.0000, 'CZK', NULL, NULL, NULL);


INSERT INTO `payment_method` (`id`, `externalId`, `paymentMethodUniqueIdentifier`, `name`, `desc`, `pageText`, `tooltip`, `sort`, `public`, `isRecommended`, `pageShow`, `mutationId`, `vats`, `customFieldsJson`) VALUES
	(1, NULL, 'BankTransfer', 'Bankovním převodem', '', NULL, NULL, 0, 1, 1, 0, 1, '{"1":"standard"}', '{"paymentAccount":[{"bban":"********/2010","iban":"************************","swift":"FIOBCZPPXXX"}]}');
INSERT INTO `payment_method` (`id`, `externalId`, `paymentMethodUniqueIdentifier`, `name`, `desc`, `pageText`, `tooltip`, `sort`, `public`, `isRecommended`, `pageShow`, `mutationId`, `vats`, `customFieldsJson`) VALUES
	(2, NULL, 'CashOnDelivery', 'Dobírka', '', NULL, NULL, 0, 1, 0, 0, 1, '{"1":"standard"}', '{}');
INSERT INTO `payment_method` (`id`, `externalId`, `paymentMethodUniqueIdentifier`, `name`, `desc`, `pageText`, `tooltip`, `sort`, `public`, `isRecommended`, `pageShow`, `mutationId`, `vats`, `customFieldsJson`) VALUES
	(3, NULL, 'BenefitCard', 'Benefitní kartou Benefity', '', NULL, NULL, 0, 1, 0, 1, 1, '{"1":"standard"}', '{"deliveryPayment":[{"icon":["205","205"]}]}');
INSERT INTO `payment_method` (`id`, `externalId`, `paymentMethodUniqueIdentifier`, `name`, `desc`, `pageText`, `tooltip`, `sort`, `public`, `isRecommended`, `pageShow`, `mutationId`, `vats`, `customFieldsJson`) VALUES
	(4, NULL, 'Card', 'Kartou on-line', '', NULL, NULL, 0, 1, 0, 1, 1, '{"1":"standard"}', '{}');
INSERT INTO `payment_method` (`id`, `externalId`, `paymentMethodUniqueIdentifier`, `name`, `desc`, `pageText`, `tooltip`, `sort`, `public`, `isRecommended`, `pageShow`, `mutationId`, `vats`, `customFieldsJson`) VALUES
	(5, NULL, 'Certificate', '', '', NULL, NULL, 0, 0, 0, 0, 1, '{}', '{}');
INSERT INTO `payment_method` (`id`, `externalId`, `paymentMethodUniqueIdentifier`, `name`, `desc`, `pageText`, `tooltip`, `sort`, `public`, `isRecommended`, `pageShow`, `mutationId`, `vats`, `customFieldsJson`) VALUES
	(6, NULL, 'Store', 'Na prodejně', '', NULL, NULL, 0, 1, 0, 0, 1, '{"1":"standard"}', '{}');
INSERT INTO `payment_method` (`id`, `externalId`, `paymentMethodUniqueIdentifier`, `name`, `desc`, `pageText`, `tooltip`, `sort`, `public`, `isRecommended`, `pageShow`, `mutationId`, `vats`, `customFieldsJson`) VALUES
	(7, NULL, 'InvoicePayment', 'Na fakturu', '', NULL, 'Faktura má splatnost 14 dní', 0, 1, 0, 0, 1, '{"1":"standard"}', '{}');
INSERT INTO `payment_method` (`id`, `externalId`, `paymentMethodUniqueIdentifier`, `name`, `desc`, `pageText`, `tooltip`, `sort`, `public`, `isRecommended`, `pageShow`, `mutationId`, `vats`, `customFieldsJson`) VALUES
	(8, NULL, 'PluxeeCard', 'Benefitní kartou Pluxee', '', NULL, NULL, 0, 1, 0, 0, 1, '{"1":"standard"}', '{}');
INSERT INTO `payment_method` (`id`, `externalId`, `paymentMethodUniqueIdentifier`, `name`, `desc`, `pageText`, `tooltip`, `sort`, `public`, `isRecommended`, `pageShow`, `mutationId`, `vats`, `customFieldsJson`) VALUES
	(9, NULL, 'EdenredCard', 'Edenred benefit', '', NULL, NULL, 0, 1, 0, 0, 1, '{"1":"standard"}', '{}');
INSERT INTO `payment_method` (`id`, `externalId`, `paymentMethodUniqueIdentifier`, `name`, `desc`, `pageText`, `tooltip`, `sort`, `public`, `isRecommended`, `pageShow`, `mutationId`, `vats`, `customFieldsJson`) VALUES
	(10, NULL, 'Legacy', 'Importovaná platba z ERP', '', NULL, NULL, 0, 1, 0, 0, 1, '{"1":"standard"}', '{}');

INSERT INTO `payment_method_currency` (`id`, `paymentMethodId`, `currency`) VALUES
	(1, 2, 'CZK');
INSERT INTO `payment_method_currency` (`id`, `paymentMethodId`, `currency`) VALUES
	(2, 3, 'CZK');
INSERT INTO `payment_method_currency` (`id`, `paymentMethodId`, `currency`) VALUES
	(3, 1, 'CZK');
INSERT INTO `payment_method_currency` (`id`, `paymentMethodId`, `currency`) VALUES
	(4, 1, 'EUR');
INSERT INTO `payment_method_currency` (`id`, `paymentMethodId`, `currency`) VALUES
	(5, 4, 'CZK');
INSERT INTO `payment_method_currency` (`id`, `paymentMethodId`, `currency`) VALUES
	(6, 4, 'EUR');
INSERT INTO `payment_method_currency` (`id`, `paymentMethodId`, `currency`) VALUES
	(7, 5, 'CZK');
INSERT INTO `payment_method_currency` (`id`, `paymentMethodId`, `currency`) VALUES
	(8, 5, 'EUR');
INSERT INTO `payment_method_currency` (`id`, `paymentMethodId`, `currency`) VALUES
	(9, 6, 'CZK');
INSERT INTO `payment_method_currency` (`id`, `paymentMethodId`, `currency`) VALUES
	(10, 6, 'EUR');
INSERT INTO `payment_method_currency` (`id`, `paymentMethodId`, `currency`) VALUES
	(11, 7, 'CZK');
INSERT INTO `payment_method_currency` (`id`, `paymentMethodId`, `currency`) VALUES
	(12, 7, 'EUR');
INSERT INTO `payment_method_currency` (`id`, `paymentMethodId`, `currency`) VALUES
	(13, 8, 'CZK');
INSERT INTO `payment_method_currency` (`id`, `paymentMethodId`, `currency`) VALUES
	(14, 9, 'CZK');
INSERT INTO `payment_method_currency` (`id`, `paymentMethodId`, `currency`) VALUES
	(15, 10, 'CZK');

INSERT INTO `payment_method_price` (`id`, `externalId`, `paymentMethodId`, `priceLevelId`, `stateId`, `price_amount`, `price_currency`) VALUES
	(1, NULL, 1, 1, 1, 0.0000, 'CZK');
INSERT INTO `payment_method_price` (`id`, `externalId`, `paymentMethodId`, `priceLevelId`, `stateId`, `price_amount`, `price_currency`) VALUES
	(2, NULL, 2, 1, 1, 39.0000, 'CZK');
INSERT INTO `payment_method_price` (`id`, `externalId`, `paymentMethodId`, `priceLevelId`, `stateId`, `price_amount`, `price_currency`) VALUES
	(3, NULL, 3, 1, 1, 99.0000, 'CZK');
INSERT INTO `payment_method_price` (`id`, `externalId`, `paymentMethodId`, `priceLevelId`, `stateId`, `price_amount`, `price_currency`) VALUES
	(4, NULL, 4, 1, 1, 0.0000, 'CZK');
INSERT INTO `payment_method_price` (`id`, `externalId`, `paymentMethodId`, `priceLevelId`, `stateId`, `price_amount`, `price_currency`) VALUES
	(5, NULL, 6, 1, 1, 9.0000, 'CZK');
INSERT INTO `payment_method_price` (`id`, `externalId`, `paymentMethodId`, `priceLevelId`, `stateId`, `price_amount`, `price_currency`) VALUES
	(6, NULL, 7, 1, 1, 0.0000, 'CZK');
INSERT INTO `payment_method_price` (`id`, `externalId`, `paymentMethodId`, `priceLevelId`, `stateId`, `price_amount`, `price_currency`) VALUES
	(7, NULL, 8, 1, 1, 20.0000, 'CZK');
INSERT INTO `payment_method_price` (`id`, `externalId`, `paymentMethodId`, `priceLevelId`, `stateId`, `price_amount`, `price_currency`) VALUES
	(8, NULL, 9, 1, 1, 31.0000, 'CZK');
INSERT INTO `payment_method_price` (`id`, `externalId`, `paymentMethodId`, `priceLevelId`, `stateId`, `price_amount`, `price_currency`) VALUES
	(9, NULL, 10, 1, 1, 0.0000, 'CZK');



INSERT INTO `delivery_method_x_payment_method` (`deliveryMethodId`, `paymentMethodId`) VALUES
	(1, 1);
INSERT INTO `delivery_method_x_payment_method` (`deliveryMethodId`, `paymentMethodId`) VALUES
	(2, 1);
INSERT INTO `delivery_method_x_payment_method` (`deliveryMethodId`, `paymentMethodId`) VALUES
	(2, 2);
INSERT INTO `delivery_method_x_payment_method` (`deliveryMethodId`, `paymentMethodId`) VALUES
	(2, 4);
INSERT INTO `delivery_method_x_payment_method` (`deliveryMethodId`, `paymentMethodId`) VALUES
	(2, 3);
INSERT INTO `delivery_method_x_payment_method` (`deliveryMethodId`, `paymentMethodId`) VALUES
	(1, 2);
INSERT INTO `delivery_method_x_payment_method` (`deliveryMethodId`, `paymentMethodId`) VALUES
	(1, 4);
INSERT INTO `delivery_method_x_payment_method` (`deliveryMethodId`, `paymentMethodId`) VALUES
	(1, 3);
INSERT INTO `delivery_method_x_payment_method` (`deliveryMethodId`, `paymentMethodId`) VALUES
	(3, 1);
INSERT INTO `delivery_method_x_payment_method` (`deliveryMethodId`, `paymentMethodId`) VALUES
	(3, 2);
INSERT INTO `delivery_method_x_payment_method` (`deliveryMethodId`, `paymentMethodId`) VALUES
	(3, 4);
INSERT INTO `delivery_method_x_payment_method` (`deliveryMethodId`, `paymentMethodId`) VALUES
	(3, 3);
INSERT INTO `delivery_method_x_payment_method` (`deliveryMethodId`, `paymentMethodId`) VALUES
	(4, 1);
INSERT INTO `delivery_method_x_payment_method` (`deliveryMethodId`, `paymentMethodId`) VALUES
	(4, 2);
INSERT INTO `delivery_method_x_payment_method` (`deliveryMethodId`, `paymentMethodId`) VALUES
	(4, 4);
INSERT INTO `delivery_method_x_payment_method` (`deliveryMethodId`, `paymentMethodId`) VALUES
	(4, 3);
INSERT INTO `delivery_method_x_payment_method` (`deliveryMethodId`, `paymentMethodId`) VALUES
	(5, 1);
INSERT INTO `delivery_method_x_payment_method` (`deliveryMethodId`, `paymentMethodId`) VALUES
	(5, 2);
INSERT INTO `delivery_method_x_payment_method` (`deliveryMethodId`, `paymentMethodId`) VALUES
	(5, 4);
INSERT INTO `delivery_method_x_payment_method` (`deliveryMethodId`, `paymentMethodId`) VALUES
	(5, 3);
INSERT INTO `delivery_method_x_payment_method` (`deliveryMethodId`, `paymentMethodId`) VALUES
	(6, 1);
INSERT INTO `delivery_method_x_payment_method` (`deliveryMethodId`, `paymentMethodId`) VALUES
	(6, 2);
INSERT INTO `delivery_method_x_payment_method` (`deliveryMethodId`, `paymentMethodId`) VALUES
	(6, 4);
INSERT INTO `delivery_method_x_payment_method` (`deliveryMethodId`, `paymentMethodId`) VALUES
	(6, 3);
INSERT INTO `delivery_method_x_payment_method` (`deliveryMethodId`, `paymentMethodId`) VALUES
	(7, 1);
INSERT INTO `delivery_method_x_payment_method` (`deliveryMethodId`, `paymentMethodId`) VALUES
	(7, 2);
INSERT INTO `delivery_method_x_payment_method` (`deliveryMethodId`, `paymentMethodId`) VALUES
	(7, 4);
INSERT INTO `delivery_method_x_payment_method` (`deliveryMethodId`, `paymentMethodId`) VALUES
	(7, 3);
INSERT INTO `delivery_method_x_payment_method` (`deliveryMethodId`, `paymentMethodId`) VALUES
	(8, 1);
INSERT INTO `delivery_method_x_payment_method` (`deliveryMethodId`, `paymentMethodId`) VALUES
	(8, 4);
INSERT INTO `delivery_method_x_payment_method` (`deliveryMethodId`, `paymentMethodId`) VALUES
	(8, 3);
INSERT INTO `delivery_method_x_payment_method` (`deliveryMethodId`, `paymentMethodId`) VALUES
	(8, 6);
INSERT INTO `delivery_method_x_payment_method` (`deliveryMethodId`, `paymentMethodId`) VALUES
	(1, 7);
INSERT INTO `delivery_method_x_payment_method` (`deliveryMethodId`, `paymentMethodId`) VALUES
	(2, 7);
INSERT INTO `delivery_method_x_payment_method` (`deliveryMethodId`, `paymentMethodId`) VALUES
	(3, 7);
INSERT INTO `delivery_method_x_payment_method` (`deliveryMethodId`, `paymentMethodId`) VALUES
	(4, 7);
INSERT INTO `delivery_method_x_payment_method` (`deliveryMethodId`, `paymentMethodId`) VALUES
	(5, 7);
INSERT INTO `delivery_method_x_payment_method` (`deliveryMethodId`, `paymentMethodId`) VALUES
	(6, 7);
INSERT INTO `delivery_method_x_payment_method` (`deliveryMethodId`, `paymentMethodId`) VALUES
	(7, 7);
INSERT INTO `delivery_method_x_payment_method` (`deliveryMethodId`, `paymentMethodId`) VALUES
	(8, 7);
INSERT INTO `delivery_method_x_payment_method` (`deliveryMethodId`, `paymentMethodId`) VALUES
	(1, 8);
INSERT INTO `delivery_method_x_payment_method` (`deliveryMethodId`, `paymentMethodId`) VALUES
	(2, 8);
INSERT INTO `delivery_method_x_payment_method` (`deliveryMethodId`, `paymentMethodId`) VALUES
	(1, 9);
INSERT INTO `delivery_method_x_payment_method` (`deliveryMethodId`, `paymentMethodId`) VALUES
	(2, 9);
INSERT INTO `delivery_method_x_payment_method` (`deliveryMethodId`, `paymentMethodId`) VALUES
	(8, 8);
INSERT INTO `delivery_method_x_payment_method` (`deliveryMethodId`, `paymentMethodId`) VALUES
	(8, 9);
INSERT INTO `delivery_method_x_payment_method` (`deliveryMethodId`, `paymentMethodId`) VALUES
	(9, 1);
INSERT INTO `delivery_method_x_payment_method` (`deliveryMethodId`, `paymentMethodId`) VALUES
	(9, 4);

INSERT INTO `delivery_method_x_state` (`deliveryMethodId`, `stateId`) VALUES
	(1, 1);
INSERT INTO `delivery_method_x_state` (`deliveryMethodId`, `stateId`) VALUES
	(2, 1);
INSERT INTO `delivery_method_x_state` (`deliveryMethodId`, `stateId`) VALUES
	(3, 1);
INSERT INTO `delivery_method_x_state` (`deliveryMethodId`, `stateId`) VALUES
	(4, 1);
INSERT INTO `delivery_method_x_state` (`deliveryMethodId`, `stateId`) VALUES
	(5, 1);
INSERT INTO `delivery_method_x_state` (`deliveryMethodId`, `stateId`) VALUES
	(6, 1);
INSERT INTO `delivery_method_x_state` (`deliveryMethodId`, `stateId`) VALUES
	(7, 1);
INSERT INTO `delivery_method_x_state` (`deliveryMethodId`, `stateId`) VALUES
	(8, 1);
INSERT INTO `delivery_method_x_state` (`deliveryMethodId`, `stateId`) VALUES
	(9, 1);
