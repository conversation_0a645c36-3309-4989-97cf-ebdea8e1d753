insert into voucher (id, mutationId, synced, syncChecksum, name, internalName, type, minPriceOrder, reuse, combination, combinationType, public, publicTo, publicFrom, created, createdTime, edited, editedTime, discount_amount, discount_currency, discountPercent)
values  (1, 1, 0, null, 'test', 'test', 'amount', null, 1, 0, null, 1, '2125-01-23 10:28:00', '2025-01-23 10:28:00', 3, '2025-01-23 10:28:31', 3, '2025-01-23 10:38:32', 100.0000, 'CZK', null);

insert into voucher_code (id, code, isUsed, usedAt, createdTime, voucher)
values  (2, 'test3ELAQL', 0, null, '2025-01-23 10:38:32', 1);


