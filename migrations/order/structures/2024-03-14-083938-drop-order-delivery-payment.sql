-- Adminer 4.8.1 MySQL 10.6.12-MariaDB dump

SET NAMES utf8;
SET time_zone = '+00:00';
SET foreign_key_checks = 0;
SET sql_mode = 'NO_AUTO_VALUE_ON_ZERO';

SET NAMES utf8mb4;

DROP TABLE IF EXISTS `order`;
CREATE TABLE `order` (
						 `id` int(11) NOT NULL AUTO_INCREMENT,
						 `state` varchar(255) NOT NULL,
						 `orderNumber` varchar(255) DEFAULT NULL,
						 `userId` int(11) DEFAULT NULL,
						 `mutationId` int(11) NOT NULL,
						 `priceLevelId` int(11) NOT NULL,
						 `name` varchar(255) NOT NULL,
						 `street` varchar(255) NOT NULL,
						 `city` varchar(255) NOT NULL,
						 `zip` varchar(255) NOT NULL,
						 `countryId` int(11) NOT NULL,
						 `companyIdentifier` varchar(255) DEFAULT NULL,
						 `vatNumber` varchar(255) DEFAULT NULL,
						 `deliveryId` int(11) DEFAULT NULL,
						 `paymentId` int(11) DEFAULT NULL,
						 `currency` char(3) NOT NULL,
						 PRIMARY KEY (`id`),
						 UNIQUE KEY `orderNumber` (`orderNumber`,`mutationId`),
						 KEY `userId` (`userId`),
						 KEY `mutationId` (`mutationId`),
						 KEY `priceLevelId` (`priceLevelId`),
						 KEY `countryId` (`countryId`),
						 KEY `deliveryId` (`deliveryId`),
						 KEY `paymentId` (`paymentId`),
						 CONSTRAINT `order_ibfk_1` FOREIGN KEY (`userId`) REFERENCES `user` (`id`),
						 CONSTRAINT `order_ibfk_2` FOREIGN KEY (`mutationId`) REFERENCES `mutation` (`id`),
						 CONSTRAINT `order_ibfk_3` FOREIGN KEY (`priceLevelId`) REFERENCES `price_level` (`id`),
						 CONSTRAINT `order_ibfk_4` FOREIGN KEY (`countryId`) REFERENCES `state` (`id`),
						 CONSTRAINT `order_ibfk_7` FOREIGN KEY (`deliveryId`) REFERENCES `order_delivery` (`id`),
						 CONSTRAINT `order_ibfk_8` FOREIGN KEY (`paymentId`) REFERENCES `order_payment` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;


DROP TABLE IF EXISTS `order_delivery`;
CREATE TABLE `order_delivery` (
								  `id` int(11) NOT NULL AUTO_INCREMENT,
								  `deliveryMethodId` int(11) DEFAULT NULL,
								  `informationId` int(11) DEFAULT NULL,
								  `amount` int(11) NOT NULL DEFAULT 1,
								  `vatRate` varchar(255) NOT NULL,
								  `unitPrice_amount` decimal(18,4) NOT NULL,
								  `unitPrice_currency` char(3) NOT NULL,
								  PRIMARY KEY (`id`),
								  KEY `deliveryMethodId` (`deliveryMethodId`),
								  KEY `informationId` (`informationId`),
								  CONSTRAINT `order_delivery_ibfk_1` FOREIGN KEY (`deliveryMethodId`) REFERENCES `delivery_method` (`id`),
								  CONSTRAINT `order_delivery_ibfk_3` FOREIGN KEY (`informationId`) REFERENCES `order_delivery_information` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;


DROP TABLE IF EXISTS `order_delivery_information`;
CREATE TABLE `order_delivery_information` (
											  `id` int(11) NOT NULL AUTO_INCREMENT,
											  `type` varchar(255) NOT NULL,
											  `name` varchar(255) DEFAULT NULL,
											  `street` varchar(255) DEFAULT NULL,
											  `city` varchar(255) DEFAULT NULL,
											  `zip` varchar(255) DEFAULT NULL,
											  `countryId` int(11) DEFAULT NULL,
											  `phoneNumber` varchar(255) DEFAULT NULL,
											  `trackingCode` varchar(255) DEFAULT NULL,
											  `pickupPointId` varchar(255) DEFAULT NULL,
											  PRIMARY KEY (`id`),
											  KEY `countryId` (`countryId`),
											  CONSTRAINT `order_delivery_information_ibfk_1` FOREIGN KEY (`countryId`) REFERENCES `state` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;


DROP TABLE IF EXISTS `order_payment`;
CREATE TABLE `order_payment` (
								 `id` int(11) NOT NULL AUTO_INCREMENT,
								 `paymentMethodId` int(11) DEFAULT NULL,
								 `informationId` int(11) DEFAULT NULL,
								 `amount` int(11) NOT NULL DEFAULT 1,
								 `vatRate` varchar(255) NOT NULL,
								 `unitPrice_amount` decimal(18,4) NOT NULL,
								 `unitPrice_currency` char(3) NOT NULL,
								 PRIMARY KEY (`id`),
								 KEY `paymentMethodId` (`paymentMethodId`),
								 KEY `informationId` (`informationId`),
								 CONSTRAINT `order_payment_ibfk_1` FOREIGN KEY (`paymentMethodId`) REFERENCES `payment_method` (`id`),
								 CONSTRAINT `order_payment_ibfk_2` FOREIGN KEY (`informationId`) REFERENCES `order_payment_information` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;


DROP TABLE IF EXISTS `order_payment_information`;
CREATE TABLE `order_payment_information` (
											 `id` int(11) NOT NULL AUTO_INCREMENT,
											 `type` varchar(255) NOT NULL,
											 `state` varchar(255) NOT NULL,
											 `variableSymbol` varchar(255) DEFAULT NULL,
											 `dueDate` date DEFAULT NULL,
											 PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;


-- 2024-03-14 07:40:29


ALTER TABLE `order_delivery`
	ADD `dateDeliveryFrom` date NULL,
ADD `dateDeliveryTo` date NULL AFTER `dateDeliveryFrom`,
ADD `dateExpedition` date NULL AFTER `dateDeliveryTo`;

ALTER TABLE `order`
	ADD `phone` varchar(255) COLLATE 'utf8mb4_general_ci' NOT NULL AFTER `zip`,
	ADD `note` text COLLATE 'utf8mb4_general_ci' NOT NULL AFTER `phone`,
	ADD `companyName` varchar(255) COLLATE 'utf8mb4_general_ci' NULL AFTER `countryId`,
	ADD `hash` varchar(128)  COLLATE 'utf8mb4_general_ci' NULL AFTER `id`,
	ADD `placedAt` datetime NULL AFTER `state`,
	ADD `email` varchar(255) COLLATE 'utf8mb4_general_ci' NOT NULL AFTER `priceLevelId`;
ALTER TABLE `order`
	ADD UNIQUE `hash` (`hash`);

ALTER TABLE `order_delivery_information`
	ADD `company` varchar(255) COLLATE 'utf8mb4_general_ci' NULL AFTER `name`;
