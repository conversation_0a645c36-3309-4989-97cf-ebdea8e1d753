-- Adminer 4.8.1 MySQL 10.6.12-MariaDB dump

SET NAMES utf8;
SET time_zone = '+00:00';
SET foreign_key_checks = 0;
SET sql_mode = 'NO_AUTO_VALUE_ON_ZERO';

SET NAMES utf8mb4;

DROP TABLE IF EXISTS `order_gift`;
CREATE TABLE `order_gift` (
							  `id` int(11) NOT NULL AUTO_INCREMENT,
							  `orderId` int(11) NOT NULL,
							  `amount` int(11) NOT NULL,
							  `unitPrice_amount` decimal(18,4) NOT NULL,
							  `unitPrice_currency` char(3) NOT NULL,
							  `vatRate` varchar(255) NOT NULL,
							  `giftLocalizationId` int(11) NOT NULL,
							  PRIMARY KEY (`id`),
							  KEY `orderId` (`orderId`),
							  KEY `giftLocalizationId` (`giftLocalizationId`),
							  CONSTRAINT `order_gift_ibfk_1` FOREIG<PERSON> KEY (`orderId`) REFERENCES `order` (`id`),
							  CONSTRAINT `order_gift_ibfk_2` <PERSON>OREI<PERSON>N KEY (`giftLocalizationId`) REFERENCES `gift_localization` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;


-- 2024-05-21 13:10:31
