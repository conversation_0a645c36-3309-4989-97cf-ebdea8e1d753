ALTER TABLE `payment_method`
	ADD `externalId` int(11) NULL AFTER `id`,
ADD `name` varchar(255) COLLATE 'utf8mb4_general_ci' NOT NULL AFTER `paymentMethodUniqueIdentifier`,
ADD `desc` text COLLATE 'utf8mb4_general_ci' NOT NULL AFTER `name`,
ADD `tooltip` text COLLATE 'utf8mb4_general_ci' NULL AFTER `desc`,
ADD `sort` int NOT NULL AFTER `tooltip`,
ADD `public` tinyint(1) NOT NULL AFTER `sort`,
ADD `isRecommended` tinyint(1) NOT NULL AFTER `public`;

ALTER TABLE `payment_method`
	ADD `customFieldsJson` longtext COLLATE 'utf8mb4_general_ci' NULL;
