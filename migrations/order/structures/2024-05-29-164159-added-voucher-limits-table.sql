-- Adminer 4.8.1 MySQL 10.6.12-MariaDB dump

SET NAMES utf8;
SET time_zone = '+00:00';
SET foreign_key_checks = 0;
SET sql_mode = 'NO_AUTO_VALUE_ON_ZERO';

SET NAMES utf8mb4;

DROP TABLE IF EXISTS `voucher_limit`;
CREATE TABLE `voucher_limit` (
								 `id` int(11) NOT NULL AUTO_INCREMENT,
								 `voucherId` int(11) NOT NULL,
								 `isNegated` tinyint(1) NOT NULL DEFAULT 0,
								 `limit` longtext DEFAULT NULL,
								 PRIMARY KEY (`id`),
								 KEY `voucherId` (`voucherId`),
								 CONSTRAINT `voucher_limit_ibfk_1` FOREIGN KEY (`voucherId`) REFERENCES `voucher` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci;


-- 2024-05-29 14:41:33
