
ALTER TABLE `delivery_method`
	ADD `calculateFree` tinyint(1) NOT NULL AFTER `isRecommended`;
ALTER TABLE `delivery_method`
	ADD `isGift` tinyint(1) NOT NULL AFTER `isRecommended`;

SET NAMES utf8;
SET time_zone = '+00:00';
SET foreign_key_checks = 0;
SET sql_mode = 'NO_AUTO_VALUE_ON_ZERO';

SET NAMES utf8mb4;

DROP TABLE IF EXISTS `delivery_method_currency`;
CREATE TABLE `delivery_method_currency` (
											`id` int(11) NOT NULL AUTO_INCREMENT,
											`deliveryMethodId` int(11) NOT NULL,
											`currency` char(3) NOT NULL,
											PRIMARY KEY (`id`),
											KEY `deliveryMethodId` (`deliveryMethodId`),
											CONSTRAINT `delivery_method_currency_ibfk_1` FOREIGN KEY (`deliveryMethodId`) REFERENCES `delivery_method` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci;



DROP TABLE IF EXISTS `payment_method_currency`;
CREATE TABLE `payment_method_currency` (
										   `id` int(11) NOT NULL AUTO_INCREMENT,
										   `paymentMethodId` int(11) NOT NULL,
										   `currency` char(3) NOT NULL,
										   PRIMARY KEY (`id`),
										   KEY `paymentMethodId` (`paymentMethodId`),
										   CONSTRAINT `payment_method_currency_ibfk_1` FOREIGN KEY (`paymentMethodId`) REFERENCES `payment_method` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci;
