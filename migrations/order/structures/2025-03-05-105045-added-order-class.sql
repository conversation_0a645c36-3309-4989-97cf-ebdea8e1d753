SET @precartId = (SELECT id FROM tree WHERE uid = 'precart');
UPDATE `tree` SET `name` = 'Úspěšně vloženo do košíku', `nameAnchor` = 'Úspěšně vloženo do košíku', `nameTitle` = 'Úspěšně vloženo do košíku' WHERE `id` = @precartId;
UPDATE `alias` SET `alias` = 'vlozeno-do-kosiku' WHERE `referenceId` = @precartId AND module='tree' AND `mutationId` = 1;

-- Adminer 4.8.1 MySQL 10.6.12-MariaDB dump

SET NAMES utf8;
SET time_zone = '+00:00';
SET foreign_key_checks = 0;
SET sql_mode = 'NO_AUTO_VALUE_ON_ZERO';

SET NAMES utf8mb4;

DROP TABLE IF EXISTS `order_class`;
CREATE TABLE `order_class` (
							   `id` int(11) NOT NULL AUTO_INCREMENT,
							   `orderId` int(11) NOT NULL,
							   `amount` int(11) NOT NULL,
							   `unitPrice_amount` decimal(18,4) NOT NULL,
							   `unitPrice_currency` char(3) DEFAULT NULL,
							   `vatRate` varchar(255) DEFAULT NULL,
							   `vatRateValue` decimal(10,4) DEFAULT NULL,
							   `classEventId` int(11) DEFAULT NULL,
							   `productName` varchar(255) DEFAULT NULL,
							   `classEventName` varchar(255) DEFAULT NULL,
							   `classEventDateFrom` datetime DEFAULT NULL,
							   `classEventDateTo` datetime DEFAULT NULL,
							   `classEventDate` varchar(255) DEFAULT NULL,
							   `classEventTime` varchar(255) DEFAULT NULL,
							   `availableCapacity` int(11) DEFAULT NULL,
							   PRIMARY KEY (`id`),
							   KEY `orderId` (`orderId`),
							   KEY `classEventId` (`classEventId`),
							   CONSTRAINT `order_class_ibfk_1` FOREIGN KEY (`orderId`) REFERENCES `order` (`id`) ON DELETE CASCADE,
							   CONSTRAINT `order_class_ibfk_2` FOREIGN KEY (`classEventId`) REFERENCES `class_event` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci;


-- 2025-03-05 09:54:59

ALTER TABLE `class_event_x_user`
	COLLATE 'utf8mb4_unicode_520_ci';
