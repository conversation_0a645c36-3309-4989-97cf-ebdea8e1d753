SET NAMES utf8;
SET time_zone = '+00:00';
SET foreign_key_checks = 0;
SET sql_mode = 'NO_AUTO_VALUE_ON_ZERO';

SET NAMES utf8mb4;

DROP TABLE IF EXISTS `order_voucher`;
CREATE TABLE `order_voucher` (
								 `id` int(11) NOT NULL AUTO_INCREMENT,
								 `orderId` int(11) NOT NULL,
								 `amount` int(11) NOT NULL,
								 `unitPrice_amount` decimal(18,4) NOT NULL,
								 `unitPrice_currency` char(3) NOT NULL,
								 `vatRate` varchar(255) NOT NULL,
								 `voucherCodeId` int(11) NOT NULL,
								 PRIMARY KEY (`id`),
								 KEY `orderId` (`orderId`),
								 KEY `voucherCodeId` (`voucherCodeId`),
								 CONSTRAINT `order_voucher_ibfk_1` FOREIGN KEY (`orderId`) REFERENCES `order` (`id`),
								 CONSTRAINT `order_voucher_ibfk_3` FOREIGN KEY (`voucherCodeId`) REFERENCES `voucher_code` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;


DROP TABLE IF EXISTS `voucher`;
CREATE TABLE `voucher` (
						   `id` int(11) NOT NULL AUTO_INCREMENT,
						   `mutationId` int(11) NOT NULL,
						   `name` varchar(255) NOT NULL,
						   `internalName` varchar(255) NOT NULL,
						   `type` varchar(20) NOT NULL,
						   `minPriceOrder` decimal(18,4) DEFAULT NULL,
						   `reuse` int(11) NOT NULL DEFAULT 0,
						   `public` int(11) NOT NULL DEFAULT 0,
						   `publicTo` datetime DEFAULT NULL,
						   `publicFrom` datetime DEFAULT NULL,
						   `created` int(11) DEFAULT NULL,
						   `createdTime` datetime DEFAULT NULL,
						   `edited` int(11) DEFAULT NULL,
						   `editedTime` datetime DEFAULT NULL,
						   `discount_amount` decimal(18,4) DEFAULT NULL,
						   `discount_currency` char(3) DEFAULT NULL,
						   `discountPercent` int(11) DEFAULT NULL,
						   PRIMARY KEY (`id`),
						   KEY `mutationId` (`mutationId`),
						   CONSTRAINT `voucher_ibfk_1` FOREIGN KEY (`mutationId`) REFERENCES `mutation` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;


DROP TABLE IF EXISTS `voucher_code`;
CREATE TABLE `voucher_code` (
								`id` int(11) NOT NULL AUTO_INCREMENT,
								`code` varchar(255) NOT NULL,
								`isUsed` int(11) NOT NULL DEFAULT 0,
								`usedAt` datetime DEFAULT NULL,
								`createdTime` datetime NOT NULL,
								`voucher` int(11) NOT NULL,
								PRIMARY KEY (`id`),
								KEY `voucher` (`voucher`),
								CONSTRAINT `voucher_code_ibfk_1` FOREIGN KEY (`voucher`) REFERENCES `voucher` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci;


ALTER TABLE `voucher`
	ADD `combination` int(11) NOT NULL DEFAULT '0' AFTER `reuse`,
	ADD `combinationType` varchar(20) NULL AFTER `combination`;
