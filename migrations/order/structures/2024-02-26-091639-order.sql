-- delivery

CREATE TABLE `delivery_method` (
	`id` int(11) NOT NULL AUTO_INCREMENT,
	`deliveryMethodUniqueIdentifier` VARCHAR(255) NOT NULL,
	`mutationId` int(11) NOT NULL,
	`vats` TEXT NOT NULL,
	PRIMARY KEY (`id`),
	FOREIGN KEY (`mutationId`) REFERENCES `mutation` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4;

CREATE TABLE `delivery_method_price` (
	`id` int(11) NOT NULL AUTO_INCREMENT,
	`deliveryMethodId` int(11) NOT NULL,
	`priceLevelId` int(11) NOT NULL,
	`stateId` int(11) NOT NULL,
	`price_amount` decimal(18, 4) NOT NULL,
	`price_currency` char(3) NOT NULL,
	PRIMARY KEY (`id`),
	<PERSON>OR<PERSON><PERSON><PERSON> KEY (`deliveryMethodId`) REFERENCES `delivery_method` (`id`),
	FOREIG<PERSON> KEY (`priceLevelId`) REFERENCES `price_level` (`id`),
	FOREIG<PERSON> KEY (`stateId`) REFERENCES `state` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4;

CREATE TABLE `delivery_method_x_state` (
	`deliveryMethodId` int(11) NOT NULL,
	`stateId` int(11) NOT NULL,
	PRIMARY KEY (`deliveryMethodId`, `stateId`),
	FOREIGN KEY (`deliveryMethodId`) REFERENCES `delivery_method` (`id`),
	FOREIGN KEY (`stateId`) REFERENCES `state` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4;

CREATE TABLE `order_delivery_information` (
	`id` int(11) NOT NULL,
	`type` varchar(255) NOT NULL,
	`name` varchar(255) DEFAULT NULL,
	`street` varchar(255) DEFAULT NULL,
	`city` varchar(255) DEFAULT NULL,
	`zip` varchar(255) DEFAULT NULL,
	`countryId` int(11) DEFAULT NULL,
	`phoneNumber` varchar(255) DEFAULT NULL,
	`trackingCode` varchar(255) DEFAULT NULL,
	`pickupPointId` varchar(255) DEFAULT NULL,
	PRIMARY KEY (`id`),
	FOREIGN KEY (`countryId`) REFERENCES `state` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4;

CREATE TABLE `order_delivery` (
	`id` int(11) NOT NULL,
	`deliveryMethodId` int(11) NOT NULL,
	`informationId` int(11) NOT NULL,
	PRIMARY KEY (`id`),
	FOREIGN KEY (`deliveryMethodId`) REFERENCES `delivery_method` (`id`),
	FOREIGN KEY (`informationId`) REFERENCES `order_delivery_information` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4;


-- payment

CREATE TABLE `payment_method` (
	`id` int(11) NOT NULL AUTO_INCREMENT,
	`paymentMethodUniqueIdentifier` VARCHAR(255) NOT NULL,
	`mutationId` int(11) NOT NULL,
	`vats` TEXT NOT NULL,
	PRIMARY KEY (`id`),
	FOREIGN KEY (`mutationId`) REFERENCES `mutation` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4;

CREATE TABLE `payment_method_price` (
	`id` int(11) NOT NULL AUTO_INCREMENT,
	`paymentMethodId` int(11) NOT NULL,
	`priceLevelId` int(11) NOT NULL,
	`stateId` int(11) NOT NULL,
	`price_amount` decimal(18, 4) NOT NULL,
	`price_currency` char(3) NOT NULL,
	PRIMARY KEY (`id`),
	FOREIGN KEY (`paymentMethodId`) REFERENCES `payment_method` (`id`),
	FOREIGN KEY (`priceLevelId`) REFERENCES `price_level` (`id`),
FOREIGN KEY (`stateId`) REFERENCES `state` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4;

CREATE TABLE `payment_method_x_state` (
	`paymentMethodId` int(11) NOT NULL,
	`stateId` int(11) NOT NULL,
	PRIMARY KEY (`paymentMethodId`, `stateId`),
	FOREIGN KEY (`paymentMethodId`) REFERENCES `payment_method` (`id`),
	FOREIGN KEY (`stateId`) REFERENCES `state` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4;

CREATE TABLE `order_payment_information` (
	`id` int(11) NOT NULL,
	`type` varchar(255) NOT NULL,
	`state` varchar(255) NOT NULL,
	`variableSymbol` varchar(255) DEFAULT NULL,
	`dueDate` date DEFAULT NULL,
	PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4;

CREATE TABLE `order_payment` (
	`id` int(11) NOT NULL,
	`paymentMethodId` int(11) NOT NULL,
	`informationId` int(11) NOT NULL,
	PRIMARY KEY (`id`),
	FOREIGN KEY (`paymentMethodId`) REFERENCES `payment_method` (`id`),
	FOREIGN KEY (`informationId`) REFERENCES `order_payment_information` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4;


-- order

CREATE TABLE `order` (
	`id` int(11) NOT NULL AUTO_INCREMENT,
	`state` varchar(255) NOT NULL,
	`orderNumber` varchar(255),
	`userId` int(11),
	`mutationId` int(11) NOT NULL,
	`priceLevelId` int(11) NOT NULL,
	`name` varchar(255) NOT NULL,
	`street` varchar(255) NOT NULL,
	`city` varchar(255) NOT NULL,
	`zip` varchar(255) NOT NULL,
	`countryId` int(11) NOT NULL,
	`companyIdentifier` varchar(255) DEFAULT NULL,
	`vatNumber` varchar(255) DEFAULT NULL,
	`deliveryId` int(11) DEFAULT NULL,
	`paymentId` int(11) DEFAULT NULL,
	PRIMARY KEY (`id`),
	FOREIGN KEY (`userId`) REFERENCES `user` (`id`),
	FOREIGN KEY (`mutationId`) REFERENCES `mutation` (`id`),
	FOREIGN KEY (`priceLevelId`) REFERENCES `price_level` (`id`),
	FOREIGN KEY (`countryId`) REFERENCES `state` (`id`),
	FOREIGN KEY (`deliveryId`) REFERENCES `order_delivery` (`id`),
	FOREIGN KEY (`paymentId`) REFERENCES `order_payment` (`id`),
	UNIQUE (`orderNumber`, `mutationId`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4;


-- order product

CREATE TABLE `order_product` (
	`id` int(11) NOT NULL AUTO_INCREMENT,
	`orderId` int(11) NOT NULL,
	`amount` int(11) NOT NULL,
	`unitPrice_amount` decimal(18, 4) NOT NULL,
	`unitPrice_currency` char(3) NOT NULL,
	`vatRate` varchar(255) NOT NULL,
	`variantId` int(11) NOT NULL,
	PRIMARY KEY (`id`),
	FOREIGN KEY (`orderId`) REFERENCES `order` (`id`),
	FOREIGN KEY (`variantId`) REFERENCES `product_variant` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4;


-- discount

CREATE TABLE `order_discount` (
	`id` int(11) NOT NULL AUTO_INCREMENT,
	`orderId` int(11) NOT NULL,
	`amount` int(11) NOT NULL,
	`unitPrice_amount` decimal(18, 4) NOT NULL,
	`unitPrice_currency` char(3) NOT NULL,
	`vatRate` varchar(255) NOT NULL,
	`discountTypeUniqueIdentifier` varchar(255) NOT NULL,
	PRIMARY KEY (`id`),
	FOREIGN KEY (`orderId`) REFERENCES `order` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4;


-- voucher

CREATE TABLE `voucher` (
	`id` int(11) NOT NULL AUTO_INCREMENT,
	`code` varchar(255) NOT NULL UNIQUE,
	`mutationId` int(11) NOT NULL,
	`maxNumberOfUses` int(11) NOT NULL,
	`discount_amount` decimal(18, 4) NOT NULL,
	`discount_currency` char(3) NOT NULL,
	PRIMARY KEY (`id`),
	FOREIGN KEY (`mutationId`) REFERENCES `mutation` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4;

CREATE TABLE `order_voucher` (
	`id` int(11) NOT NULL AUTO_INCREMENT,
	`orderId` int(11) NOT NULL,
	`amount` int(11) NOT NULL,
	`unitPrice_amount` decimal(18, 4) NOT NULL,
	`unitPrice_currency` char(3) NOT NULL,
	`vatRate` varchar(255) NOT NULL,
	`voucherId` int(11) NOT NULL,
	PRIMARY KEY (`id`),
	FOREIGN KEY (`orderId`) REFERENCES `order` (`id`),
	FOREIGN KEY (`voucherId`) REFERENCES `voucher` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4;
