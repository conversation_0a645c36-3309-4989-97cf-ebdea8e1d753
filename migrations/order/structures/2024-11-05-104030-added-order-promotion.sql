-- Adminer 4.8.1 MySQL 10.6.12-MariaDB dump

SET NAMES utf8;
SET time_zone = '+00:00';
SET foreign_key_checks = 0;
SET sql_mode = 'NO_AUTO_VALUE_ON_ZERO';

SET NAMES utf8mb4;

DROP TABLE IF EXISTS `order_promotion`;
CREATE TABLE `order_promotion` (
								   `id` int(11) NOT NULL AUTO_INCREMENT,
								   `orderId` int(11) NOT NULL,
								   `amount` int(11) NOT NULL,
								   `unitPrice_amount` decimal(18,4) NOT NULL,
								   `unitPrice_currency` char(3) NOT NULL,
								   `vatRate` varchar(255) NOT NULL,
								   `vatRateValue` decimal(10,4) DEFAULT NULL,
								   `promotionLocalizationId` int(11) DEFAULT NULL,
								   `promotionName` varchar(255) DEFAULT NULL,
								   `promotionInfo` text DEFAULT NULL,
								   `promotionUniqId` varchar(128) DEFAULT NULL,
								   PRIMARY KEY (`id`),
								   <PERSON><PERSON><PERSON> `orderId` (`orderId`),
								   <PERSON>EY `promotionLocalizationId` (`promotionLocalizationId`),
								   CONSTRAINT `order_promotion_ibfk_1` FOREIGN KEY (`orderId`) REFERENCES `order` (`id`),
								   CONSTRAINT `order_promotion_ibfk_2` FOREIGN KEY (`promotionLocalizationId`) REFERENCES `promotion_localization` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;


-- 2024-11-05 09:23:55

ALTER TABLE `promotion`
	ADD `discountType` varchar(50) COLLATE 'utf8mb3_bin' NOT NULL DEFAULT 'percent' AFTER `type`;
