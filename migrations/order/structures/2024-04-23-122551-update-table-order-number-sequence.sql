-- Adminer 4.8.1 MySQL 10.6.12-MariaDB dump

SET NAMES utf8;
SET time_zone = '+00:00';
SET foreign_key_checks = 0;
SET sql_mode = 'NO_AUTO_VALUE_ON_ZERO';

SET NAMES utf8mb4;

DROP TABLE IF EXISTS `order_number_sequence`;
CREATE TABLE `order_number_sequence` (
										 `mutationId` int(11) NOT NULL,
										 `prefix` tinyint(1) NOT NULL,
										 `year` year(4) NOT NULL,
										 `month` tinyint(2) NOT NULL,
										 `day` tinyint(2) NOT NULL,
										 `number` bigint(20) unsigned NOT NULL,
										 `sufix` tinyint(2) unsigned NOT NULL,
										 PRIMARY KEY (`mutationId`,`year`,`month`,`day`,`prefix`,`sufix`),
										 CONSTRAINT `order_number_sequence_ibfk_1` FOREIGN KEY (`mutationId`) REFERENCES `mutation` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci;


-- 2024-04-23 10:25:14
