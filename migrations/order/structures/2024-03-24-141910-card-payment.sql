CREATE TABLE `card_payment` (
	`id` int NOT NULL AUTO_INCREMENT PRIMARY KEY,
	`cardPaymentInformationId` int NOT NULL,
	`paymentGatewayUniqueIdentifier` varchar(255) NOT NULL,
	`externalId` varchar(255) NOT NULL,
	`externalUrl` varchar(255) NULL,
	`status` varchar(255) NOT NULL,
	UNIQUE KEY (`paymentGatewayUniqueIdentifier`, `externalId`),
	FOREIGN KEY (`cardPaymentInformationId`) REFERENCES `order_payment_information` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4;

CREATE TABLE `card_payment_status_change` (
	`id` int NOT NULL AUTO_INCREMENT PRIMARY KEY,
	`paymentId` int NOT NULL,
	`changedAt` datetime NOT NULL,
	`from` varchar(255) NULL,
	`to` varchar(255) NOT NULL,
	FOREI<PERSON><PERSON> KEY (`paymentId`) REFERENCES `card_payment` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4;
