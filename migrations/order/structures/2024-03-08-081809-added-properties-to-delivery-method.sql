ALTER TABLE `delivery_method`
	ADD `externalId` varchar(50) NULL AFTER `id`,
ADD `name` varchar(255) COLLATE 'utf8mb4_general_ci' NOT NULL AFTER `deliveryMethodUniqueIdentifier`,
ADD `desc` varchar(255) COLLATE 'utf8mb4_general_ci' NOT NULL AFTER `name`,
ADD `tooltip` varchar(255) COLLATE 'utf8mb4_general_ci' NULL AFTER `desc`,
ADD `sort` int NOT NULL AFTER `tooltip`,
	ADD `public` tinyint(1) NOT NULL AFTER `sort`,
ADD `isRecommended` tinyint(1) NOT NULL AFTER `public`,
ADD `deliveryDayFrom` int NOT NULL AFTER `isRecommended`,
ADD `deliveryDayTo` int NULL AFTER `deliveryDayFrom`,
ADD `deliveryHourByStock` text COLLATE 'utf8mb4_general_ci' NOT NULL AFTER `deliveryDayTo`;


ALTER TABLE `delivery_method_price`
	ADD `freeFrom` decimal(18,4) NULL,
ADD `maxWeight` int NULL AFTER `freeFrom`,
ADD `maxCodPrice` int NULL AFTER `maxWeight`;

ALTER TABLE `delivery_method`
	ADD `customFieldsJson` longtext COLLATE 'utf8mb4_general_ci' NULL;


CREATE TABLE `delivery_method_x_payment_method` (
													`deliveryMethodId` int(11) NOT NULL,
													`paymentMethodId` int(11) NOT NULL,
													FOREIGN KEY (`deliveryMethodId`) REFERENCES `delivery_method` (`id`),
													FOREIGN KEY (`paymentMethodId`) REFERENCES `payment_method` (`id`)
) ENGINE='InnoDB';
