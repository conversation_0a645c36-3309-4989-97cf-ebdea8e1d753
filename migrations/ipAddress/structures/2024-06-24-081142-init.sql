create table ip_address
(
	id        int auto_increment,
	ipAddress varchar(45) not null,
	countryId int         not null,
	constraint ip_address_pk
		primary key (id),
	constraint ip_address_state_id_fk
		foreign key (countryId) references state (id)
) ENGINE = InnoDB
	AUTO_INCREMENT = 1
	DEFAULT CHARSET = utf8
	COLLATE = utf8_bin;


alter table ip_address
	add isInEurope tinyint(1) default 0 not null;

