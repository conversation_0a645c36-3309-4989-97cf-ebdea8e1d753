/** tree entry point */
INSERT INTO `tree` (`id`, `rootId`, `parentId`, `level`, `path`, `sort`, `last`, `public`, `uid`, `created`, `createdTime`, `createdTimeOrder`, `edited`, `editedTime`, `template`, `type`, `publicFrom`, `publicTo`, `name`, `nameTitle`, `nameAnchor`, `annotation`, `description`, `keywords`, `content`, `hideFirstImage`, `links`, `seoTitleFilter`, `seoAnnotationFilter`, `seoDescriptionFilter`, `videos`, `hideCrossroad`, `hideInSitemap`, `forceNoIndex`, `hideInSearch`, `showContactForm`, `showOnHomepage`, `customFieldsJson`, `customContentJson`, `customContentSchemeJson`, `productAttachedId`, `hasLinkedCategories`)  VALUES (446, 1, 1, 1, '1|', 1, 0, 1, 'blog', 0, '2021-08-16 16:41:04', '2021-08-16 16:41:04', 3, '2021-08-16 16:41:52', 'Blog:default', 'common', '2021-08-16 16:41:04', '2121-08-16 16:41:04', 'Blog', 'Blog', 'Blog', '', '', '', '', 0, '', NULL, NULL, NULL, '', 0, 0, 0, 0, 0, 0, '{}', NULL, NULL, NULL, NULL);
INSERT INTO `alias` (`id`, `alias`, `module`, `referenceId`, `mutationId`) VALUES (2199, 'blog', 'tree', 446, 1);
