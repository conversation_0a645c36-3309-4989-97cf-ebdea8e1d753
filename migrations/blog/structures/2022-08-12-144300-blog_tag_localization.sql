UPDATE `alias` SET `module`='blogTagLocalization' WHERE  `module`='blogTag';

RENAME TABLE `blog_tag` TO `blog_tag_localization`;

CREATE TABLE `blog_tag` (
						  `id` INT(11) NOT NULL AUTO_INCREMENT,
						  `internalName` VARCHAR(50) NOT NULL DEFAULT '' COLLATE 'utf8_czech_ci',
						  `customFieldsJson` LONGTEXT NULL DEFAULT NULL COLLATE 'utf8_bin',
						  PRIMARY KEY (`id`) USING BTREE
)
	COLLATE='utf8_bin'
ENGINE=InnoDB
AUTO_INCREMENT=1
;

ALTER TABLE `blog_tag_localization`
	ADD COLUMN `blogTagId` INT(11) NOT NULL AFTER `id`;
