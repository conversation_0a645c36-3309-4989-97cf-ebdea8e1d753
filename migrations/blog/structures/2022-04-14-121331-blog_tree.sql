CREATE TABLE `blog_localization_x_tree` (
	`blogId` INT(11) NOT NULL,
	`treeId` INT(11) NOT NULL,
	PRIMARY KEY (`blogId`, `treeId`) USING BTREE,
	INDEX `FK_blog_x_tree_blog` (`blogId`) USING BTREE,
	INDEX `FK_blog_x_tree_tree` (`treeId`) USING BTREE,
	CONSTRAINT `FK_blog_x_tree_blog` FOREIGN KEY (`blogId`) REFERENCES `blog_localization` (`id`) ON UPDATE CASCADE ON DELETE CASCADE,
	CONSTRAINT `FK_blog_x_tree_tree` FOREIGN KEY (`treeId`) REFERENCES `tree` (`id`) ON UPDATE CASCADE ON DELETE CASCADE
)
	COLLATE='utf8_bin'
ENGINE=InnoDB
;
