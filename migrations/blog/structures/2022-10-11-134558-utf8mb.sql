ALTER TABLE `blog`
	<PERSON>ANGE COLUMN `internalName` `internalName` VARCHAR(50) NOT NULL DEFAULT '' COLLATE 'utf8mb4_unicode_520_ci' AFTER `id`,
	<PERSON><PERSON><PERSON> COLUMN `customFieldsJson` `customFieldsJson` LONGTEXT NULL DEFAULT NULL COLLATE 'utf8mb4_unicode_520_ci' AFTER `internalName`;

ALTER TABLE `blog_localization`
	CHANGE COLUMN `name` `name` VARCHAR(250) NOT NULL DEFAULT '' COLLATE 'utf8mb4_unicode_520_ci' AFTER `blogId`,
	<PERSON>ANGE COLUMN `nameAnchor` `nameAnchor` VARCHAR(250) NOT NULL DEFAULT '' COLLATE 'utf8mb4_unicode_520_ci' AFTER `name`,
	CHANGE COLUMN `nameTitle` `nameTitle` VARCHAR(250) NOT NULL DEFAULT '' COLLATE 'utf8mb4_unicode_520_ci' AFTER `nameAnchor`,
	<PERSON>AN<PERSON> COLUMN `description` `description` TEXT NOT NULL COLLATE 'utf8mb4_unicode_520_ci' AFTER `nameTitle`,
	CHANGE COLUMN `keywords` `keywords` TEXT NOT NULL COLLATE 'utf8mb4_unicode_520_ci' AFTER `description`,
	CHANGE COLUMN `customFieldsJson` `customFieldsJson` LONGTEXT NULL DEFAULT NULL COLLATE 'utf8mb4_unicode_520_ci' AFTER `editedTime`,
	CHANGE COLUMN `customContentSchemeJson` `customContentSchemeJson` LONGTEXT NULL DEFAULT NULL COLLATE 'utf8mb4_unicode_520_ci' AFTER `customFieldsJson`,
	CHANGE COLUMN `customContentJson` `customContentJson` LONGTEXT NULL DEFAULT NULL COLLATE 'utf8mb4_unicode_520_ci' AFTER `customContentSchemeJson`;


ALTER TABLE `blog_tag`
	CHANGE COLUMN `internalName` `internalName` VARCHAR(50) NOT NULL DEFAULT '' COLLATE 'utf8mb4_unicode_520_ci' AFTER `id`,
	CHANGE COLUMN `customFieldsJson` `customFieldsJson` LONGTEXT NULL DEFAULT NULL COLLATE 'utf8mb4_unicode_520_ci' AFTER `internalName`;

ALTER TABLE `blog_tag_localization`
	CHANGE COLUMN `name` `name` VARCHAR(250) NOT NULL DEFAULT '' COLLATE 'utf8mb4_unicode_520_ci' AFTER `mutationId`,
	CHANGE COLUMN `nameAnchor` `nameAnchor` VARCHAR(250) NOT NULL DEFAULT '' COLLATE 'utf8mb4_unicode_520_ci' AFTER `sort`,
	CHANGE COLUMN `nameTitle` `nameTitle` VARCHAR(250) NOT NULL DEFAULT '' COLLATE 'utf8mb4_unicode_520_ci' AFTER `nameAnchor`,
	CHANGE COLUMN `description` `description` TEXT NOT NULL COLLATE 'utf8mb4_unicode_520_ci' AFTER `nameTitle`,
	CHANGE COLUMN `keywords` `keywords` TEXT NOT NULL COLLATE 'utf8mb4_unicode_520_ci' AFTER `description`,
	CHANGE COLUMN `customFieldsJson` `customFieldsJson` LONGTEXT NULL DEFAULT NULL COLLATE 'utf8mb4_unicode_520_ci' AFTER `editedTime`,
	CHANGE COLUMN `customContentJson` `customContentJson` LONGTEXT NULL DEFAULT NULL COLLATE 'utf8mb4_unicode_520_ci' AFTER `customFieldsJson`;



