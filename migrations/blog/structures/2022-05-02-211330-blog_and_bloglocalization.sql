CREATE TABLE `blog_localization` (
	 `id` INT(11) NOT NULL AUTO_INCREMENT,
	 `mutationId` INT(11) NOT NULL DEFAULT '1',
	 `blogId` INT(11) NOT NULL,
	 `name` VARCHAR(250) NULL DEFAULT NULL COLLATE 'utf8_bin',
	 `nameAnchor` VARCHAR(250) NULL DEFAULT NULL COLLATE 'utf8_bin',
	 `nameTitle` VARCHAR(250) NULL DEFAULT NULL COLLATE 'utf8_bin',
	 `pathString` TEXT NULL DEFAULT NULL COLLATE 'utf8_bin',
	 `description` TEXT NULL DEFAULT NULL COLLATE 'utf8_bin',
	 `keywords` TEXT NULL DEFAULT NULL COLLATE 'utf8_bin',
	 `title` VARCHAR(250) NULL DEFAULT NULL COLLATE 'utf8_bin',
	 `public` INT(11) NULL DEFAULT NULL,
	 `publicFrom` DATETIME NULL DEFAULT NULL,
	 `publicTo` DATETIME NULL DEFAULT NULL,
	 `forceNoIndex` INT(11) NULL DEFAULT '0',
	 `hideInSearch` INT(11) NULL DEFAULT '0',
	 `hideInSitemap` INT(11) NULL DEFAULT '0',
	 `edited` INT(11) NULL DEFAULT NULL,
	 `editedTime` DATETIME NULL DEFAULT NULL,
	 `customFieldsJson` LONGTEXT NULL DEFAULT NULL COLLATE 'utf8_bin',
	 `customContentSchemeJson` LONGTEXT NULL DEFAULT NULL COLLATE 'utf8_bin',
	 `customContentJson` LONGTEXT NULL DEFAULT NULL COLLATE 'utf8_bin',
	 `viewsNumber` INT(10) UNSIGNED NOT NULL DEFAULT '0',
	 PRIMARY KEY (`id`) USING BTREE,
	 INDEX `FK_blog_mutation` (`mutationId`) USING BTREE,
	 INDEX `FK_blog_localization_blog` (`blogId`) USING BTREE,
	 CONSTRAINT `FK_blog_localization_blog` FOREIGN KEY (`blogId`) REFERENCES `blog` (`id`) ON UPDATE CASCADE ON DELETE CASCADE,
	 CONSTRAINT `FK_blog_mutation` FOREIGN KEY (`mutationId`) REFERENCES `mutation` (`id`) ON UPDATE CASCADE ON DELETE CASCADE
)
	COLLATE='utf8_bin'
ENGINE=InnoDB
AUTO_INCREMENT=12
;
