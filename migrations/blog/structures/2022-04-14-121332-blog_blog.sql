CREATE TABLE `blog_x_blog` (
   `blogId` INT(11) NOT NULL,
   `attachedBlogId` INT(11) NOT NULL,
   PRIMARY KEY (`blogId`, `attachedBlogId`) USING BTREE,
   INDEX `FK_blog_x_blog_blog` (`blogId`) USING BTREE,
   INDEX `FK_blog_x_blog_blog_2` (`attachedBlogId`) USING BTREE,
   CONSTRAINT `FK_blog_x_blog_blog` FOREIG<PERSON>E<PERSON> (`blogId`) REFERENCES `blog` (`id`) ON UPDATE CASCADE ON DELETE CASCADE,
   CONSTRAINT `FK_blog_x_blog_blog_2` FOREIGN KEY (`attachedBlogId`) REFERENCES `blog` (`id`) ON UPDATE CASCADE ON DELETE CASCADE
)
	COLLATE='utf8_bin'
ENGINE=InnoDB
;
