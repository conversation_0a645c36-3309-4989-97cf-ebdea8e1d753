ALTER TABLE `blog_localization_x_tree`
	ADD COLUMN `sort` INT NOT NULL DEFAULT '1' AFTER `treeId`;

RENAME TABLE `blog_localization_x_tree` TO `blog_localization_tree`;


ALTER TABLE `blog_localization_tree`
	ADD COLUMN `Id` INT NOT NULL AUTO_INCREMENT FIRST,
DROP PRIMARY KEY,
DROP INDEX `FK_blog_x_tree_blog`,
DROP INDEX `FK_blog_x_tree_tree`,
	ADD PRIMARY KEY (`Id`),
DROP FOREIGN KEY `FK_blog_x_tree_blog`,
	DROP FOREIGN KEY `FK_blog_x_tree_tree`;




ALTER TABLE `blog_localization_tree`
	CHANGE COLUMN `blogId` `blogLocalizationId` INT(11) NOT NULL AFTER `Id`;



ALTER TABLE `blog_localization_tree`
	ADD CONSTRAINT `FK_blog_localization_tree_blog_localization` FOREI<PERSON>N KEY (`blogLocalizationId`) REFERENCES `blog_localization` (`id`) ON UPDATE CASCADE ON DELETE CASCADE,
	ADD CONSTRAINT `FK_blog_localization_tree_tree` FOREIGN KEY (`treeId`) REFERENCES `tree` (`id`) ON UPDATE CASCADE ON DELETE CASCADE;
