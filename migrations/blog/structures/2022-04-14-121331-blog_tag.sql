
CREATE TABLE IF NOT EXISTS `blog_tag` (
	`id` int(11) NOT NULL AUTO_INCREMENT,
	`mutationId` int(11) NOT NULL DEFAULT '1',
	`name` varchar(250) COLLATE utf8_bin DEFAULT NULL,
	`sort` int(11) NOT NULL DEFAULT '0',
	`nameAnchor` varchar(250) COLLATE utf8_bin DEFAULT NULL,
	`nameTitle` varchar(250) COLLATE utf8_bin DEFAULT NULL,
	`description` text COLLATE utf8_bin,
	`keywords` text COLLATE utf8_bin,
	`title` varchar(250) COLLATE utf8_bin DEFAULT NULL,
	`public` int(11) DEFAULT NULL,
	`forceNoIndex` int(11) DEFAULT '0',
	`hideInSearch` int(11) DEFAULT '0',
	`hideInSitemap` int(11) DEFAULT '0',
	`edited` int(11) DEFAULT NULL,
	`editedTime` datetime DEFAULT NULL,
	`customFieldsJson` longtext COLLATE utf8_bin,
	PRIMARY KEY (`id`) USING BTREE,
	KEY `FK_blog_tag_mutation` (`mutationId`) USING BTREE,
	CONSTRAINT `FK_blog_tag_mutation` FOREIGN KEY (`mutationId`) REFERENCES `mutation` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
	) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8 COLLATE=utf8_bin;
