CREATE TABLE `blog_x_blog_tag` (
   `blogId` INT(11) NOT NULL,
   `blogTagId` INT(11) NOT NULL,
   PRIMARY KEY (`blogId`, `blogTagId`) USING BTREE,
   INDEX `FK__blog` (`blogId`) USING BTREE,
   INDEX `FK__blog_tag` (`blogTagId`) USING BTREE,
   CONSTRAINT `FK__blog_tag` FOREIGN KEY (`blogTagId`) REFERENCES `blog_tag` (`id`) ON UPDATE CASCADE ON DELETE CASCADE,
   CONSTRAINT `FK_blog_x_blog_tag_blog` FOREI<PERSON><PERSON> KEY (`blogId`) REFERENCES `blog` (`id`) ON UPDATE CASCADE ON DELETE CASCADE
)
	COLLATE='utf8_bin'
ENGINE=InnoDB
;
