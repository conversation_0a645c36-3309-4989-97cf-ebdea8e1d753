ALTER TABLE `blog_tag_localization`
	CHANGE COLUMN `nameAnchor` `nameAnchor` VARCHAR(250) NOT NULL DEFAULT '' COLLATE 'utf8_bin',
	CHANGE COLUMN `nameTitle` `nameTitle` VARCHAR(250) NOT NULL DEFAULT '' COLLATE 'utf8_bin' AFTER `nameAnchor`,
	CHANGE COLUMN `description` `description` TEXT NOT NULL COLLATE 'utf8_bin' AFTER `nameTitle`,
	CHANGE COLUMN `keywords` `keywords` TEXT NOT NULL COLLATE 'utf8_bin' AFTER `description`,
	<PERSON>ANGE COLUMN `public` `public` INT(11) NOT NULL DEFAULT '0' AFTER `keywords`,
	CHANGE COLUMN `forceNoIndex` `forceNoIndex` INT(11) NOT NULL DEFAULT '0' AFTER `public`,
	CHANGE COLUMN `hideInSearch` `hideInSearch` INT(11) NOT NULL DEFAULT '0' AFTER `forceNoIndex`,
	<PERSON>AN<PERSON> COLUMN `hideInSitemap` `hideInSitemap` INT(11) NOT NULL DEFAULT '0' AFTER `hideInSearch`;


ALTER TABLE `blog_localization`
	CHANGE COLUMN `nameAnchor` `nameAnchor` VARCHAR(250) NOT NULL DEFAULT '' COLLATE 'utf8_bin',
	CHANGE COLUMN `nameTitle` `nameTitle` VARCHAR(250) NOT NULL DEFAULT '' COLLATE 'utf8_bin' AFTER `nameAnchor`,
	CHANGE COLUMN `description` `description` TEXT NOT NULL COLLATE 'utf8_bin' AFTER `nameTitle`,
	CHANGE COLUMN `keywords` `keywords` TEXT NOT NULL COLLATE 'utf8_bin' AFTER `description`,
	CHANGE COLUMN `public` `public` INT(11) NOT NULL DEFAULT '0' AFTER `keywords`,
	CHANGE COLUMN `forceNoIndex` `forceNoIndex` INT(11) NOT NULL DEFAULT '0' AFTER `public`,
	CHANGE COLUMN `hideInSearch` `hideInSearch` INT(11) NOT NULL DEFAULT '0' AFTER `forceNoIndex`,
	CHANGE COLUMN `hideInSitemap` `hideInSitemap` INT(11) NOT NULL DEFAULT '0' AFTER `hideInSearch`;


ALTER TABLE `author_localization`
	CHANGE COLUMN `nameAnchor` `nameAnchor` VARCHAR(250) NOT NULL DEFAULT '' COLLATE 'utf8_bin',
	CHANGE COLUMN `nameTitle` `nameTitle` VARCHAR(250) NOT NULL DEFAULT '' COLLATE 'utf8_bin' AFTER `nameAnchor`,
	CHANGE COLUMN `description` `description` TEXT NOT NULL COLLATE 'utf8_bin' AFTER `nameTitle`,
	CHANGE COLUMN `keywords` `keywords` TEXT NOT NULL COLLATE 'utf8_bin' AFTER `description`,
	CHANGE COLUMN `public` `public` INT(11) NOT NULL DEFAULT '0' AFTER `keywords`,
	CHANGE COLUMN `forceNoIndex` `forceNoIndex` INT(11) NOT NULL DEFAULT '0' AFTER `public`,
	CHANGE COLUMN `hideInSearch` `hideInSearch` INT(11) NOT NULL DEFAULT '0' AFTER `forceNoIndex`,
	CHANGE COLUMN `hideInSitemap` `hideInSitemap` INT(11) NOT NULL DEFAULT '0' AFTER `hideInSearch`;




ALTER TABLE `tree`
	CHANGE COLUMN `nameAnchor` `nameAnchor` VARCHAR(250) NOT NULL DEFAULT '' COLLATE 'utf8_bin',
	CHANGE COLUMN `nameTitle` `nameTitle` VARCHAR(250) NOT NULL DEFAULT '' COLLATE 'utf8_bin' AFTER `nameAnchor`,
	CHANGE COLUMN `description` `description` TEXT NOT NULL COLLATE 'utf8_bin' AFTER `nameTitle`,
	CHANGE COLUMN `keywords` `keywords` TEXT NOT NULL COLLATE 'utf8_bin' AFTER `description`,
	CHANGE COLUMN `public` `public` INT(11) NOT NULL DEFAULT '0' AFTER `keywords`,
	CHANGE COLUMN `forceNoIndex` `forceNoIndex` INT(11) NOT NULL DEFAULT '0' AFTER `public`,
	CHANGE COLUMN `hideInSearch` `hideInSearch` INT(11) NOT NULL DEFAULT '0' AFTER `forceNoIndex`,
	CHANGE COLUMN `hideInSitemap` `hideInSitemap` INT(11) NOT NULL DEFAULT '0' AFTER `hideInSearch`;


ALTER TABLE `blog_localization`
DROP COLUMN `title`;

ALTER TABLE `blog_tag_localization`
DROP COLUMN `title`;

ALTER TABLE `author_localization`
DROP COLUMN `title`;


ALTER TABLE `blog_localization`
	CHANGE COLUMN `name` `name` VARCHAR(250) NOT NULL DEFAULT '' COLLATE 'utf8_bin' AFTER `blogId`,
DROP COLUMN `pathString`;

ALTER TABLE `author_localization`
	CHANGE COLUMN `name` `name` VARCHAR(250) NOT NULL DEFAULT '' COLLATE 'utf8_czech_ci' AFTER `authorId`;

ALTER TABLE `blog_localization`
	CHANGE COLUMN `mutationId` `mutationId` INT(11) NOT NULL AFTER `id`;
ALTER TABLE `author_localization`
	CHANGE COLUMN `authorId` `authorId` INT(11) NOT NULL AFTER `mutationId`;

ALTER TABLE `blog_tag_localization`
	CHANGE COLUMN `mutationId` `mutationId` INT(11) NOT NULL AFTER `blogTagId`,
	CHANGE COLUMN `name` `name` VARCHAR(250) NOT NULL DEFAULT '' COLLATE 'utf8_bin' AFTER `mutationId`;
ALTER TABLE `tree`
	CHANGE COLUMN `name` `name` VARCHAR(255) NOT NULL DEFAULT '' COLLATE 'utf8mb4_czech_ci' AFTER `publicTo`;


ALTER TABLE `blog_tag_localization`
	ADD COLUMN `customContentJson` LONGTEXT NULL DEFAULT NULL AFTER `customFieldsJson`;
