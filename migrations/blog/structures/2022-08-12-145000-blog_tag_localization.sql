ALTER TABLE `blog_x_blog_tag`
DROP FOREIGN KEY `FK__blog_tag`;
ALTER TABLE `blog_x_blog_tag`
	ADD CONSTRAINT `FK_blog_x_blog_tag_blog_tag` <PERSON>OR<PERSON><PERSON><PERSON> KEY (`blogTagId`) REFERENCES `blog_tag` (`id`) ON UPDATE CASCADE ON DELETE CASCADE;


ALTER TABLE `blog_tag_localization`
	ADD CONSTRAINT `FK_blog_tag_localization_blog_tag` FOREI<PERSON>N KEY (`blogTagId`) REFERENCES `blog_tag` (`id`) ON UPDATE CASCADE ON DELETE CASCADE;
