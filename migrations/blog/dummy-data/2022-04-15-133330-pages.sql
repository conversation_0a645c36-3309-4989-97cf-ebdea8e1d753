INSERT INTO `tree`  (`id`, `rootId`, `parentId`, `level`, `path`, `sort`, `last`, `public`, `uid`, `created`, `createdTime`, `createdTimeOrder`, `edited`, `editedTime`, `template`, `type`, `publicFrom`, `publicTo`, `name`, `nameTitle`, `nameAnchor`, `annotation`, `description`, `keywords`, `content`, `hideFirstImage`, `links`, `seoTitleFilter`, `seoAnnotationFilter`, `seoDescriptionFilter`, `videos`, `hideCrossroad`, `hideInSitemap`, `forceNoIndex`, `hideInSearch`, `showContactForm`, `showOnHomepage`, `customFieldsJson`, `customContentJson`, `customContentSchemeJson`, `productAttachedId`, `hasLinkedCategories`) VALUES (447, 1, 446, 2, '1|446|', 1, 1, 1, '', 0, '2021-08-16 16:42:05', '2021-08-16 16:42:05', 5, '2021-08-23 15:44:49', 'Blog:default', 'common', '2021-08-16 16:42:05', '2121-08-16 16:42:05', 'Kategorie 1', 'Kategorie 1', 'Kategorie 1', '', '', '', '', 0, '', NULL, NULL, NULL, '', 0, 0, 0, 0, 0, 0, '{}', NULL, NULL, NULL, NULL);
INSERT INTO `alias` (`id`, `alias`, `module`, `referenceId`, `mutationId`)
	VALUES (2200, 'kategorie-1', 'tree', 447, 1);

INSERT INTO `tree`  (`id`, `rootId`, `parentId`, `level`, `path`, `sort`, `last`, `public`, `uid`, `created`, `createdTime`, `createdTimeOrder`, `edited`, `editedTime`, `template`, `type`, `publicFrom`, `publicTo`, `name`, `nameTitle`, `nameAnchor`, `annotation`, `description`, `keywords`, `content`, `hideFirstImage`, `links`, `seoTitleFilter`, `seoAnnotationFilter`, `seoDescriptionFilter`, `videos`, `hideCrossroad`, `hideInSitemap`, `forceNoIndex`, `hideInSearch`, `showContactForm`, `showOnHomepage`, `customFieldsJson`, `customContentJson`, `customContentSchemeJson`, `productAttachedId`, `hasLinkedCategories`) VALUES (448, 1, 446, 2, '1|446|', 1, 1, 1, '', 0, '2021-08-16 16:42:34', '2021-08-16 16:42:34', 5, '2021-08-23 15:44:30', 'Blog:default', 'common', '2021-08-16 16:42:34', '2121-08-16 16:42:34', 'Kategorie 2', 'Kategorie 2', 'Kategorie 2', '', '', '', '', 0, '', NULL, NULL, NULL, '', 0, 0, 0, 0, 0, 0, '{}', NULL, NULL, NULL, NULL);
INSERT INTO `alias` (`id`, `alias`, `module`, `referenceId`, `mutationId`)
	VALUES (2201, 'kategorie-2', 'tree', 448, 1);
