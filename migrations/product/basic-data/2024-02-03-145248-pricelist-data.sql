UPDATE `price_level` SET `name` = 'Cena e-shop sklad' WHERE `id` = '1';
UPDATE `price_level` SET `type` = 'supplier', `name` = 'Cena dodavatel sklad' WHERE `id` = '2';
INSERT INTO `price_level` (`type`, `name`) VALUES ('default_discount', 'Akciová cena e-shop sklad');
INSERT INTO `price_level` (`type`, `name`) VALUES ('supplier_discount', 'Akciová cena dodavatel sklad');
INSERT INTO `price_level` (`type`, `name`) VALUES ('recommended', 'Doporučená prodejní cena');
INSERT INTO `price_level` (`type`, `name`) VALUES ('reference', 'Referenční cena');
UPDATE `stock` SET `name` = 'e-shop sklad' WHERE `id` = '1';

ALTER TABLE `product_variant_price`
	ADD `validFrom` date NULL,
	ADD `validTo` date NULL AFTER `validFrom`;

ALTER TABLE `price_level`
	ADD `hasValid` tinyint(1) NOT NULL DEFAULT '0';

UPDATE `price_level` SET `hasValid` = '1' WHERE `id` = '3';
UPDATE `price_level` SET `hasValid` = '1' WHERE `id` = '4';
