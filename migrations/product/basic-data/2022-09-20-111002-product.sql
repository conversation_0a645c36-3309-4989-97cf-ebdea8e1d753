INSERT INTO `product` (`id`, `uid`, `template`, `publicFrom`, `publicTo`, `internalName`, `hideFirstImage`, `availability`, `isSet`, `isOld`, `isInPrepare`, `isNew`, `notSoldSeparately`, `discount`, `discountType`, `reviewAverage`, `isFreeTransport`, `soldCount`, `availableServices`, `mainCategoryUID`, `customFieldsJson`, `editedTime`, `edited`, `vats`, `extId`) VALUES
	(2, NULL, ':Front:Product:detail', '2018-04-11 15:28:38', '2120-04-11 15:28:38', 'produkt 2', 0, '', NULL, 0, 0, 0, 0, NULL, NULL, 0.00, 0, 0, '', 'eshop-1', '{"feeds":{"zbozi":"","heureka":"","google":""},"article":{"tree":""}}', '2021-06-28 08:37:05', 3, '{"1":"default","2":"default","3":"default","4":"default","5":"default","6":"default","7":"default","8":"default"}', NULL);
INSERT INTO `product` (`id`, `uid`, `template`, `publicFrom`, `publicTo`, `internalName`, `hideFirstImage`, `availability`, `isSet`, `isOld`, `isInPrepare`, `isNew`, `notSoldSeparately`, `discount`, `discountType`, `reviewAverage`, `isFreeTransport`, `soldCount`, `availableServices`, `mainCategoryUID`, `customFieldsJson`, `editedTime`, `edited`, `vats`, `extId`) VALUES
	(4, NULL, ':Front:Product:detail', '2018-04-11 15:28:38', '2120-04-11 15:28:38', 'Produkt 4', 0, '', NULL, 0, 0, 0, 0, NULL, NULL, 0.00, 0, 0, '', NULL, '{"feeds":{"zbozi":"","heureka":"","google":""}}', '2021-07-07 17:05:23', 1, '{"1":"default","2":"default","3":"default","4":"default","5":"default","6":"default","7":"default","8":"default"}', NULL);
INSERT INTO `product` (`id`, `uid`, `template`, `publicFrom`, `publicTo`, `internalName`, `hideFirstImage`, `availability`, `isSet`, `isOld`, `isInPrepare`, `isNew`, `notSoldSeparately`, `discount`, `discountType`, `reviewAverage`, `isFreeTransport`, `soldCount`, `availableServices`, `mainCategoryUID`, `customFieldsJson`, `editedTime`, `edited`, `vats`, `extId`) VALUES
	(5, NULL, ':Front:Product:detail', '2018-04-11 15:28:38', '2120-04-11 15:28:38', 'produkt 5', 0, '', NULL, 0, 0, 0, 0, NULL, NULL, 0.00, 0, 0, '', NULL, '{"feeds":{"zbozi":"","heureka":"","google":""}}', '2021-06-28 08:34:53', 3, '{"1":"default","2":"default","3":"default","4":"default","5":"default","6":"default","7":"default","8":"default"}', NULL);
INSERT INTO `product` (`id`, `uid`, `template`, `publicFrom`, `publicTo`, `internalName`, `hideFirstImage`, `availability`, `isSet`, `isOld`, `isInPrepare`, `isNew`, `notSoldSeparately`, `discount`, `discountType`, `reviewAverage`, `isFreeTransport`, `soldCount`, `availableServices`, `mainCategoryUID`, `customFieldsJson`, `editedTime`, `edited`, `vats`, `extId`) VALUES
	(6, NULL, ':Front:Product:detail', '2018-04-11 15:28:38', '2120-04-11 15:28:38', 'produkt 6', 0, '', NULL, 0, 0, 0, 0, NULL, NULL, 0.00, 0, 0, '', NULL, '[]', '2021-06-28 08:33:48', 3, '{"1":"default","2":"default","3":"default","4":"default","5":"default","6":"default","7":"default","8":"default"}', NULL);
INSERT INTO `product` (`id`, `uid`, `template`, `publicFrom`, `publicTo`, `internalName`, `hideFirstImage`, `availability`, `isSet`, `isOld`, `isInPrepare`, `isNew`, `notSoldSeparately`, `discount`, `discountType`, `reviewAverage`, `isFreeTransport`, `soldCount`, `availableServices`, `mainCategoryUID`, `customFieldsJson`, `editedTime`, `edited`, `vats`, `extId`) VALUES
	(7, NULL, ':Front:Product:detail', '2018-04-11 15:28:38', '2120-04-11 15:28:38', 'produkt 7', 0, '', NULL, 0, 0, 0, 0, NULL, NULL, 0.00, 0, 0, '', NULL, '[]', '2021-06-28 08:53:36', 3, '{"1":"default","2":"default","3":"default","4":"default","5":"default","6":"default","7":"default","8":"default"}', NULL);
INSERT INTO `product` (`id`, `uid`, `template`, `publicFrom`, `publicTo`, `internalName`, `hideFirstImage`, `availability`, `isSet`, `isOld`, `isInPrepare`, `isNew`, `notSoldSeparately`, `discount`, `discountType`, `reviewAverage`, `isFreeTransport`, `soldCount`, `availableServices`, `mainCategoryUID`, `customFieldsJson`, `editedTime`, `edited`, `vats`, `extId`) VALUES
	(8, NULL, ':Front:Product:detail', '2018-04-11 15:28:38', '2120-04-11 15:28:38', 'produkt 8', 0, '', NULL, 0, 0, 0, 0, NULL, NULL, 0.00, 0, 0, '', NULL, '[]', '2021-06-29 16:17:25', 14, '{"1":"default","2":"default","3":"default","4":"default","5":"default","6":"default","7":"default","8":"default"}', NULL);
INSERT INTO `product` (`id`, `uid`, `template`, `publicFrom`, `publicTo`, `internalName`, `hideFirstImage`, `availability`, `isSet`, `isOld`, `isInPrepare`, `isNew`, `notSoldSeparately`, `discount`, `discountType`, `reviewAverage`, `isFreeTransport`, `soldCount`, `availableServices`, `mainCategoryUID`, `customFieldsJson`, `editedTime`, `edited`, `vats`, `extId`) VALUES
	(9, NULL, ':Front:Product:detail', '2018-04-11 15:28:00', '2120-04-11 15:28:00', 'produkt 9', 0, '', NULL, 0, 0, 0, 0, NULL, NULL, 0.00, 0, 0, '', NULL, '', '2021-11-30 16:33:58', 3, '{"1":"default","2":"default","3":"default","4":"default","5":"default","6":"default","7":"default","8":"default"}', NULL);
INSERT INTO `product` (`id`, `uid`, `template`, `publicFrom`, `publicTo`, `internalName`, `hideFirstImage`, `availability`, `isSet`, `isOld`, `isInPrepare`, `isNew`, `notSoldSeparately`, `discount`, `discountType`, `reviewAverage`, `isFreeTransport`, `soldCount`, `availableServices`, `mainCategoryUID`, `customFieldsJson`, `editedTime`, `edited`, `vats`, `extId`) VALUES
	(10, NULL, ':Front:Product:detail', '2018-04-11 15:28:00', '2120-04-11 15:28:00', 'VZDUCHOVKA HAMMERLI HUNTER FORCE 900 COMBO 4,5MM1', 0, '', NULL, 0, 0, 1, 0, NULL, NULL, 0.00, 0, 0, '', NULL, '{}', '2022-01-13 13:37:38', 4, '{"1":"default","2":"default","3":"default","4":"default","5":"default","6":"default","7":"default","8":"default"}', NULL);
INSERT INTO `product` (`id`, `uid`, `template`, `publicFrom`, `publicTo`, `internalName`, `hideFirstImage`, `availability`, `isSet`, `isOld`, `isInPrepare`, `isNew`, `notSoldSeparately`, `discount`, `discountType`, `reviewAverage`, `isFreeTransport`, `soldCount`, `availableServices`, `mainCategoryUID`, `customFieldsJson`, `editedTime`, `edited`, `vats`, `extId`) VALUES
	(11, NULL, ':Front:Product:detail', '2021-07-04 18:16:00', '2121-07-04 18:16:00', 'Vzduchovka Kral Arms N-08 Camo 5,5mm', 0, '', NULL, 0, 0, 0, 0, NULL, NULL, 0.00, 0, 0, '', NULL, '{}', '2022-04-04 10:42:30', 13, '{"1":"default","2":"default","3":"default","4":"default","5":"default","6":"default","7":"default","8":"default"}', NULL);
INSERT INTO `product` (`id`, `uid`, `template`, `publicFrom`, `publicTo`, `internalName`, `hideFirstImage`, `availability`, `isSet`, `isOld`, `isInPrepare`, `isNew`, `notSoldSeparately`, `discount`, `discountType`, `reviewAverage`, `isFreeTransport`, `soldCount`, `availableServices`, `mainCategoryUID`, `customFieldsJson`, `editedTime`, `edited`, `vats`, `extId`) VALUES
	(13, NULL, ':Front:Product:detail', '2021-12-13 11:40:00', '2121-07-13 11:38:00', '', 0, '', NULL, 0, 0, 0, 0, NULL, NULL, 0.00, 0, 0, '', NULL, '{}', '2021-09-03 09:59:25', 5, '{"1":"default","2":"default","3":"default","4":"default","5":"default","6":"default","7":"default","8":"default"}', NULL);
INSERT INTO `product` (`id`, `uid`, `template`, `publicFrom`, `publicTo`, `internalName`, `hideFirstImage`, `availability`, `isSet`, `isOld`, `isInPrepare`, `isNew`, `notSoldSeparately`, `discount`, `discountType`, `reviewAverage`, `isFreeTransport`, `soldCount`, `availableServices`, `mainCategoryUID`, `customFieldsJson`, `editedTime`, `edited`, `vats`, `extId`) VALUES
	(14, NULL, ':Front:Product:detail', '2021-08-23 14:44:00', '2121-08-23 14:44:00', 'Název', 0, '', NULL, 0, 0, 0, 0, NULL, NULL, 0.00, 0, 0, '', NULL, '{}', '2022-02-08 14:09:22', 4, '{"1":"default","2":"default","3":"default","4":"default","5":"default","6":"default","7":"default","8":"default"}', NULL);
