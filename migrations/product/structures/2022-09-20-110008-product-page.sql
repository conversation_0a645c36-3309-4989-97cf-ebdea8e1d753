CREATE TABLE IF NOT EXISTS `product_tree` (
	`id` int(11) NOT NULL AUTO_INCREMENT,
	`productId` int(11) NOT NULL,
	`treeId` int(11) NOT NULL,
	`sort` int(11) NOT NULL,
	<PERSON>IMARY KEY (`id`),
	UNIQUE KEY `productId_treeId` (`productId`,`treeId`),
	<PERSON><PERSON>Y `treeId` (`treeId`),
	CONSTRAINT `product_tree_ibfk_1` FOREIGN KEY (`productId`) REFERENCES `product` (`id`) ON DELETE CASCADE,
	CONSTRAINT `product_tree_ibfk_2` FOREIGN KEY (`treeId`) REFERENCES `tree` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=13709 DEFAULT CHARSET=utf8;
