CREATE TABLE IF NOT EXISTS `product_variant_price` (
	`id` bigint(20) NOT NULL AUTO_INCREMENT,
	`mutationId` int(11) NOT NULL,
	`priceLevelId` int(11) NOT NULL,
	`productId` int(11) NOT NULL,
	`productVariantId` int(11) NOT NULL,
	`price` decimal(13,5) NOT NULL,
	PRIMARY KEY (`id`),
	<PERSON>EY `mutationId` (`mutationId`),
	KEY `productVariantId` (`productVariantId`),
	KEY `priceLevelId` (`priceLevelId`),
	KEY `productId` (`productId`),
	CONSTRAINT `product_variant_price_ibfk_1` FOREIGN KEY (`mutationId`) REFERENCES `mutation` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
	CONSTRAINT `product_variant_price_ibfk_2` FOREIGN KEY (`productVariantId`) REFERENCES `product_variant` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
	CONSTRAINT `product_variant_price_ibfk_3` FOREIG<PERSON> KEY (`priceLevelId`) REFERENCES `price_level` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
	CONSTRAINT `product_variant_price_ibfk_4` FOREIGN KEY (`productId`) REFERENCES `product` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_czech_ci;
