SET NAMES utf8mb4;

DROP TABLE IF EXISTS `product_type`;
CREATE TABLE `product_type` (
								`id` int(11) NOT NULL AUTO_INCREMENT,
								`uid` varchar(255) NOT NULL,
								`name` varchar(255) NOT NULL,
								`icon` varchar(255) NOT NULL,
								`sort` int(11) NOT NULL,
								PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci;


ALTER TABLE `product`
	ADD `productTypeId` int(11) NULL AFTER `id`;

ALTER TABLE `product`
	ADD FOREIGN KEY (`productTypeId`) REFERENCES `product_type` (`id`);
