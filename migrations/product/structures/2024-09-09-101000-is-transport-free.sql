ALTER TABLE `product`
	CHANGE COLUMN `isFreeTransport` `isFreeTransport` TINYINT(1) NOT NULL DEFAULT '0' AFTER `reviewAverage`,
	ADD COLUMN `isFreeTransportForced` TINYINT(1) NOT NULL DEFAULT '0' AFTER `isFreeTransport`;


ALTER TABLE `product`
DROP COLUMN `isNew`,
	DROP COLUMN `isBestseller`,
	DROP COLUMN `isTop`;


UPDATE `tag` SET `internalName`='Free transit' WHERE  `id`=6;
DELETE FROM `tag` WHERE  `id`=8;


ALTER TABLE `tag_localization`
	CHANGE COLUMN `name` `name` VA<PERSON>HAR(255) NULL DEFAULT NULL COLLATE 'utf8mb4_unicode_520_ci' AFTER `edited`;


ALTER TABLE `tag_x_product`
	ADD COLUMN `from` DATETIME NULL DEFAULT current_timestamp() AFTER `edited`,
	ADD COLUMN `to` DATETIME NULL DEFAULT current_timestamp() AFTER `from`;

ALTER TABLE `tag_x_product`
	<PERSON>ANGE COLUMN `from` `from` DATETIME NULL AFTER `edited`,
	CHANGE COLUMN `to` `to` DATETIME NULL AFTER `from`;

ALTER TABLE `tag_x_product`
	ADD UNIQUE INDEX `tagId_productId` (`tagId`, `productId`);


ALTER TABLE `product`
	ADD COLUMN `freeTransportForcedFrom` DATE NULL AFTER `isFreeTransportForced`,
	ADD COLUMN `freeTransportForcedTo` DATE NULL AFTER `freeTransportForcedFrom`;

