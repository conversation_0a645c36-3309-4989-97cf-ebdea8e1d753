CREATE TABLE IF NOT EXISTS `product_parameter` (
	`id` int(11) NOT NULL AUTO_INCREMENT,
	`productId` int(11) NOT NULL,
	`parameterId` int(11) NOT NULL,
	`parameterValueId` int(11) NOT NULL,
	PRIMARY KEY (`id`),
	UNIQUE KEY `productId_parameterId_parameterValueId` (`productId`,`parameterId`,`parameterValueId`),
	<PERSON><PERSON><PERSON> `parameterId` (`parameterId`),
	KEY `parameterValueId` (`parameterValueId`),
	CONSTRAINT `product_parameter_ibfk_4` FOREIGN KEY (`productId`) REFERENCES `product` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
	CONSTRAINT `product_parameter_ibfk_5` FOREIGN KEY (`parameterId`) REFERENCES `parameter` (`id`) ON UPDATE CASCADE,
	CONSTRAINT `product_parameter_ibfk_6` FOREIGN KEY (`parameterValueId`) REFERENCES `parameter_value` (`id`) ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;
