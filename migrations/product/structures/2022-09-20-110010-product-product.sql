CREATE TABLE IF NOT EXISTS `product_product` (
	`id` int(11) NOT NULL AUTO_INCREMENT,
	`mainProductId` int(11) NOT NULL,
	`attachedProductId` int(11) NOT NULL,
	`type` varchar(200) COLLATE utf8_czech_ci DEFAULT NULL,
	`sort` int(11) NOT NULL,
	PRIMARY KEY (`id`),
	UNIQUE KEY `productMainId_productId_type` (`mainProductId`,`attachedProductId`,`type`),
	KEY `productId` (`attachedProductId`),
	KEY `mainProductId` (`mainProductId`),
	CONSTRAINT `product_product_ibfk_1` FOREIGN KEY (`mainProductId`) REFERENCES `product` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
	CONSTRAINT `product_product_ibfk_4` FOREIGN KEY (`attachedProductId`) REFERENCES `product` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8 COLLATE=utf8_czech_ci;
