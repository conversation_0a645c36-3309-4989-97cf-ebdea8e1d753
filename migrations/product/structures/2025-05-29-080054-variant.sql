ALTER TABLE `product_variant`
	CHANGE `productId` `productId` int(11) NULL AFTER `id`,
	DROP `isbn`;

ALTER TABLE `product_variant`
	CHANGE `extId` `extId` int(11) NULL;

ALTER TABLE `product_variant`
	ADD `minExpriration`  datetime                                    NULL,
	ADD `weight`          int(11)                                     NULL AFTER `minExpriration`,
	ADD `weightUnit`      varchar(20)                                 NULL AFTER `weight`,
	ADD `erpName`         varchar(255)                                NULL AFTER `weightUnit`,
	ADD `erpContent`      mediumtext COLLATE 'utf8mb4_unicode_520_ci' NULL AFTER `erpName`,
	ADD `erpCategoryIds`  varchar(255)                                NULL AFTER `erpContent`,
	ADD `erpCategoryPath` text                                        NULL AFTER `erpCategoryIds`,
	ADD `erpVat`          decimal(10, 4)                              NULL AFTER `erpCategoryPath`,
	ADD `tempImageId`     int(11)                                     NULL AFTER `erpVat`;

ALTER TABLE `product_variant_price`
	CHANGE `productId` `productId` int(11) NULL AFTER `realOrigPriceDPH`;


