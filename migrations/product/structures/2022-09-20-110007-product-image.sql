CREATE TABLE IF NOT EXISTS `product_image` (
	`id` int(11) NOT NULL AUTO_INCREMENT,
	`productId` int(11) NOT NULL,
	`imageId` int(11) NOT NULL COMMENT 'idfile',
	`name` varchar(250) DEFAULT NULL,
	`url` varchar(250) DEFAULT NULL,
	`sort` tinyint(4) DEFAULT NULL,
	`variants` varchar(255) DEFAULT NULL,
	`data` longtext,
	`extId` varchar(32) DEFAULT NULL,
	PRIMARY KEY (`id`),
	UNIQUE KEY `productId_imageId` (`productId`,`imageId`),
	UNIQUE KEY `productId_extId` (`productId`,`extId`),
	KEY `imageId` (`imageId`),
	CONSTRAINT `product_image_ibfk_3` FOREIGN KEY (`productId`) REFERENCES `product` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
	CONSTRAINT `product_image_ibfk_4` FOREIGN KEY (`imageId`) REFERENCES `image` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
	) ENGINE=InnoDB AUTO_INCREMENT=33 DEFAULT CHARSET=utf8;
