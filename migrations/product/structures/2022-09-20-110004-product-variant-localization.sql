CREATE TABLE IF NOT EXISTS `product_variant_localization` (
	`id` int(11) NOT NULL AUTO_INCREMENT,
	`variantId` int(11) NOT NULL,
	`mutationId` int(11) NOT NULL,
	`active` int(11) NOT NULL DEFAULT '0',
	`name` varchar(255) COLLATE utf8_bin DEFAULT NULL,
	PRIMARY KEY (`id`),
	UNIQUE KEY `variantId_mutationId` (`variantId`,`mutationId`),
	<PERSON>EY `mutationId` (`mutationId`),
	CONSTRAINT `product_variant_localization_ibfk_1` FOREIGN KEY (`mutationId`) REFERENCES `mutation` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
	CONSTRAINT `product_variant_localization_ibfk_2` FOREIGN KEY (`variantId`) REFERENCES `product_variant` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 COLLATE=utf8_bin;
