-- Adminer 4.8.1 MySQL 10.6.12-<PERSON>D<PERSON> dump

SET NAMES utf8;
SET time_zone = '+00:00';
SET foreign_key_checks = 0;
SET sql_mode = 'NO_AUTO_VALUE_ON_ZERO';

SET NAMES utf8mb4;

DROP TABLE IF EXISTS `gift`;
CREATE TABLE `gift` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `internalName` varchar(255) NOT NULL,
  `productId` int(11) DEFAULT NULL,
  `price_amount` decimal(18,4) DEFAULT NULL,
  `price_currency` char(3) DEFAULT NULL,
  `sort` int(11) DEFAULT NULL,
  `minPrice` decimal(18,4) DEFAULT NULL,
  `minCount` int(11) DEFAULT NULL,
  `onFirstOrder` tinyint(1) NOT NULL DEFAULT 0,
  `customFieldsJson` longtext DEFAULT NULL,
  PRIMARY KEY (`id`),
  <PERSON><PERSON>Y `productId` (`productId`),
  CONSTRAINT `gift_ibfk_4` FOREIGN KEY (`productId`) REFERENCES `product` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci;


DROP TABLE IF EXISTS `gift_localization`;
CREATE TABLE `gift_localization` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `mutationId` int(11) NOT NULL,
  `giftId` int(11) NOT NULL,
  `name` varchar(255) NOT NULL,
  `public` tinyint(1) NOT NULL DEFAULT 0,
  `publicFrom` datetime DEFAULT NULL,
  `publicTo` datetime DEFAULT NULL,
  `edited` int(11) DEFAULT NULL,
  `editedTime` datetime DEFAULT NULL,
  `customFieldsJson` longtext DEFAULT NULL,
  `customContentJson` longtext DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `mutationId` (`mutationId`),
  KEY `giftId` (`giftId`),
  CONSTRAINT `gift_localization_ibfk_1` FOREIGN KEY (`mutationId`) REFERENCES `mutation` (`id`),
  CONSTRAINT `gift_localization_ibfk_2` FOREIGN KEY (`giftId`) REFERENCES `gift` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci;


DROP TABLE IF EXISTS `gift_x_product`;
CREATE TABLE `gift_x_product` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `giftId` int(11) NOT NULL,
  `productId` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `giftId_productId` (`giftId`,`productId`),
  KEY `productId` (`productId`),
  KEY `giftId` (`giftId`),
  CONSTRAINT `gift_x_product_ibfk_1` FOREIGN KEY (`giftId`) REFERENCES `gift` (`id`),
  CONSTRAINT `gift_x_product_ibfk_2` FOREIGN KEY (`productId`) REFERENCES `product` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci;


-- 2024-05-15 15:00:13

ALTER TABLE `product`
	ADD `giftDate` date NULL;
ALTER TABLE `gift`
	CHANGE `sort` `sort` int(11) NOT NULL DEFAULT '0' AFTER `price_currency`;

ALTER TABLE `gift`
	CHANGE `price_amount` `price_amount` decimal(18,4) NOT NULL DEFAULT '0' AFTER `productId`,
	CHANGE `price_currency` `price_currency` char(3) COLLATE 'utf8mb4_unicode_520_ci' NOT NULL DEFAULT 'CZK' AFTER `price_amount`;
