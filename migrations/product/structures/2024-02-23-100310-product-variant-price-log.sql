-- Adminer 4.8.1 MySQL 10.6.12-<PERSON>D<PERSON> dump

SET NAMES utf8;
SET time_zone = '+00:00';
SET foreign_key_checks = 0;
SET sql_mode = 'NO_AUTO_VALUE_ON_ZERO';

SET NAMES utf8mb4;

DROP TABLE IF EXISTS `product_variant_price_log`;
CREATE TABLE `product_variant_price_log` (
											 `id` int(11) NOT NULL AUTO_INCREMENT,
											 `mutationId` int(11) NOT NULL,
											 `productVariantId` int(11) NOT NULL,
											 `priceLevelId` int(11) NOT NULL,
											 `realOrigPrice` decimal(10,2) NOT NULL,
											 `origPrice` decimal(10,2) NOT NULL,
											 `salePrice` decimal(10,2) NOT NULL,
											 `createdAt` datetime DEFAULT NULL,
											 `lastSaleAt` datetime DEFAULT NULL,
											 PRIMARY KEY (`id`),
											 KEY `mutationId` (`mutationId`),
											 <PERSON><PERSON>Y `productVariantId` (`productVariantId`),
											 <PERSON><PERSON>Y `priceLevelId` (`priceLevelId`),
											 CONSTRAINT `product_variant_price_log_ibfk_1` FOREIGN KEY (`priceLevelId`) REFERENCES `price_level` (`id`) ON DELETE CASCADE,
											 CONSTRAINT `product_variant_price_log_mutation` FOREIGN KEY (`mutationId`) REFERENCES `mutation` (`id`),
											 CONSTRAINT `product_variant_price_log_variant` FOREIGN KEY (`productVariantId`) REFERENCES `product_variant` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci;


-- 2024-02-23 09:09:48

ALTER TABLE `product_variant_price`
	ADD `realOrigPriceDPH` float(10,2) NOT NULL AFTER `priceLevelId`;

ALTER TABLE `price_level`
	ADD `discountPriceId` int NULL;

ALTER TABLE `price_level`
	ADD FOREIGN KEY (`discountPriceId`) REFERENCES `price_level` (`id`) ON DELETE SET NULL;

UPDATE `price_level` SET `discountPriceId` = '3' WHERE `id` = '1';
UPDATE `price_level` SET `discountPriceId` = '4' WHERE `id` = '2';
