CREATE TABLE IF NOT EXISTS `tree_product` (
	`id` int(11) NOT NULL AUTO_INCREMENT,
	`productId` int(11) NOT NULL,
	`treeId` int(11) NOT NULL,
	`type` varchar(20) COLLATE utf8_czech_ci NOT NULL DEFAULT '',
	`sort` int(11) NOT NULL,
	PRIMARY KEY (`id`),
	UNIQUE KEY `productId_treeId_type` (`productId`,`treeId`,`type`),
	KEY `idproduct` (`productId`),
	KEY `idtree` (`treeId`),
	CONSTRAINT `tree_product_ibfk_2` FOREIGN KEY (`treeId`) REFERENCES `tree` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
	CONSTRAINT `tree_product_ibfk_5` FOREIGN KEY (`productId`) REFERENCES `product` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8 COLLATE=utf8_czech_ci;
