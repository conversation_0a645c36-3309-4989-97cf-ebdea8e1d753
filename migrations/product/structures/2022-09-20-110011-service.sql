CREATE TABLE IF NOT EXISTS `services` (
	`id` int(11) NOT NULL AUTO_INCREMENT,
	`public` tinyint(1) NOT NULL DEFAULT '0',
	`name` varchar(255) COLLATE utf8_czech_ci NOT NULL,
	`price` float(10,2) DEFAULT NULL,
	`priceDPH` float(10,2) DEFAULT NULL,
	`description` text COLLATE utf8_czech_ci,
	`createdTime` datetime DEFAULT NULL,
	`created` int(11) DEFAULT NULL,
	`sort` int(11) DEFAULT NULL,
	PRIMARY KEY (`id`),
	<PERSON><PERSON><PERSON> `created` (`created`),
	CONSTRAINT `services_ibfk_1` FOREIGN KEY (`created`) REFERENCES `user` (`id`) ON DELETE NO ACTION
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 COLLATE=utf8_czech_ci;
