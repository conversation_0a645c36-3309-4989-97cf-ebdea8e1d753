ALTER TABLE `price_level`
	DROP FOREIGN KEY `price_level_ibfk_1`;

ALTER TABLE `price_level`
	ADD `discount`                  decimal(4, 2) NULL AFTER `hasValid`,
	ADD `discountSubscription`      decimal(4, 2) NULL AFTER `discount`,
	ADD `discountSubscriptionFirst` decimal(4, 2) NULL AFTER `discountSubscription`,
	DROP `discountPriceId`;

ALTER TABLE `price_level`
	DROP `hasValid`;

ALTER TABLE `price_level`
	ADD `sort` tinyint NOT NULL DEFAULT '0';

ALTER TABLE `product_variant`
	ADD `margin`    decimal(8, 2) NULL,
	ADD `marginMin` decimal(8, 2) NULL;

ALTER TABLE `product_variant`
	ADD `usePrice` enum ('margin','catalog') NOT NULL DEFAULT 'margin';

ALTER TABLE `product_variant`
	ADD `isForRevaluation` tinyint NOT NULL DEFAULT '0' AFTER `isReSale`;

ALTER TABLE `product_variant`
	ADD `erpNewPurchasePrice` decimal(10,4) NULL AFTER `erpVat`;
