CREATE TABLE IF NOT EXISTS `product_file` (
	`id` int(11) NOT NULL AUTO_INCREMENT,
	`productLocalizationId` int(11) NOT NULL,
	`fileId` int(11) NOT NULL,
	`name` varchar(250) DEFAULT NULL,
	`url` varchar(250) DEFAULT NULL,
	`sort` int(11) DEFAULT NULL,
	`size` int(11) DEFAULT NULL,
	PRIMARY KEY (`id`),
	UNIQUE KEY `productId_fileId` (`productLocalizationId`,`fileId`),
	KEY `fileId` (`fileId`),
	CONSTRAINT `product_file_ibfk_4` FOREIGN KEY (`fileId`) REFERENCES `file` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
	CONSTRAINT `product_file_ibfk_5` FOREIGN KEY (`productLocalizationId`) REFERENCES `product_localization` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=21 DEFAULT CHARSET=utf8;
