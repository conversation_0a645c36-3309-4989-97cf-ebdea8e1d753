ALTER TABLE `product` ADD `isDamaged` tinyint(1) NOT NULL DEFAULT '0' AFTER `isElectronic`;
ALTER TABLE `product` ADD `boostScore` int NULL;
ALTER TABLE `product` ADD `boostScoreValid` datetime NULL;
ALTER TABLE `product`
	ADD `syncChecksum` varchar(128) NULL AFTER `syncTime`,
	ADD `damagedType` varchar(255) NULL AFTER `isDamaged`,
	ADD `damagedParentId` int NULL AFTER `damagedType`;

ALTER TABLE `product`
	ADD FOREIGN KEY (`damagedParentId`) REFERENCES `product` (`id`);

ALTER TABLE `product` ADD `deleted` tinyint(1) NOT NULL DEFAULT '0';

ALTER TABLE `product`
	ADD `itemType` varchar(50) NULL AFTER `id`;

ALTER TABLE `import_cache`
	CHANGE `type` `type` enum('product','stock','review','product_images') COLLATE 'utf8mb4_czech_ci' NOT NULL AFTER `id`;
