CREATE TABLE IF NOT EXISTS `product` (
	`id` int(11) NOT NULL AUTO_INCREMENT,
	`uid` varchar(50) DEFAULT NULL,
	`template` varchar(60) NOT NULL DEFAULT 'Product:detail',
	`publicFrom` datetime DEFAULT NULL,
	`publicTo` datetime DEFAULT NULL,
	`internalName` varchar(255) DEFAULT NULL,
	`hideFirstImage` tinyint(1) NOT NULL,
	`availability` varchar(40) NOT NULL,
	`isSet` tinyint(4) DEFAULT NULL,
	`isOld` tinyint(4) DEFAULT NULL,
	`isInPrepare` tinyint(4) DEFAULT NULL,
	`isNew` tinyint(4) NOT NULL,
	`notSoldSeparately` tinyint(4) DEFAULT NULL,
	`discount` float(10,2) DEFAULT NULL,
	`discountType` tinyint(1) DEFAULT NULL,
	`reviewAverage` float(10,2) NOT NULL DEFAULT '0.00',
	`isFreeTransport` tinyint(1) DEFAULT '0',
	`soldCount` int(11) DEFAULT '0',
	`availableServices` text,
	`mainCategoryUID` varchar(250) DEFAULT NULL,
	`customFieldsJson` text,
	`editedTime` datetime DEFAULT NULL,
	`edited` int(11) DEFAULT NULL,
	`vats` text,
	`extId` varchar(32) DEFAULT NULL,
	PRIMARY KEY (`id`),
	UNIQUE KEY `extId` (`extId`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;
