CREATE TABLE IF NOT EXISTS `product_review` (
	`id` int(11) NOT NULL AUTO_INCREMENT,
	`productId` int(11) NOT NULL,
	`userId` int(11) DEFAULT NULL,
	`isWebMaster` tinyint(1) NOT NULL DEFAULT '0',
	`isMain` tinyint(1) NOT NULL COMMENT 'main review for user',
	`name` varchar(250) DEFAULT NULL,
	`email` varchar(250) DEFAULT NULL,
	`stars` int(11) DEFAULT NULL,
	`date` datetime DEFAULT NULL,
	`text` text,
	PRIMARY KEY (`id`),
	KEY `idtree_idx` (`productId`),
	KEY `userId` (`userId`),
	CONSTRAINT `product_review_ibfk_3` FOREIGN KEY (`productId`) REFERENCES `product` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
	CONSTRAINT `product_review_ibfk_4` FOREIGN KEY (`userId`) REFERENCES `user` (`id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
