alter table class_section
	add content longtext null;

create table class_event_section_metadata
(
	id             int auto_increment primary key,
	classEventId   int  not null,
	classSectionId int  not null,
	link           text null,
	constraint class_event_section_metadata_class_event_id_fk
		foreign key (classEventId) references class_event (id)
			on update cascade on delete cascade,
	constraint class_event_section_metadata_class_section_id_fk
		foreign key (classSectionId) references class_section (id)
			on update cascade on delete cascade
)
	collate = utf8mb4_unicode_520_ci;

