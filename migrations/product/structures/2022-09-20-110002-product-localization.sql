CREATE TABLE IF NOT EXISTS `product_localization` (
	`id` int(11) NOT NULL AUTO_INCREMENT,
	`productId` int(11) NOT NULL,
	`mutationId` int(11) NOT NULL,
	`public` int(11) DEFAULT '0',
	`name` varchar(255) COLLATE utf8_bin DEFAULT NULL,
	`nameTitle` varchar(255) COLLATE utf8_bin DEFAULT NULL,
	`nameAnchor` varchar(255) COLLATE utf8_bin DEFAULT NULL,
	`description` text COLLATE utf8_bin,
	`keywords` text COLLATE utf8_bin,
	`annotation` text COLLATE utf8_bin,
	`content` text COLLATE utf8_bin,
	`videos` longtext COLLATE utf8_bin,
	`links` longtext COLLATE utf8_bin,
	`setup` longtext COLLATE utf8_bin,
	`customFieldsJson` longtext COLLATE utf8_bin,
	PRIMARY <PERSON> (`id`),
	UNIQUE KEY `mutationId_productId` (`mutationId`,`productId`),
	<PERSON><PERSON>Y `productId` (`productId`),
	CONSTRAINT `product_localization_ibfk_1` FOREIGN KEY (`productId`) REFERENCES `product` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
	CONSTRAINT `product_localization_ibfk_2` FOREIGN KEY (`mutationId`) REFERENCES `mutation` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 COLLATE=utf8_bin;
