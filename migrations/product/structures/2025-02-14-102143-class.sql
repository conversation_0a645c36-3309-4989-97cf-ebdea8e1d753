create table class_event
(
	id         int auto_increment
        primary key,
	mutationId int          not null,
	name       varchar(255) not null,
	productId  int          null,
	stateId    int          not null,
	`from`     datetime     null,
	`to`       datetime     null,
	`limit`    int          null,
	constraint class_event_mutation_id_fk
		foreign key (mutationId) references mutation (id)
			on update cascade on delete cascade,
	constraint class_event_product_id_fk
		foreign key (productId) references product (id)
			on update cascade on delete cascade,
	constraint class_event_state_id_fk
		foreign key (stateId) references state (id)
			on update cascade on delete cascade
);



create table class_section
(
	id         int auto_increment
        primary key,
	mutationId int          not null,
	name       varchar(255) not null,
	productId  int          null,
	constraint class_section_mutation_id_fk
		foreign key (mutationId) references mutation (id)
			on update cascade on delete cascade,
	constraint class_section_product_id_fk
		foreign key (productId) references product (id)
			on update cascade on delete cascade
);

