ALTER TABLE `product_image` DROP FOREIGN KEY `product_image_ibfk_4`;
ALTER TABLE `product_image` CHANGE COLUMN `imageId` `libraryImage` INT(11) NOT NULL COMMENT 'idfile' AFTER `productId`,
DROP INDEX `productId_imageId`, ADD UNIQUE INDEX `productId_imageId` (`productId`, `libraryImage`) USING BTREE,
DROP INDEX `imageId`,
	ADD INDEX `imageId` (`libraryImage`) USING BTREE,
	ADD CONSTRAINT `product_image_ibfk_4` FOREIGN KEY (`libraryImage`) REFERENCES `image` (`id`) ON UPDATE CASCADE ON DELETE CASCADE;

ALTER TABLE `image`
	ADD COLUMN `timeOfChange` DATETIME NULL DEFAULT NULL AFTER `alts`;

ALTER TABLE `product_image` DROP COLUMN `url`;



ALTER TABLE `product_image` DROP FOREIGN KEY `product_image_ibfk_4`;
<PERSON>TER TABLE `product_image` CHANGE COLUMN `libraryImage` `libraryImageId` INT(11) NOT NULL COMMENT 'idfile' AFTER `productId`,
DROP INDEX `productId_imageId`,
	ADD UNIQUE INDEX `productId_imageId` (`productId`, `libraryImageId`) USING BTREE,
DROP INDEX `imageId`,
	ADD INDEX `imageId` (`libraryImageId`) USING BTREE,
	ADD CONSTRAINT `product_image_ibfk_4` FOREIGN KEY (`libraryImageId`) REFERENCES `image` (`id`) ON UPDATE CASCADE ON DELETE CASCADE;
