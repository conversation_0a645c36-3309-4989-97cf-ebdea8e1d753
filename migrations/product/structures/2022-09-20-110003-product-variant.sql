CREATE TABLE IF NOT EXISTS `product_variant` (
	`id` int(11) NOT NULL AUTO_INCREMENT,
	`productId` int(11) NOT NULL,
	`param1ValueId` int(11) DEFAULT NULL,
	`param2ValueId` int(11) DEFAULT NULL,
	`ean` varchar(30) COLLATE utf8_czech_ci NOT NULL,
	`code` varchar(50) COLLATE utf8_czech_ci NOT NULL,
	`created` datetime DEFAULT NULL,
	`createdBy` int(11) DEFAULT NULL,
	`edited` datetime DEFAULT NULL,
	`editedBy` int(11) DEFAULT NULL,
	`sort` int(11) NOT NULL DEFAULT '0',
	`soldCount` int(11) NOT NULL,
	`isInDiscount` int(11) DEFAULT NULL,
	`extId` varchar(32) COLLATE utf8_czech_ci DEFAULT NULL,
	PRIMARY <PERSON>EY (`id`),
	UNIQUE KEY `productId_param1ValueId_param2ValueId` (`productId`,`param1ValueId`,`param2ValueId`),
	UNIQUE KEY `extId` (`extId`),
	KEY `param2ValueId` (`param2ValueId`),
	KEY `param1ValueId` (`param1ValueId`),
	CONSTRAINT `product_variant_ibfk_13` FOREIGN KEY (`param2ValueId`) REFERENCES `parameter_value` (`id`) ON DELETE NO ACTION ON UPDATE CASCADE,
	CONSTRAINT `product_variant_ibfk_14` FOREIGN KEY (`param1ValueId`) REFERENCES `parameter_value` (`id`) ON DELETE NO ACTION ON UPDATE CASCADE,
	CONSTRAINT `product_variant_ibfk_2` FOREIGN KEY (`productId`) REFERENCES `product` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 COLLATE=utf8_czech_ci;

