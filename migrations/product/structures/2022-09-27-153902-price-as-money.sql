ALTER TABLE `product_variant_price`
	CHANGE `price` `price_amount` decimal(18, 4),
	ADD `price_currency` char(3);

UPDATE `product_variant_price`
	JOIN `mutation` ON `mutation`.`id` = `product_variant_price`.`mutationId`
	SET `product_variant_price`.`price_currency` = `mutation`.`currency`;

ALTER TABLE `product_variant_price`
	MODIFY `price_amount` decimal(18, 4) NOT NULL,
	MODIFY `price_currency` char(3) NOT NULL;
