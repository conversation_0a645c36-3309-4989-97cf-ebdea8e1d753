ALTER TABLE `price_level`
	CHAN<PERSON> COLUMN `type` `type` VARCHAR(50) NOT NULL COLLATE 'utf8mb4_unicode_520_ci' AFTER `id`,
	CHANGE COLUMN `name` `name` VARCHAR(100) NOT NULL COLLATE 'utf8mb4_unicode_520_ci' AFTER `type`;

ALTER TABLE `product`
	CHANGE COLUMN `uid` `uid` VARCHAR(50) NULL DEFAULT NULL COLLATE 'utf8mb4_unicode_520_ci' AFTER `id`,
	CHANGE COLUMN `template` `template` VARCHAR(60) NOT NULL DEFAULT 'Product:detail' AFTER `uid`,
	<PERSON>AN<PERSON> COLUMN `internalName` `internalName` VARCHAR(255) NULL DEFAULT NULL COLLATE 'utf8mb4_unicode_520_ci' AFTER `publicTo`,
	CHANGE COLUMN `availability` `availability` VARCHAR(40) NOT NULL COLLATE 'utf8mb4_unicode_520_ci' AFTER `hideFirstImage`,
	<PERSON>AN<PERSON> COLUMN `mainCategoryUID` `mainCategoryUID` VARCHAR(250) NULL DEFAULT NULL COLLATE 'utf8mb4_unicode_520_ci' AFTER `soldCount`,
	CHANGE COLUMN `customFieldsJson` `customFieldsJson` TEXT NULL DEFAULT NULL COLLATE 'utf8mb4_unicode_520_ci' AFTER `mainCategoryUID`,
	CHANGE COLUMN `vats` `vats` TEXT NULL DEFAULT NULL COLLATE 'utf8mb4_unicode_520_ci' AFTER `edited`,
	CHANGE COLUMN `extId` `extId` VARCHAR(32) NULL DEFAULT NULL COLLATE 'utf8mb4_unicode_520_ci' AFTER `vats`;

ALTER TABLE `product_file`
	CHANGE COLUMN `name` `name` VARCHAR(250) NULL DEFAULT NULL COLLATE 'utf8mb4_unicode_520_ci' AFTER `fileId`,
	CHANGE COLUMN `url` `url` VARCHAR(250) NULL DEFAULT NULL COLLATE 'utf8mb4_unicode_520_ci' AFTER `name`;

ALTER TABLE `product_image`
	CHANGE COLUMN `name` `name` VARCHAR(250) NULL DEFAULT NULL COLLATE 'utf8mb4_unicode_520_ci' AFTER `imageId`,
	CHANGE COLUMN `url` `url` VARCHAR(250) NULL DEFAULT NULL COLLATE 'utf8mb4_unicode_520_ci' AFTER `name`,
	CHANGE COLUMN `variants` `variants` VARCHAR(255) NULL DEFAULT NULL COLLATE 'utf8mb4_unicode_520_ci' AFTER `sort`,
	CHANGE COLUMN `data` `data` LONGTEXT NULL DEFAULT NULL COLLATE 'utf8mb4_unicode_520_ci' AFTER `variants`,
	CHANGE COLUMN `extId` `extId` VARCHAR(32) NULL DEFAULT NULL COLLATE 'utf8mb4_unicode_520_ci' AFTER `data`;

ALTER TABLE `product_localization`
	CHANGE COLUMN `name` `name` VARCHAR(255) NULL DEFAULT NULL COLLATE 'utf8mb4_unicode_520_ci' AFTER `public`,
	CHANGE COLUMN `nameTitle` `nameTitle` VARCHAR(255) NULL DEFAULT NULL COLLATE 'utf8mb4_unicode_520_ci' AFTER `name`,
	CHANGE COLUMN `nameAnchor` `nameAnchor` VARCHAR(255) NULL DEFAULT NULL COLLATE 'utf8mb4_unicode_520_ci' AFTER `nameTitle`,
	CHANGE COLUMN `description` `description` TEXT NULL DEFAULT NULL COLLATE 'utf8mb4_unicode_520_ci' AFTER `nameAnchor`,
	CHANGE COLUMN `keywords` `keywords` TEXT NULL DEFAULT NULL COLLATE 'utf8mb4_unicode_520_ci' AFTER `description`,
	CHANGE COLUMN `annotation` `annotation` TEXT NULL DEFAULT NULL COLLATE 'utf8mb4_unicode_520_ci' AFTER `keywords`,
	CHANGE COLUMN `content` `content` TEXT NULL DEFAULT NULL COLLATE 'utf8mb4_unicode_520_ci' AFTER `annotation`,
	CHANGE COLUMN `setup` `setup` LONGTEXT NULL DEFAULT NULL COLLATE 'utf8mb4_unicode_520_ci' AFTER `content`,
	CHANGE COLUMN `customFieldsJson` `customFieldsJson` LONGTEXT NULL DEFAULT NULL COLLATE 'utf8mb4_unicode_520_ci' AFTER `setup`;

ALTER TABLE `product_product`
	CHANGE COLUMN `type` `type` VARCHAR(200) NULL DEFAULT NULL COLLATE 'utf8mb4_unicode_520_ci' AFTER `attachedProductId`;

ALTER TABLE `product_review`
	CHANGE COLUMN `name` `name` VARCHAR(250) NULL DEFAULT NULL COLLATE 'utf8mb4_unicode_520_ci' AFTER `isMain`,
	CHANGE COLUMN `email` `email` VARCHAR(250) NULL DEFAULT NULL COLLATE 'utf8mb4_unicode_520_ci' AFTER `name`,
	CHANGE COLUMN `text` `text` TEXT NULL DEFAULT NULL COLLATE 'utf8mb4_unicode_520_ci' AFTER `date`;

ALTER TABLE `product_variant`
	CHANGE COLUMN `ean` `ean` VARCHAR(30) NOT NULL COLLATE 'utf8mb4_unicode_520_ci' AFTER `param2ValueId`,
	CHANGE COLUMN `code` `code` VARCHAR(50) NOT NULL COLLATE 'utf8mb4_unicode_520_ci' AFTER `ean`,
	CHANGE COLUMN `extId` `extId` VARCHAR(32) NULL DEFAULT NULL COLLATE 'utf8mb4_unicode_520_ci' AFTER `isInDiscount`;

ALTER TABLE `product_variant_localization`
	CHANGE COLUMN `name` `name` VARCHAR(255) NULL DEFAULT NULL COLLATE 'utf8mb4_unicode_520_ci' AFTER `active`;

ALTER TABLE `product_variant_price`
	CHANGE COLUMN `price_currency` `price_currency` CHAR(3) NOT NULL COLLATE 'utf8mb4_unicode_520_ci' AFTER `price_amount`;

ALTER TABLE `product`
	CHANGE COLUMN `template` `template` VARCHAR(60) NOT NULL DEFAULT 'Product:detail' COLLATE 'utf8mb4_unicode_520_ci' AFTER `uid`;


ALTER TABLE `product_localization`
	CHANGE COLUMN `customContentJson` `customContentJson` LONGTEXT NULL DEFAULT NULL COLLATE 'utf8mb4_unicode_520_ci' AFTER `customFieldsJson`;
