SET @eshopPageId = 21;
SET @extId = 21;


INSERT INTO `product` ( `productTypeId`, `itemType`, `uid`, `template`, `dateCreated`, `syncTime`, `syncChecksum`, `publicFrom`, `publicTo`, `internalName`, `hideFirstImage`, `availability`, `isSet`, `isOld`, `isInPrepare`, `isElectronic`, `isDamaged`, `isStockNull`, `damagedType`, `damagedParentId`, `notSoldSeparately`, `discount`, `discountType`, `reviewAverage`, `isFreeTransport`, `isFreeTransportForced`, `freeTransportForcedFrom`, `freeTransportForcedTo`, `soldCount`, `customFieldsJson`, `editedTime`, `edited`, `vats`, `extId`, `score`, `erpScore`, `erpHeurekaPopularity`, `boostScore`, `boostScoreValid`, `similarBuyProductsCalculateAt`, `similarBuyProducts`, `deleted`, `giftDate`, `categoryMainPosition`) VALUES
	( 1, 'Kniha', NULL, ':Front:Product:detail', '2024-07-18 12:00:01', '2024-07-26 18:45:40', 'c91f7f4508c1ea55e1b35bdd0af73989', '2024-07-18 12:00:01', '2124-07-18 12:00:01', 'Vyučování jako dialog', 0, '', NULL, 0, 0, 0, 0, 0, NULL, NULL, 0, NULL, NULL, 0.00, 0, 0, NULL, NULL, 0, '{}', NULL, NULL, '{"1":"none","2":"none"}', @extId, -40, -8, 0, NULL, NULL, NULL, '{}', 0, NULL, NULL);

SET @productId = LAST_INSERT_ID();
INSERT INTO `product_localization` ( `productId`, `mutationId`, `public`, `name`, `nameTitle`, `nameAnchor`, `description`, `keywords`, `annotation`, `score`, `content`, `setup`, `customFieldsJson`, `customContentJson`) VALUES
	(@productId, 1, 1, 'Vyučování jako dialog', 'Vyučování jako dialog', 'Vyučování jako dialog', NULL, NULL, NULL, -40, NULL, '[]', '{"productImagePages":{},"product":[{"priceLabel":[{"validFrom":null,"validTo":null}]}]}', '{}');

SET @productLocalizationId = LAST_INSERT_ID();

INSERT INTO `alias` ( `alias`, `module`, `referenceId`, `mutationId`) VALUES (concat('product-', @productId),	'productLocalization',	@productLocalizationId,	1);


INSERT INTO `product_tree` ( `productId`, `treeId`, `sort`) VALUES
	( @productId, @eshopPageId, 0);

INSERT INTO `product_variant` (`productId`, `param1ValueId`, `param2ValueId`, `ean`, `isbn`, `code`, `created`, `createdBy`, `edited`, `editedBy`, `sort`, `soldCount`, `isInDiscount`, `extId`) VALUES
	(@productId, NULL, NULL, '9788024715414', '978-80-247-1541-4', 'D0116681', '2024-07-18 12:00:01', NULL, '2024-07-18 12:00:01', NULL, 0, 0, NULL, @extId);

SET @productVariantId = LAST_INSERT_ID();


INSERT INTO `product_variant_localization` ( `variantId`, `mutationId`, `active`, `name`) VALUES
	(@productVariantId, 1, 1, NULL);
SET @productVariantLocalizationId = LAST_INSERT_ID();



INSERT INTO `product_variant_price` ( `mutationId`, `priceLevelId`, `realOrigPriceDPH`, `productId`, `productVariantId`, `price_amount`, `price_currency`, `validFrom`, `validTo`) VALUES (1, 1, 0.00, @productId, @productVariantId, 128.0000, 'CZK', NULL, NULL);
INSERT INTO `product_variant_price` ( `mutationId`, `priceLevelId`, `realOrigPriceDPH`, `productId`, `productVariantId`, `price_amount`, `price_currency`, `validFrom`, `validTo`) VALUES (1, 2, 0.00, @productId, @productVariantId, 128.0000, 'CZK', NULL, NULL);
INSERT INTO `product_variant_price` ( `mutationId`, `priceLevelId`, `realOrigPriceDPH`, `productId`, `productVariantId`, `price_amount`, `price_currency`, `validFrom`, `validTo`) VALUES (1, 5, 0.00, @productId, @productVariantId, 153.6400, 'CZK', NULL, NULL);
INSERT INTO `product_variant_price` ( `mutationId`, `priceLevelId`, `realOrigPriceDPH`, `productId`, `productVariantId`, `price_amount`, `price_currency`, `validFrom`, `validTo`) VALUES (1, 3, 0.00, @productId, @productVariantId, 0.0000, 'CZK', NULL, NULL);
INSERT INTO `product_variant_price` ( `mutationId`, `priceLevelId`, `realOrigPriceDPH`, `productId`, `productVariantId`, `price_amount`, `price_currency`, `validFrom`, `validTo`) VALUES (1, 4, 0.00, @productId, @productVariantId, 0.0000, 'CZK', NULL, NULL);
INSERT INTO `product_variant_price` ( `mutationId`, `priceLevelId`, `realOrigPriceDPH`, `productId`, `productVariantId`, `price_amount`, `price_currency`, `validFrom`, `validTo`) VALUES (1, 7, 0.00, @productId, @productVariantId, 96.7900, 'CZK', NULL, NULL);


INSERT INTO `product_variant_price_log` (`mutationId`, `productVariantId`, `priceLevelId`, `realOrigPrice`, `origPrice`, `salePrice`, `createdAt`, `lastSaleAt`) VALUES (1, @productVariantId, 1, 128.00, 128.00, 128.00, '2024-09-02 12:18:35', NULL);
INSERT INTO `product_variant_price_log` (`mutationId`, `productVariantId`, `priceLevelId`, `realOrigPrice`, `origPrice`, `salePrice`, `createdAt`, `lastSaleAt`) VALUES (1, @productVariantId, 2, 128.00, 128.00, 128.00, '2024-09-02 12:18:35', NULL);


INSERT INTO `supplier` (`erpId`, `erpCode`, `internalName`, `customFieldsJson`, `editedBy`, `editedTime`, `syncChecksum`, `syncTime`) VALUES ( 2, '0171124', 'Euromedia', '{"delivery":[{"standardDays":"1","closingAddDays":"1","closingTime":[{"0":"16","1":"16","2":"16","3":"16","4":"16"}]}]}', 34, '2024-09-23 15:40:23', 'a3c26ca8002a63674f9c979e93a9b7c8', '2024-08-02 11:32:15');
SET @supplierId = LAST_INSERT_ID();


INSERT INTO `stock_supplies` ( `stockId`, `variantId`, `amount`, `lastImport`, `lastOnStock`, `stockDate`, `supplierId`, `deliveryDelay`) VALUES (1, @productVariantId, 5, '2024-09-02 12:26:21', '2024-11-13 12:11:37', NULL, NULL, 0);
INSERT INTO `stock_supplies` ( `stockId`, `variantId`, `amount`, `lastImport`, `lastOnStock`, `stockDate`, `supplierId`, `deliveryDelay`) VALUES (2, @productVariantId, 2, '2024-09-02 12:26:21', '2024-11-10 12:11:51', NULL, @supplierId, 0);


-- fix country for mutation
UPDATE `mutation_state` SET `stateId`=1 WHERE  `id`=1;

