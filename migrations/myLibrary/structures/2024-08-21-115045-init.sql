CREATE TABLE `my_library`
(
	`id`     int(11) NOT NULL AUTO_INCREMENT,
	`uid`    varchar(50) NOT NULL,
	`userId` int(11) NOT NULL,
	PRIMARY KEY (`id`),
	<PERSON><PERSON><PERSON>      `my_library_user_id_fk` (`userId`),
	CONSTRAINT `my_library_user_id_fk` FOREIGN KEY (`userId`) REFERENCES `user` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

create table my_library_product
(
	id          int auto_increment,
	productId   int                           not null,
	myLibraryId int                           not null,
	dateAdded   datetime default CURRENT_DATE not null,
	constraint my_library_product_pk
		primary key (id),
	constraint my_library_product_my_library_id_fk
		foreign key (myLibraryId) references my_library (id)
			on update cascade on delete cascade,
	constraint my_library_product_product_id_fk
		foreign key (productId) references product (id)
			on update cascade on delete cascade
)ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
