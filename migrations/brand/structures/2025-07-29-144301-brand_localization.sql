CREATE TABLE IF NOT EXISTS `brand_localization`
(
	`id`                  int(11) NOT NULL AUTO_INCREMENT,
	`mutationId`          int(11) NOT NULL,
	`brandId`             int(11) NOT NULL                              DEFAULT '1',
	`name`                varchar(250) COLLATE 'utf8mb4_unicode_520_ci' DEFAULT NULL,
	`nameAnchor`          varchar(250) COLLATE 'utf8mb4_unicode_520_ci' DEFAULT NULL,
	`nameTitle`           varchar(250) COLLATE 'utf8mb4_unicode_520_ci' DEFAULT NULL,
	`shortDescription`    text COLLATE 'utf8mb4_unicode_520_ci',
	`description`         longtext COLLATE 'utf8mb4_unicode_520_ci',
	`keywords`            text COLLATE 'utf8mb4_unicode_520_ci',
	`title`               varchar(250) COLLATE 'utf8mb4_unicode_520_ci' DEFAULT NULL,
	`public`              int(11)                                       DEFAULT NULL,
	`publicFrom`          datetime                                      DEFAULT NULL,
	`publicTo`            datetime                                      DEFAULT NULL,
	`forceNoIndex`        int(11)                                       DEFAULT '0',
	`hideInSearch`        int(11)                                       DEFAULT '0',
	`hideInSitemap`       int(11)                                       DEFAULT '0',
	`edited`              int(11)                                       DEFAULT NULL,
	`editedTime`          datetime                                      DEFAULT NULL,
	`customFieldsJson`    longtext COLLATE 'utf8mb4_unicode_520_ci',
	`customContentJson`   longtext COLLATE 'utf8mb4_unicode_520_ci',
	`brandParameterValue` int(11)                                       DEFAULT NULL,
	PRIMARY KEY (`id`) USING BTREE,
	UNIQUE KEY `mutationId_brandId` (`mutationId`, `brandId`),
	KEY `FK_brand_mutation` (`mutationId`) USING BTREE,
	KEY `FK_brand_localization_brand` (`brandId`) USING BTREE,
	CONSTRAINT `FK_brand_localization_brand` FOREIGN KEY (`brandId`) REFERENCES `brand` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
	CONSTRAINT `FK_brand_localization_mutation` FOREIGN KEY (`mutationId`) REFERENCES `mutation` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
	CONSTRAINT `FK_brand_localization_brand_parameter_value` FOREIGN KEY (`brandParameterValue`) REFERENCES `parameter_value` (`id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE = InnoDB
  AUTO_INCREMENT = 3
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_520_ci;
