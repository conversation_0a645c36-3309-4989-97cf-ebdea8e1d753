INSERT INTO `user` (`id`, `email`, `password`, `role`, `firstname`, `lastname`, `phone`, `street`, `city`, `zip`,
					`stateId`, `company`, `ic`, `dic`, `created`, `createdTime`, `edited`, `editedTime`, `lastLogin`,
					`customAddress<PERSON>son`, `orderCount`, `priceLevelId`, `customFields<PERSON>son`, `googleId`)
VALUES (7, '<EMAIL>', NULL, 'developer', 'ERP', 'Adamint', '', '', '', '', 1, '', '', '', NULL, NULL, NULL,
		NULL, NULL, NULL, 0, 1, NULL, NULL)
ON DUPLICATE KEY UPDATE email=email;

INSERT INTO `user_mutation` (`userId`, `mutationId`, `newsletter`)
VALUES (7, 1, 0);
