INSERT INTO `user` ( `email`, `password`, `role`, `firstname`, `lastname`, `phone`, `street`, `city`, `zip`, `stateId`, `company`, `ic`, `dic`, `created`, `createdTime`, `edited`, `editedTime`, `lastLogin`, `customAddressJson`, `orderCount`, `priceLevelId`, `customFieldsJson`, `googleId`) VALUES ('<EMAIL>', '$2y$10$fliC2SobBP53YHHgol62Vex0fUCqjEduU2J4fsUxS5mT5s7BanAaa', 'developer', 'Jakub', 'Mikita', '', '', '', '', 1, '', '', '', NULL, NULL, NULL, '2024-01-22 16:40:34', NULL, NULL, 0, 1, NULL, NULL)
	ON DUPLICATE KEY UPDATE email=email;

set @kuboId = LAST_INSERT_ID();

INSERT INTO `user_mutation` (`userId`, `mutationId`, `newsletter`) VALUES (@kuboId, 1, 0);

