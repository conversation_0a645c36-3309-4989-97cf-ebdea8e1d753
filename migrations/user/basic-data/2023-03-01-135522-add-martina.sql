INSERT INTO `user` ( `email`, `password`, `role`, `firstname`, `lastname`, `phone`, `street`, `city`, `zip`, `stateId`, `company`, `ic`, `dic`, `created`, `createdTime`, `edited`, `editedTime`, `lastLogin`, `customAddressJson`, `orderCount`, `priceLevelId`, `customFieldsJson`, `googleId`) VALUES ('<EMAIL>', '$2y$10$unREJLZVvjmmwuLjhYSRUucnZxJS/QPjNh08uGMiyMQsSnYwQ7whm', 'developer', 'Martina', '<PERSON><PERSON><PERSON><PERSON>', '', '', '', '', 1, '', '', '', NULL, NULL, NULL, '2023-03-01 09:40:34', NULL, NULL, 0, 1, NULL, NULL)
	ON DUPLICATE KEY UPDATE email=email;

set @martinaId = LAST_INSERT_ID();

INSERT INTO `user_mutation` (`userId`, `mutationId`, `newsletter`) VALUES (@martinaId, 1, 0);

