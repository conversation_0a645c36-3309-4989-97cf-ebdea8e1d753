INSERT INTO `tree`  (`id`, `rootId`, `parentId`, `level`, `path`, `sort`, `last`, `public`, `uid`, `created`, `createdTime`, `createdTimeOrder`, `edited`, `editedTime`, `template`, `type`, `publicFrom`, `publicTo`, `name`, `nameTitle`, `nameAnchor`, `annotation`, `description`, `keywords`, `content`, `hideFirstImage`, `links`, `seoTitleFilter`, `seoAnnotationFilter`, `seoDescriptionFilter`, `videos`, `hideCrossroad`, `hideInSitemap`, `forceNoIndex`, `hideInSearch`, `showContactForm`, `showOnHomepage`, `customFieldsJson`, `customContentJson`, `customContentSchemeJson`, `productAttachedId`, `hasLinkedCategories`) VALUES (26, 1, 72, 2, '1|72|', 8, 1, 1, 'resetPassword', 0, '2018-02-08 02:46:27', '2018-02-08 02:46:27', 12, '2021-06-21 18:22:30', 'User:resetPassword', 'common', '2018-02-08 02:46:27', '2118-02-08 02:46:27', 'Reset hesla', 'Změna hesla', 'Změna hesla', '', '', '', '', 0, '', '', '', '', '', 0, 1, 0, 0, 0, 0, '[]', NULL, NULL, NULL, NULL);
INSERT INTO `alias` (`id`, `alias`, `module`, `referenceId`, `mutationId`)
	VALUES (229, 'reset-hesla', 'tree', 26, 1);

INSERT INTO `tree`  (`id`, `rootId`, `parentId`, `level`, `path`, `sort`, `last`, `public`, `uid`, `created`, `createdTime`, `createdTimeOrder`, `edited`, `editedTime`, `template`, `type`, `publicFrom`, `publicTo`, `name`, `nameTitle`, `nameAnchor`, `annotation`, `description`, `keywords`, `content`, `hideFirstImage`, `links`, `seoTitleFilter`, `seoAnnotationFilter`, `seoDescriptionFilter`, `videos`, `hideCrossroad`, `hideInSitemap`, `forceNoIndex`, `hideInSearch`, `showContactForm`, `showOnHomepage`, `customFieldsJson`, `customContentJson`, `customContentSchemeJson`, `productAttachedId`, `hasLinkedCategories`) VALUES (37, 1, 72, 2, '1|72|', 6, 1, 1, 'userLogin', 36, '2017-03-15 15:05:46', '2017-03-15 15:05:46', 2319, '2018-09-05 17:42:28', 'User:login', 'common', '2017-03-15 15:05:46', '2100-01-01 00:00:00', 'Přihlášení', 'Přihlášení', 'Přihlášení', '', '', '', '', 0, '', NULL, NULL, NULL, '', 0, 0, 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `alias` (`id`, `alias`, `module`, `referenceId`, `mutationId`)
	VALUES (224, 'prihlaseni', 'tree', 37, 1);

INSERT INTO `tree`  (`id`, `rootId`, `parentId`, `level`, `path`, `sort`, `last`, `public`, `uid`, `created`, `createdTime`, `createdTimeOrder`, `edited`, `editedTime`, `template`, `type`, `publicFrom`, `publicTo`, `name`, `nameTitle`, `nameAnchor`, `annotation`, `description`, `keywords`, `content`, `hideFirstImage`, `links`, `seoTitleFilter`, `seoAnnotationFilter`, `seoDescriptionFilter`, `videos`, `hideCrossroad`, `hideInSitemap`, `forceNoIndex`, `hideInSearch`, `showContactForm`, `showOnHomepage`, `customFieldsJson`, `customContentJson`, `customContentSchemeJson`, `productAttachedId`, `hasLinkedCategories`) VALUES (72, 1, 1, 1, '1|', 6, 0, 1, 'userSection', 1, '2013-11-06 15:34:00', '2013-11-06 15:34:00', 12, '2021-06-22 08:46:52', 'User:default', 'common', '2013-11-06 15:34:00', '2100-01-01 00:00:00', 'Uživatelská sekce', 'Uživatelská sekce', 'Uživatelská sekce', '', '', '', '<h2 class="text-center">Děkujeme za přihlášení</h2>\r\n<p class="text-center">Vítejte ve svém účtu.</p>', 0, 'http://wwww|www|', '', '', '', 'https://www.youtube.com/watch?v=CPjyJiPNnmk|', 0, 0, 0, 0, 0, 0, '{"userMenuUnloggedUser":[{"tree":"37"},{"tree":"76"},{"tree":"74"},{"tree":""}],"userMenuLoggedUser":[{"tree":"72"},{"tree":"75"},{"tree":"405"}],"userSideMenu":[{"tree":"72"},{"tree":"75"},{"tree":"413"},{"tree":"36"},{"tree":"405"}]}', NULL, NULL, NULL, NULL);
INSERT INTO `alias` (`id`, `alias`, `module`, `referenceId`, `mutationId`)
	VALUES (211, 'prihlaseny', 'tree', 72, 1);

INSERT INTO `tree`  (`id`, `rootId`, `parentId`, `level`, `path`, `sort`, `last`, `public`, `uid`, `created`, `createdTime`, `createdTimeOrder`, `edited`, `editedTime`, `template`, `type`, `publicFrom`, `publicTo`, `name`, `nameTitle`, `nameAnchor`, `annotation`, `description`, `keywords`, `content`, `hideFirstImage`, `links`, `seoTitleFilter`, `seoAnnotationFilter`, `seoDescriptionFilter`, `videos`, `hideCrossroad`, `hideInSitemap`, `forceNoIndex`, `hideInSearch`, `showContactForm`, `showOnHomepage`, `customFieldsJson`, `customContentJson`, `customContentSchemeJson`, `productAttachedId`, `hasLinkedCategories`) VALUES (74, 1, 72, 2, '1|72|', 7, 1, 1, 'lostPassword', 1, '2013-11-06 15:36:28', '2013-11-06 15:36:28', 9, '2021-06-25 11:31:04', 'User:lostPassword', 'common', '2013-11-06 15:36:28', '2100-01-01 00:00:00', 'Zapomenuté heslo', 'Zapomenuté heslo', 'Zapomenuté heslo', 'Zapomněli jste své heslo? Nevadí, na e-mail vám pošleme nové.\r\nStačí zadat e-mail, který jste uvedli při registraci.\r\n', '', '', '', 0, '', NULL, NULL, NULL, '', 0, 0, 0, 0, 0, 0, '[]', NULL, NULL, NULL, NULL);
INSERT INTO `alias` (`id`, `alias`, `module`, `referenceId`, `mutationId`)
	VALUES (219, 'zapomenute-heslo', 'tree', 74, 1);

INSERT INTO `tree`  (`id`, `rootId`, `parentId`, `level`, `path`, `sort`, `last`, `public`, `uid`, `created`, `createdTime`, `createdTimeOrder`, `edited`, `editedTime`, `template`, `type`, `publicFrom`, `publicTo`, `name`, `nameTitle`, `nameAnchor`, `annotation`, `description`, `keywords`, `content`, `hideFirstImage`, `links`, `seoTitleFilter`, `seoAnnotationFilter`, `seoDescriptionFilter`, `videos`, `hideCrossroad`, `hideInSitemap`, `forceNoIndex`, `hideInSearch`, `showContactForm`, `showOnHomepage`, `customFieldsJson`, `customContentJson`, `customContentSchemeJson`, `productAttachedId`, `hasLinkedCategories`) VALUES (75, 1, 72, 2, '1|72|', 1, 1, 1, 'userProfil', 1, '2013-11-06 17:17:48', '2013-11-06 17:17:48', 12, '2021-05-18 12:50:11', 'User:profil', 'common', '2013-11-06 17:17:48', '2100-01-01 00:00:00', 'Můj účet', 'Můj účet', 'Můj účet', '', '', '', '', 0, '', '', '', '', '', 0, 1, 0, 0, 0, 0, '[]', NULL, NULL, NULL, NULL);
INSERT INTO `alias` (`id`, `alias`, `module`, `referenceId`, `mutationId`)
	VALUES (166, 'profil', 'tree', 75, 1);

INSERT INTO `tree`  (`id`, `rootId`, `parentId`, `level`, `path`, `sort`, `last`, `public`, `uid`, `created`, `createdTime`, `createdTimeOrder`, `edited`, `editedTime`, `template`, `type`, `publicFrom`, `publicTo`, `name`, `nameTitle`, `nameAnchor`, `annotation`, `description`, `keywords`, `content`, `hideFirstImage`, `links`, `seoTitleFilter`, `seoAnnotationFilter`, `seoDescriptionFilter`, `videos`, `hideCrossroad`, `hideInSitemap`, `forceNoIndex`, `hideInSearch`, `showContactForm`, `showOnHomepage`, `customFieldsJson`, `customContentJson`, `customContentSchemeJson`, `productAttachedId`, `hasLinkedCategories`) VALUES (76, 1, 72, 2, '1|72|', 5, 1, 1, 'registration', 1, '2013-11-07 06:49:48', '2013-11-07 06:49:48', 4, '2018-10-23 17:22:53', 'User:registration', 'common', '2013-11-07 06:49:48', '2100-01-01 00:00:00', 'Registrace', 'Registrace', 'Registrace', '', '', '', '', 0, '', NULL, NULL, NULL, '', 0, 0, 0, 0, 0, 0, '[]', NULL, NULL, NULL, NULL);
INSERT INTO `alias` (`id`, `alias`, `module`, `referenceId`, `mutationId`)
	VALUES (172, 'registrace', 'tree', 76, 1);

INSERT INTO `tree`  (`id`, `rootId`, `parentId`, `level`, `path`, `sort`, `last`, `public`, `uid`, `created`, `createdTime`, `createdTimeOrder`, `edited`, `editedTime`, `template`, `type`, `publicFrom`, `publicTo`, `name`, `nameTitle`, `nameAnchor`, `annotation`, `description`, `keywords`, `content`, `hideFirstImage`, `links`, `seoTitleFilter`, `seoAnnotationFilter`, `seoDescriptionFilter`, `videos`, `hideCrossroad`, `hideInSitemap`, `forceNoIndex`, `hideInSearch`, `showContactForm`, `showOnHomepage`, `customFieldsJson`, `customContentJson`, `customContentSchemeJson`, `productAttachedId`, `hasLinkedCategories`) VALUES (405, 1, 72, 2, '1|72|', 1, 1, 1, 'userLogout', 0, '2021-05-19 12:36:35', '2021-05-19 12:36:35', 12, '2021-05-19 12:37:10', 'User:default', 'common', '2021-05-19 12:36:35', '2121-05-19 12:36:35', 'Odhlášení', 'Odhlásit', 'Odhlásit', '', '', '', '', 0, '', '', '', '', '', 0, 1, 1, 1, 0, 0, '[]', NULL, NULL, NULL, NULL);
INSERT INTO `alias` (`id`, `alias`, `module`, `referenceId`, `mutationId`)
	VALUES (2145, 'odhlasit', 'tree', 405, 1);

INSERT INTO `tree`  (`id`, `rootId`, `parentId`, `level`, `path`, `sort`, `last`, `public`, `uid`, `created`, `createdTime`, `createdTimeOrder`, `edited`, `editedTime`, `template`, `type`, `publicFrom`, `publicTo`, `name`, `nameTitle`, `nameAnchor`, `annotation`, `description`, `keywords`, `content`, `hideFirstImage`, `links`, `seoTitleFilter`, `seoAnnotationFilter`, `seoDescriptionFilter`, `videos`, `hideCrossroad`, `hideInSitemap`, `forceNoIndex`, `hideInSearch`, `showContactForm`, `showOnHomepage`, `customFieldsJson`, `customContentJson`, `customContentSchemeJson`, `productAttachedId`, `hasLinkedCategories`) VALUES (413, 1, 72, 2, '1|72|', 1, 1, 1, 'userChangePassword', 0, '2021-06-21 18:21:49', '2021-06-21 18:21:49', 12, '2021-06-21 19:06:46', 'User:default', 'common', '2021-06-21 18:21:49', '2121-06-21 18:21:49', 'Změna hesla', 'Změna hesla', 'Změna hesla', '', '', '', '', 0, '', NULL, NULL, NULL, '', 0, 1, 0, 1, 0, 0, '[]', NULL, NULL, NULL, NULL);
INSERT INTO `alias` (`id`, `alias`, `module`, `referenceId`, `mutationId`)
	VALUES (2159, 'zmena-hesla', 'tree', 413, 1);
