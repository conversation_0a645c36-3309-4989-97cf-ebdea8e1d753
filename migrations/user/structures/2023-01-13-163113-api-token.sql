CREATE TABLE IF NOT EXISTS `api_token` (
	`id` int(11) NOT NULL PRIMARY KEY AUTO_INCREMENT,
	`token` varchar(255) NOT NULL COLLATE utf8mb4_bin,
	`description` varchar(255) NOT NULL COLLATE utf8mb4_bin,
	`issuedById` int(11) NOT NULL REFERENCES `user` (`id`) ON DELETE CASCADE,
	`issuedAt` datetime NOT NULL,
	`expiresAt` datetime,
	`revokedAt` datetime,
	`scope` text NOT NULL COLLATE utf8mb4_bin
) ENGINE=InnoDB;
