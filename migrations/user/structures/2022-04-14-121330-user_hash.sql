
CREATE TABLE IF NOT EXISTS `user_hash` (
	`id` int(11) NOT NULL AUTO_INCREMENT,
	`userId` int(11) NOT NULL,
	`type` varchar(100) COLLATE utf16_czech_ci NOT NULL,
	`hash` varchar(255) COLLATE utf16_czech_ci NOT NULL,
	`createdTime` datetime NOT NULL,
	`validTo` datetime NOT NULL,
	`valid` tinyint(1) NOT NULL DEFAULT '1',
	`usedTime` datetime DEFAULT NULL,
	`data` text COLLATE utf16_czech_ci,
	<PERSON>IMAR<PERSON> KEY (`id`),
	KEY `userId` (`userId`),
	CONSTRAINT `user_hash_ibfk_1` FOREIGN KEY (`userId`) REFERENCES `user` (`id`) ON DELETE CASCADE
	) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf16 COLLATE=utf16_czech_ci;

