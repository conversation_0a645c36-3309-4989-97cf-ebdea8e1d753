-- Add smartEmailingContactId, allowedMailing, and userId to newsletter_email table

-- Add new columns to newsletter_email table
ALTER TABLE `newsletter_email`
ADD COLUMN `smartEmailingContactId` varchar(255) DEFAULT NULL COMMENT 'SmartEmailing contact ID for this newsletter email',
ADD COLUMN `allowedMailing` tinyint(1) NOT NULL DEFAULT 1 COMMENT 'Whether this newsletter email allows receiving marketing emails',
ADD COLUMN `userId` int(11) DEFAULT NULL COMMENT 'Associated user ID (can be null for anonymous subscribers)';

-- <PERSON>reate indexes for better performance
CREATE INDEX `idx_newsletter_email_smartemailing_contact_id` ON `newsletter_email` (`smartEmailingContactId`);
CREATE INDEX `idx_newsletter_email_allowed_mailing` ON `newsletter_email` (`allowedMailing`);
CREATE INDEX `idx_newsletter_email_user_id` ON `newsletter_email` (`userId`);

-- Add foreign key constraint to link newsletter_email to user table
ALTER TABLE `newsletter_email`
ADD CONSTRAINT `fk_newsletter_email_user` F<PERSON><PERSON><PERSON><PERSON> KEY (`userId`) REFERENCES `user` (`id`) ON DELETE SET NULL;

-- Create unique index to prevent duplicate entries for same email and user combination
CREATE UNIQUE INDEX `unique_email_user` ON `newsletter_email` (`email`, `userId`);

-- Newsletter Email indexes for SmartEmailing (only if column exists)
ALTER TABLE newsletter_email ADD INDEX IF NOT EXISTS idx_smartemailing_contact_id (smartEmailingContactId);
ALTER TABLE newsletter_email ADD INDEX IF NOT EXISTS idx_allowed_mailing (allowedMailing);
