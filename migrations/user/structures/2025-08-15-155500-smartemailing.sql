-- SmartEmailing Custom Fields table
CREATE TABLE `smartemailing_custom_field` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `smartEmailingFieldId` int(11) NOT NULL COMMENT 'SmartEmailing API field ID (e.g., 3, 51)',
  `name` varchar(100) NOT NULL COMMENT 'Field name (e.g., interests, preferences)',
  `type` enum('checkbox','text','select','number','date','email','url') NOT NULL DEFAULT 'text' COMMENT 'Field type',
  `fieldOptions` text COMMENT 'JSON array of predefined options for checkbox/select fields',
  `description` text COMMENT 'Field description',
  `isActive` tinyint(1) NOT NULL DEFAULT 1 COMMENT 'Whether field is active for sync',
  `createdAt` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updatedAt` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `smartEmailingFieldId` (`smartEmailingFieldId`),
  UNIQUE KEY `name` (`name`),
  KEY `isActive` (`isActive`),
  KEY `type` (`type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='SmartEmailing custom field definitions';

CREATE TABLE IF NOT EXISTS `smartemailing_contact_list` (
															`id` int(11) NOT NULL AUTO_INCREMENT,
	`smartEmailingListId` int(11) NOT NULL,
	`name` varchar(255) NOT NULL,
	`publicname` varchar(255) DEFAULT NULL,
	`sendername` varchar(255) DEFAULT NULL,
	`senderemail` varchar(255) DEFAULT NULL,
	`replyto` varchar(255) DEFAULT NULL,
	`description` text DEFAULT NULL,
	`isActive` tinyint(1) NOT NULL DEFAULT '1',
	`createdAt` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
	`updatedAt` datetime DEFAULT NULL,
	PRIMARY KEY (`id`),
	UNIQUE KEY `smartEmailingListId` (`smartEmailingListId`),
	KEY `isActive` (`isActive`),
	KEY `name` (`name`)
	) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


-- customFieldsJson column already exists, migrate interestsJson data to userInterests format
-- Transform {"dog": "2025-08-13", "cat": "2025-08-13"} to {"userInterests": [{"interest": "dog"}, {"interest": "cat"}]}

UPDATE `user`
SET `customFieldsJson` =
		CASE
			-- Zkontrolujeme, zda vůbec nějaké zájmy existují
			WHEN `interestsJson` IS NOT NULL AND `interestsJson` NOT IN ('', '{}', '[]') THEN
				-- Sestavíme celý JSON objekt jako string. Všimněte si CONCAT() okolo.
				-- Toto vytvoří např. '{"userInterests":[{"interest":"dog"}]}'
				CASE
					WHEN `customFieldsJson` IS NULL OR `customFieldsJson` IN ('', '{}')
						THEN -- Pokud je původní JSON prázdný, prostě vložíme ten náš nově vytvořený
						CONCAT('{"userInterests": [',
							   CONCAT_WS(',',
										 CASE WHEN JSON_EXTRACT(`interestsJson`, '$.dog') IS NOT NULL THEN JSON_OBJECT('interest', 'dog') END,
										 CASE WHEN JSON_EXTRACT(`interestsJson`, '$.cat') IS NOT NULL THEN JSON_OBJECT('interest', 'cat') END,
										 CASE WHEN JSON_EXTRACT(`interestsJson`, '$.aquaristics') IS NOT NULL THEN JSON_OBJECT('interest', 'aquaristics') END,
										 CASE WHEN JSON_EXTRACT(`interestsJson`, '$.reptile') IS NOT NULL THEN JSON_OBJECT('interest', 'reptile') END
							   ),
							   ']}')
					ELSE -- Pokud původní JSON existuje, sloučíme ho s naším novým objektem
						JSON_MERGE_PATCH(`customFieldsJson`,
										 CONCAT('{"userInterests": [',
												CONCAT_WS(',',
														  CASE WHEN JSON_EXTRACT(`interestsJson`, '$.dog') IS NOT NULL THEN JSON_OBJECT('interest', 'dog') END,
														  CASE WHEN JSON_EXTRACT(`interestsJson`, '$.cat') IS NOT NULL THEN JSON_OBJECT('interest', 'cat') END,
														  CASE WHEN JSON_EXTRACT(`interestsJson`, '$.aquaristics') IS NOT NULL THEN JSON_OBJECT('interest', 'aquaristics') END,
														  CASE WHEN JSON_EXTRACT(`interestsJson`, '$.reptile') IS NOT NULL THEN JSON_OBJECT('interest', 'reptile') END
												),
												']}')
						)
					END
			-- Pokud žádné zájmy nejsou, necháme původní hodnotu
			ELSE `customFieldsJson`
			END
WHERE `interestsJson` IS NOT NULL AND `interestsJson` NOT IN ('', '{}', '[]');

-- Drop old columns (separate statements for MariaDB compatibility)
ALTER TABLE `user` DROP COLUMN IF EXISTS `interestsJson`;
ALTER TABLE `user` DROP COLUMN IF EXISTS `interestsUpdated`;

-- Add SmartEmailing sync columns to user table (removing separate sync table approach)
ALTER TABLE `user`
	ADD COLUMN `smartEmailingContactId` varchar(255) DEFAULT NULL COMMENT 'SmartEmailing contact ID',
ADD COLUMN `smartEmailingLastSync` datetime DEFAULT NULL COMMENT 'Timestamp of last successful sync',
ADD COLUMN `smartEmailingSyncStatus` enum('pending','success','error','disabled') NOT NULL DEFAULT 'pending' COMMENT 'Current sync status',
	ADD COLUMN `allowedMailing` tinyint(1) NOT NULL DEFAULT 1 COMMENT 'Whether user allows receiving marketing emails';

-- Create indexes for better performance
CREATE INDEX `idx_user_smartemailing_sync_status` ON `user` (`smartEmailingSyncStatus`);
CREATE INDEX `idx_user_smartemailing_last_sync` ON `user` (`smartEmailingLastSync`);
CREATE INDEX `idx_user_smartemailing_contact_id` ON `user` (`smartEmailingContactId`);
CREATE INDEX `idx_user_allowed_mailing` ON `user` (`allowedMailing`);

-- This will be populated by the sync command, but we can set a safe default
UPDATE `user` SET `allowedMailing` = 1 WHERE `allowedMailing` IS NULL;

-- Add SmartEmailing option mapping to user_interest table
ALTER TABLE `user_interest`
	ADD COLUMN `syncToSmartEmailing` tinyint(1) NOT NULL DEFAULT 0 COMMENT 'Whether this interest should sync to SmartEmailing',
	ADD COLUMN `smartEmailingOptionId` int(11) NULL COMMENT 'SmartEmailing custom field option ID',
ADD COLUMN `smartEmailingOrder` int(11) NULL COMMENT 'SmartEmailing order',
ADD COLUMN `smartEmailingOptionName` varchar(100) NULL COMMENT 'SmartEmailing option name (for reference)',
ADD COLUMN `smartEmailingUpdated` datetime NULL COMMENT 'Last update timestamp for SmartEmailing sync',
ADD KEY `smartEmailingOptionId` (`smartEmailingOptionId`),
ADD KEY `syncToSmartEmailing` (`syncToSmartEmailing`);

CREATE INDEX `idx_user_interest_sync` ON `user_interest` (`syncToSmartEmailing`);

-- Create smartemailing_unsubscribed_contact table
CREATE TABLE `smartemailing_unsubscribed_contact` (
													  `id` int(11) NOT NULL AUTO_INCREMENT,
													  `contactListId` int(11) DEFAULT NULL,
													  `userId` int(11) DEFAULT NULL,
													  `email` varchar(255) NOT NULL,
													  `smartEmailingContactId` varchar(255) DEFAULT NULL,
													  `unsubscribeReason` enum('unsubscribed','blacklisted','bounced','complained') NOT NULL DEFAULT 'unsubscribed',
													  `unsubscribedAt` datetime NOT NULL,
													  `isBlacklisted` tinyint(1) NOT NULL DEFAULT 0,
													  `createdAt` datetime NOT NULL,
													  `updatedAt` datetime DEFAULT NULL,
													  PRIMARY KEY (`id`),
													  UNIQUE KEY `unique_email_contactlist` (`email`, `contactListId`),
													  KEY `idx_email` (`email`),
													  KEY `idx_smartemailing_contact_id` (`smartEmailingContactId`),
													  KEY `idx_unsubscribe_reason` (`unsubscribeReason`),
													  KEY `idx_unsubscribed_at` (`unsubscribedAt`),
													  KEY `idx_is_blacklisted` (`isBlacklisted`),
													  KEY `fk_unsubscribed_contact_list` (`contactListId`),
													  KEY `fk_unsubscribed_user` (`userId`),
													  CONSTRAINT `fk_unsubscribed_contact_list` FOREIGN KEY (`contactListId`) REFERENCES `smartemailing_contact_list` (`id`) ON DELETE CASCADE,
													  CONSTRAINT `fk_unsubscribed_user` FOREIGN KEY (`userId`) REFERENCES `user` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


-- SmartEmailing related indexes for better performance

-- User Interest indexes (add if not exists)
ALTER TABLE user_interest ADD INDEX IF NOT EXISTS idx_sync_to_smartemailing (syncToSmartEmailing);
ALTER TABLE user_interest ADD INDEX IF NOT EXISTS idx_smartemailing_option_id (smartEmailingOptionId);
ALTER TABLE user_interest ADD INDEX IF NOT EXISTS idx_smartemailing_order (smartEmailingOrder);
ALTER TABLE user_interest ADD INDEX IF NOT EXISTS idx_uid (uid);

-- SmartEmailing Custom Field indexes
ALTER TABLE smartemailing_custom_field ADD INDEX IF NOT EXISTS idx_smartemailing_field_id (smartEmailingFieldId);
ALTER TABLE smartemailing_custom_field ADD INDEX IF NOT EXISTS idx_name (name);
ALTER TABLE smartemailing_custom_field ADD INDEX IF NOT EXISTS idx_is_active (isActive);

-- SmartEmailing Contact List indexes
ALTER TABLE smartemailing_contact_list ADD INDEX IF NOT EXISTS idx_smartemailing_list_id (smartEmailingListId);
ALTER TABLE smartemailing_contact_list ADD INDEX IF NOT EXISTS idx_is_active (isActive);

-- SmartEmailing Unsubscribed Contact indexes
ALTER TABLE smartemailing_unsubscribed_contact ADD INDEX IF NOT EXISTS idx_smartemailing_contact_id (smartEmailingContactId);
ALTER TABLE smartemailing_unsubscribed_contact ADD INDEX IF NOT EXISTS idx_email_contact_list (email, contactListId);
ALTER TABLE smartemailing_unsubscribed_contact ADD INDEX IF NOT EXISTS idx_user_id (userId);

-- User SmartEmailing sync status indexes (only if columns exist)
ALTER TABLE user ADD INDEX IF NOT EXISTS idx_smartemailing_contact_id (smartEmailingContactId);
ALTER TABLE user ADD INDEX IF NOT EXISTS idx_smartemailing_sync_status (smartEmailingSyncStatus);
ALTER TABLE user ADD INDEX IF NOT EXISTS idx_allowed_mailing (allowedMailing);
