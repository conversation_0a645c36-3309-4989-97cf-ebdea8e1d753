CREATE TABLE `user_animal_type` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `internalName` varchar(255) DEFAULT NULL,
  `customFieldsJson` longtext DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci;

CREATE TABLE `user_animal` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `userId` int(11) NOT NULL,
  `name` varchar(255) NOT NULL,
  `userAnimalTypeId` int(11) NOT NULL,
  `customFieldsJson` longtext DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_userId` (`userId`),
  KEY `idx_typeId` (`userAnimalTypeId`),
  CONSTRAINT `fk_ua_type` FOREI<PERSON><PERSON>EY (`userAnimalTypeId`) REFERENCES `user_animal_type` (`id`),
  CONSTRAINT `fk_ua_user` FOREIGN KEY (`userId`) REFERENCES `user` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `user_animal_type_localization` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `userAnimalTypeId` int(11) NOT NULL,
  `mutationId` int(11) NOT NULL,
  `name` varchar(255) DEFAULT NULL,
  `public` tinyint(1) NOT NULL DEFAULT 0,
  `customFieldsJson` longtext DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_mutationId` (`mutationId`),
  KEY `idx_typeId` (`userAnimalTypeId`),
  CONSTRAINT `fk_loc_mutation` FOREIGN KEY (`mutationId`) REFERENCES `mutation` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `fk_loc_type` FOREIGN KEY (`userAnimalTypeId`) REFERENCES `user_animal_type` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci;

-- translate
INSERT INTO string (id, lg, name, value, usedAt)VALUES(null, 'cs', 'animal_add', 'Přidat mazlíčka', NULL);
INSERT INTO string (id, lg, name, value, usedAt)VALUES(null, 'cs', 'animal_name', 'Jméno', NULL);
INSERT INTO string (id, lg, name, value, usedAt)VALUES(null, 'cs', 'animal_type', 'Druh', NULL);
INSERT INTO string (id, lg, name, value, usedAt)VALUES(null, 'cs', 'animal_remove', 'Vymazat mazlíčka', NULL);
INSERT INTO string (id, lg, name, value, usedAt)VALUES(null, 'cs', 'animal_image_deleted', 'Obrázek mazlíčka byl vymazán', NULL);

-- trees
INSERT INTO `tree_parent` () value ();
SET @tree_parent_id = LAST_INSERT_ID();

INSERT INTO tree
( mutationId, rootId, extId, treeParentId, parentId, `level`, `path`, sort, `last`, uid, created, createdTime, createdTimeOrder, edited, editedTime, template, `type`, publicFrom, publicTo, name, nameAnchor, nameAnchorBreadcrumb, nameTitle, nameShort, nameHeading, description, keywords, public, score, forceNoIndex, hideInSearch, hideInSitemap, hideInMenu, annotation, content, hideFirstImage, links, seoTitleFilter, seoAnnotationFilter, seoDescriptionFilter, videos, customFieldsJson, customContentJson, productAttachedId, hasLinkedCategories)
VALUES( 1, 1, NULL, @tree_parent_id, 72, 2, '1|72|', 19, 1, 'userAnimalEdit', 0, '2025-06-10 11:18:59.000', '2025-06-10 11:18:00.000', 34, '2025-06-18 17:09:12.000', ':Front:User:animalEdit', 'common', '2025-06-10 11:18:00.000', '2125-06-10 11:18:00.000', 'Editace mazlíčka', 'Editace mazlíčka', '', 'Editace mazlíčka', '', '', '', '', 1, 0.0, 0, 0, 0, 0, '', '', NULL, NULL, NULL, NULL, NULL, NULL, '{"base":[{"show_eu_bnr":true}]}', '{}', NULL, NULL);

SET @treeId = LAST_INSERT_ID();
INSERT INTO alias (alias, module, referenceId, mutationId)VALUES( 'editace-mazlicka', 'tree',  @treeId, 1);

INSERT INTO `tree_parent` () value ();
SET @tree_parent_id = LAST_INSERT_ID();

INSERT INTO tree
(mutationId, rootId, extId, treeParentId, parentId, `level`, `path`, sort, `last`, uid, created, createdTime, createdTimeOrder, edited, editedTime, template, `type`, publicFrom, publicTo, name, nameAnchor, nameAnchorBreadcrumb, nameTitle, nameShort, nameHeading, description, keywords, public, score, forceNoIndex, hideInSearch, hideInSitemap, hideInMenu, annotation, content, hideFirstImage, links, seoTitleFilter, seoAnnotationFilter, seoDescriptionFilter, videos, customFieldsJson, customContentJson, productAttachedId, hasLinkedCategories)
VALUES( 1, 1, NULL, @tree_parent_id, 72, 2, '1|72|', 19, 1, 'userAnimal', 0, '2025-06-10 11:18:59.000', '2025-06-10 11:18:00.000', 34, '2025-06-18 17:29:35.000', ':Front:User:animal', 'common', '2025-06-10 11:18:00.000', '2125-06-10 11:18:00.000', 'Mí mazlíčci', 'Mí mazlíčci', '', 'Mí mazlíčci', '', '', '', '', 1, 0.0, 0, 0, 0, 0, '', '', NULL, NULL, NULL, NULL, NULL, NULL, '{"base":[{"show_eu_bnr":true}]}', '{}', NULL, NULL);

SET @treeId = LAST_INSERT_ID();
INSERT INTO alias(alias, module, referenceId, mutationId)VALUES( 'mi-mazlicci', 'tree', @treeId, 1);

INSERT INTO library_tree
(id, parentId, `level`, `path`, sort, `last`, created, createdTime, edited, editedTime, publicFrom, publicTo, name, nameTitle, nameAnchor, uid)
VALUES(6, 1, 1, '1|', 1, 1, 34, '2025-06-20 07:22:01.000', 34, '2025-06-20 07:22:01.000', '2025-06-20 07:22:01.000', '2100-01-01 00:00:00.000', 'Mí mazlíčci', 'Mí mazlíčci', 'Mí mazlíčci', NULL);
