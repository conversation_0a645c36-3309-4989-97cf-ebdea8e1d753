CREATE TABLE `user_product` (
								`id` int(11) NOT NULL AUTO_INCREMENT,
								`userId` int(11) NOT NULL,
								`productId` int(11) NOT NULL,
								PRIMARY KEY (`id`),
								UNIQUE KEY `userId_productId` (`userId`,`productId`),
								<PERSON><PERSON><PERSON> `productId` (`productId`),
								CONSTRAINT `user_product_ibfk_1` FOREIGN KEY (`userId`) REFERENCES `user` (`id`) ON DELETE CASCADE,
								CONSTRAINT `user_product_ibfk_2` FOREIGN KEY (`productId`) REFERENCES `product` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci;
