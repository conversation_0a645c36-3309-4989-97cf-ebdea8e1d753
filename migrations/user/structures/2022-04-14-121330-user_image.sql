
CREATE TABLE IF NOT EXISTS `user_image` (
	`id` int(11) NOT NULL AUTO_INCREMENT,
	`userId` int(11) NOT NULL,
	`imageId` int(11) NOT NULL COMMENT 'idimage',
	`name` varchar(250) DEFAULT NULL,
	`url` varchar(250) DEFAULT NULL,
	`sort` int(11) DEFAULT NULL,
	PRIMARY KEY (`id`),
	UNIQUE KEY `userId_imageId` (`userId`,`imageId`),
	<PERSON><PERSON>Y `idtree_idx` (`userId`),
	<PERSON>EY `idfile_idx` (`imageId`),
	CONSTRAINT `user_image_ibfk_2` FOREIGN KEY (`imageId`) REFERENCES `image` (`id`),
	CONSTRAINT `user_image_ibfk_3` FOREIGN KEY (`userId`) REFERENCES `user` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
	) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;

