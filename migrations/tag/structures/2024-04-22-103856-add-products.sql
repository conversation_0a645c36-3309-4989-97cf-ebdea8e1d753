create table tag_x_product
(
	id        int not null,
	tagId     int not null,
	productId int not null,
	constraint tag_x_product_pk
		primary key (id),
	constraint tag_x_product_product_id_fk
		foreign key (productId) references product (id),
	constraint tag_x_product_tag_id_fk
		foreign key (tagId) references tag (id)
);

alter table tag_x_product
	modify id int auto_increment;

alter table tag_x_product
	auto_increment = 1;

