-- auto-generated definition
create table tag_localization_x_product
(
	productId         int                                  null,
	tagLocalizationId int                                  null,
	id                int auto_increment
        primary key,
	edited            datetime default current_timestamp() null,
	`from`            datetime                             null,
	`to`              datetime                             null,
	constraint tag_localization_x_product_pk
		unique (productId, tagLocalizationId),
	constraint tag_localization_x_product_product_id_fk
		foreign key (productId) references product (id)
			on update cascade on delete cascade,
	constraint tag_localization_x_product_tag_localization_id_fk
		foreign key (tagLocalizationId) references tag_localization (id)
			on update cascade on delete cascade
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci;

