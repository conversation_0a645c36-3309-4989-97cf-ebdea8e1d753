ALTER TABLE tag MODIFY COLUMN color varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci DEFAULT NULL NULL;

-- clear old tags
DELETE tag_x_product
FROM tag_x_product
		 JOIN tag ON tag_x_product.tagId = tag.id
WHERE tag.type IN ('top', 'bestseller', 'present', 'classType', 'lastMinute');

DELETE tag_localization
FROM tag_localization
		 JOIN tag ON tag_localization.tagId = tag.id
WHERE tag.type IN ('top', 'bestseller', 'present', 'classType', 'lastMinute');

DELETE FROM tag
WHERE type IN ('top', 'bestseller', 'present', 'classType', 'lastMinute');

-- translations
INSERT INTO string (lg,name,value) VALUES ('cs','new','Novinka');
INSERT INTO string (lg,name,value) VALUES ('cs','color','Barva');
