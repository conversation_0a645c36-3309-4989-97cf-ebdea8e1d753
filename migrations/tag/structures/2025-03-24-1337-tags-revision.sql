alter table tag_x_product
drop foreign key tag_x_product_product_id_fk;

alter table tag_x_product
	add constraint tag_x_product_product_id_fk
		foreign key (productId) references product (id)
			on update cascade on delete cascade;

alter table tag_x_product
drop foreign key tag_x_product_tag_id_fk;

alter table tag_x_product
	add constraint tag_x_product_tag_id_fk
		foreign key (tagId) references tag (id)
			on update cascade on delete cascade;


delete from tag where type = 'classType';
