create table tag
(
	id               int auto_increment
		primary key,
	internalName     varchar(255)                 not null,
	customFieldsJson longtext null,
	type             varchar(50) default 'custom' not null
) ENGINE = InnoDB
	AUTO_INCREMENT = 1
	DEFAULT CHARSET = utf8
	COLLATE = utf8_bin;

create table tag_localization
(
	id                int auto_increment
		primary key,
	tagId             int not null,
	mutation          int not null,
	edited            int null,
	name              varchar(255) null,
	public            tinyint(1) default 0 not null,
	editedTime        datetime null,
	createdTime       datetime null,
	customFieldsJson  longtext null,
	CustomContent<PERSON>son longtext null,
	constraint tag_localization_mutation_id_fk
		foreign key (mutation) references mutation (id),
	constraint tag_localization_tag_id_fk
		foreign key (tagId) references tag (id),
	constraint tag_localization_user_id_fk
		foreign key (edited) references user (id)
)ENGINE = InnoDB
	AUTO_INCREMENT = 1
	DEFAULT CHARSET = utf8
	COLLATE = utf8_bin;

