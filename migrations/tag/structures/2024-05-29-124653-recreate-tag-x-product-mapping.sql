DROP TABLE IF EXISTS `tag_x_product`;

create table `tag_x_product`
(
	id        int auto_increment primary key,
	tagId     int                                not null,
	productId int                                not null,
	edited    datetime default CURRENT_TIMESTAMP not null,
	constraint tag_x_product_product_id_fk
		foreign key (productId) references product (id),
	constraint tag_x_product_tag_id_fk
		foreign key (tagId) references tag (id)
) ENGINE = InnoDB
	AUTO_INCREMENT = 1
	DEFAULT CHARSET = utf8
	COLLATE = utf8_bin;

INSERT INTO tag (internalName, customFieldsJson, type, color)
VALUES ('Free transit (system)', '{}', 'transitFreeSystem', '#ffffff');

INSERT INTO tag_localization (tagId, mutation, edited, name, public, editedTime, createdTime, description, nameAnchor,
							  nameTitle, title, CustomContentJson, customFieldsJson, keywords, isInFilter)
VALUES (LAST_INSERT_ID(), 1, null, 'Free transit (system)', 0, null, null, 'Free transit (system)',
		'Free transit (system)', 'Free transit (system)', null, null, null, '', 0);
