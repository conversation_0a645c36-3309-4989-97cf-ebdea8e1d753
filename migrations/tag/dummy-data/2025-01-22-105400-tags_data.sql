delete from tag;
delete from tag_localization;


insert into tag (id, internalName, customFieldsJson, type, color)
values  (1, 'Novinka', '{}', 'new', '#ff4f0f'),
		(2, 'Top', '{}', 'top', '#ff4f0f'),
		(3, 'Bestseller', '{}', 'bestseller', '#ffb7d5'),
		(4, 'Předobjednáv<PERSON>', '{}', 'preorder', '#cbddfb'),
		(5, '+ <PERSON><PERSON><PERSON>', '{}', 'present', '#bdda01'),
		(6, 'Free transit', '{}', 'transitFree', '#ffc0a1'),
		(7, 'Custom', '{}', 'custom', '#ce2c2c'),
		(10, '1', '{}', 'custom', '#ffffff'),
		(12, '<PERSON><PERSON><PERSON> od <PERSON>', '{}', 'custom', '#fe6cf2');


insert into tag_localization (id, tagId, mutationId, edited, name, public, editedTime, createdTime, description, nameAnchor, nameTitle, title, CustomContentJson, customFieldsJson, keywords, isInFilter)
values  (2, 1, 1, 34, 'Novinka', 1, '2024-10-02 17:18:40', null, '', 'novinka', '<PERSON>nky', null, '{}', '{}', '', 1),
		(3, 2, 1, 37, 'Top', 1, '2024-07-23 11:56:58', null, 'top', 'top', 'top', null, '{}', '{}', '', 1),
		(4, 3, 1, 34, 'Bestseller', 1, '2024-10-02 17:19:54', null, 'Bestsellery', 'bestseller', 'Bestsellery', null, '{}', '{}', '', 1),
		(5, 4, 1, 9, 'Předobjednávka', 1, '2024-11-01 14:09:38', null, '', 'predprodej', 'Předprodej', null, '{}', '{}', '', 1),
		(6, 5, 1, 3, '+ Dárek', 1, '2024-09-09 12:46:02', null, '', '+ dárek', '+ dárek', null, '{}', '{}', '', 1),
		(7, 6, 1, 34, 'Doprava zdarma', 1, '2024-10-02 16:23:05', null, ':)', 'transit free', 'transit free', null, '{}', '{}', '', 0),
		(8, 7, 1, 3, 'Custom', 1, '2024-09-09 13:15:17', null, 'custom', 'custom', 'custom', null, '{}', '{}', '', 0),
		(11, 10, 1, 37, '1', 0, '2024-06-28 13:11:33', '2024-06-13 15:37:48', '', '1', '1', null, '{}', '{}', '', 0),
		(13, 12, 1, 34, 'Kniha od Moravce', 1, '2024-09-25 15:21:49', '2024-09-25 15:21:14', '', 'Kniha od Moravce', 'Kniha od Moravce', null, '{}', '{}', '', 0);



INSERT INTO tag_x_product (tagId, productId, edited, `from`, `to`)
VALUES (3, 16, DEFAULT, null, null);
INSERT INTO tag_x_product (tagId, productId, edited, `from`, `to`)
VALUES (6, 17, DEFAULT, null, null);
