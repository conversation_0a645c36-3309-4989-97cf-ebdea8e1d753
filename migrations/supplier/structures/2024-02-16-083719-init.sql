SET NAMES utf8mb4;

DROP TABLE IF EXISTS `supplier`;
CREATE TABLE `supplier` (
							`id` int(11) NOT NULL AUTO_INCREMENT,
							`erpId` int(11) DEFAULT NULL,
							`erpCode` varchar(20) DEFAULT NULL,
							`internalName` varchar(255) NOT NULL,
							`customFieldsJson` longtext NOT NULL,
							PRIMARY KEY (`id`),
							<PERSON><PERSON>Y `erpCode` (`erpCode`),
							KEY `erpId` (`erpId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci;
