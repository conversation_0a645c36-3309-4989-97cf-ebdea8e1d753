
INSERT INTO `tree_parent` () value ();
SET @tree_parent_id = LAST_INSERT_ID();

INSERT INTO tree
(mutationId, rootId, extId, treeParentId, parentId, `level`, `path`, sort, `last`, uid, created, createdTime, createdTimeOrder, edited, editedTime, template, `type`, publicFrom, publicTo, name, nameAnchor, nameAnchorBreadcrumb, nameTitle, nameShort, nameHeading, description, keywords, public, score, forceNoIndex, hideInSearch, hideInSitemap, hideInMenu, annotation, content, hideFirstImage, links, seoTitleFilter, seoAnnotationFilter, seoDescriptionFilter, videos, customFieldsJson, customContentJson, productAttachedId, hasLinkedCategories)
VALUES(1, 1, NULL, @tree_parent_id, 43, 2, '1|43|', 11, 1, 'popupDeliveryOptions', 0, '2025-06-26 10:36:35.000', '2025-06-26 10:36:00.000', 34, '2025-06-26 13:08:58.000', ':Front:Product:deliveryOptions', 'common', '2025-06-26 10:36:00.000', '2125-06-26 10:36:00.000', 'Způsoby dodání - popup', 'Způsoby dodání', '', 'Způsoby dodání', '', '', '', '', 1, 0.0, 0, 0, 0, 0, '', '', NULL, NULL, NULL, NULL, NULL, NULL, '{}', '{}', NULL, NULL);

SET @treeId = LAST_INSERT_ID();
INSERT INTO alias(id, alias, module, referenceId, mutationId)VALUES(3355, 'zpusoby-dodani-popup', 'tree', @treeId, 1);
