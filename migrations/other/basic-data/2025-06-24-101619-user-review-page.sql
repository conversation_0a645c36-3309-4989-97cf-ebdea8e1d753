
-- product review add
INSERT INTO `tree_parent` () value ();
SET @tree_parent_id = LAST_INSERT_ID();

INSERT INTO tree
(mutationId, rootId, extId, treeParentId, parentId, `level`, `path`, sort, `last`, uid, created, createdTime, createdTimeOrder, edited, editedTime, template, `type`, publicFrom, publicTo, name, nameAnchor, nameAnchorBreadcrumb, nameTitle, nameShort, nameHeading, description, keywords, public, score, forceNoIndex, hideInSearch, hideInSitemap, hideInMenu, annotation, content, hideFirstImage, links, seoTitleFilter, seoAnnotationFilter, seoDescriptionFilter, videos, customFieldsJson, customContentJson, productAttachedId, hasLinkedCategories)
VALUES(1, 1, NULL, @tree_parent_id, 398, 2, '1|398|', 12, 1, 'productReviewAdd', 0, '2025-06-23 20:40:10.000', '2025-06-23 20:40:00.000', 34, '2025-06-23 20:50:07.000', ':ProductReview:Front:ProductReview:default', 'common', '2025-06-23 20:40:00.000', '2125-06-23 20:40:00.000', 'Recenze produktu', 'Recenze produktu', '', 'Recenze produktu', '', '', '', '', 1, 0.0, 0, 0, 0, 0, '', '', NULL, NULL, NULL, NULL, NULL, NULL, '{"base":[{"show_eu_bnr":true}],"header":[{"extra":[{"type":"none"}]}]}', '{}', NULL, NULL);

SET @treeId = LAST_INSERT_ID();
INSERT INTO alias(alias, module, referenceId, mutationId)VALUES( 'recenze-produktu', 'tree', @treeId, 1);

-- My review
INSERT INTO `tree_parent` () value ();
SET @tree_parent_id2 = LAST_INSERT_ID();

INSERT INTO tree
(mutationId, rootId, extId, treeParentId, parentId, `level`, `path`, sort, `last`, uid, created, createdTime, createdTimeOrder, edited, editedTime, template, `type`, publicFrom, publicTo, name, nameAnchor, nameAnchorBreadcrumb, nameTitle, nameShort, nameHeading, description, keywords, public, score, forceNoIndex, hideInSearch, hideInSitemap, hideInMenu, annotation, content, hideFirstImage, links, seoTitleFilter, seoAnnotationFilter, seoDescriptionFilter, videos, customFieldsJson, customContentJson, productAttachedId, hasLinkedCategories)
VALUES(1, 1, NULL, @tree_parent_id2, 72, 2, '1|72|', 22, 1, 'myReviews', 0, '2025-06-24 08:00:07.000', '2025-06-24 08:00:00.000', 34, '2025-06-24 08:00:27.000', ':Front:User:default', 'common', '2025-06-24 08:00:00.000', '2125-06-24 08:00:00.000', 'Mé hodnocení', 'Mé hodnocení', '', 'Mé hodnocení', '', '', '', '', 1, 0.0, 0, 0, 0, 0, '', '', NULL, NULL, NULL, NULL, NULL, NULL, '{"base":[{"show_eu_bnr":true}]}', '{}', NULL, NULL);

SET @treeId2 = LAST_INSERT_ID();
INSERT INTO alias(alias, module, referenceId, mutationId)VALUES('me-hodnoceni', 'tree', @treeId2, 1);
