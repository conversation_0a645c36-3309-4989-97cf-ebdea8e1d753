CREATE TABLE `search_log` (
  `id` INT(11) NOT NULL AUTO_INCREMENT,
  `text` VARCHAR(250) NOT NULL DEFAULT '' COLLATE 'utf8mb4_unicode_520_ci',
  `agent` TEXT NOT NULL COLLATE 'utf8mb4_unicode_520_ci',
  `userId` INT(11) NULL DEFAULT NULL,
  `createdAt` DATETIME NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `text` (`text`) USING BTREE
)
	COLLATE='utf8mb4_unicode_520_ci'
ENGINE=InnoDB
;


ALTER TABLE `search_log`
	ADD COLUMN `mutationId` INT(11) NOT NULL AFTER `id`,
	ADD CONSTRAINT `FK_search_log_mutation` FOREIGN KEY (`mutationId`) REFERENCES `mutation` (`id`) ON UPDATE CASCADE ON DELETE CASCADE;


ALTER TABLE `product_variant`
	ADD INDEX `ean` (`ean`),
	ADD INDEX `isbn` (`isbn`);
