
CREATE TABLE IF NOT EXISTS `newsletter_email` (
	`id` int(11) NOT NULL AUTO_INCREMENT,
	`email` varchar(100) NOT NULL,
	`createdTime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
	`mutationId` int(11) NOT NULL DEFAULT '1',
	PRIMARY KEY (`id`),
	UNIQUE KEY `email_mutationId` (`email`,`mutationId`),
	<PERSON><PERSON><PERSON> `newsletter_email_mutation` (`mutationId`),
	CONSTRAINT `newsletter_email_mutation` FOREIGN KEY (`mutationId`) REFERENCES `mutation` (`id`) ON UPDATE CASCADE
	) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf32;

