DELETE FROM `state`;

INSERT INTO `state` (`code`, `name`) VALUES ('AD', 'Andorra');
INSERT INTO `state` (`code`, `name`) VALUES ('AE', 'United Arab Emirates');
INSERT INTO `state` (`code`, `name`) VALUES ('AF', 'Afghanistan');
INSERT INTO `state` (`code`, `name`) VALUES ('AG', 'Antigua and Barbuda');
INSERT INTO `state` (`code`, `name`) VALUES ('AI', 'Anguilla');
INSERT INTO `state` (`code`, `name`) VALUES ('AL', 'Albania');
INSERT INTO `state` (`code`, `name`) VALUES ('AM', 'Armenia');
INSERT INTO `state` (`code`, `name`) VALUES ('AN', 'Netherlands Antilles');
INSERT INTO `state` (`code`, `name`) VALUES ('AO', 'Angola');
INSERT INTO `state` (`code`, `name`) VALUES ('AQ', 'Antarctica');
INSERT INTO `state` (`code`, `name`) VALUES ('AR', 'Argentina');
INSERT INTO `state` (`code`, `name`) VALUES ('AS', 'American Samoa');
INSERT INTO `state` (`code`, `name`) VALUES ('AT', 'Austria');
INSERT INTO `state` (`code`, `name`) VALUES ('AU', 'Australia');
INSERT INTO `state` (`code`, `name`) VALUES ('AW', 'Aruba');
INSERT INTO `state` (`code`, `name`) VALUES ('AZ', 'Azerbaijan');
INSERT INTO `state` (`code`, `name`) VALUES ('BA', 'Bosnia and Herzegovina');
INSERT INTO `state` (`code`, `name`) VALUES ('BB', 'Barbados');
INSERT INTO `state` (`code`, `name`) VALUES ('BD', 'Bangladesh');
INSERT INTO `state` (`code`, `name`) VALUES ('BE', 'Belgium');
INSERT INTO `state` (`code`, `name`) VALUES ('BF', 'Burkina Faso');
INSERT INTO `state` (`code`, `name`) VALUES ('BG', 'Bulgaria');
INSERT INTO `state` (`code`, `name`) VALUES ('BH', 'Bahrain');
INSERT INTO `state` (`code`, `name`) VALUES ('BI', 'Burundi');
INSERT INTO `state` (`code`, `name`) VALUES ('BJ', 'Benin');
INSERT INTO `state` (`code`, `name`) VALUES ('BM', 'Bermuda');
INSERT INTO `state` (`code`, `name`) VALUES ('BN', 'Brunei');
INSERT INTO `state` (`code`, `name`) VALUES ('BO', 'Bolivia');
INSERT INTO `state` (`code`, `name`) VALUES ('BR', 'Brazil');
INSERT INTO `state` (`code`, `name`) VALUES ('BS', 'Bahamas');
INSERT INTO `state` (`code`, `name`) VALUES ('BT', 'Bhutan');
INSERT INTO `state` (`code`, `name`) VALUES ('BV', 'Bouvet Island');
INSERT INTO `state` (`code`, `name`) VALUES ('BW', 'Botswana');
INSERT INTO `state` (`code`, `name`) VALUES ('BY', 'Belarus');
INSERT INTO `state` (`code`, `name`) VALUES ('BZ', 'Belize');
INSERT INTO `state` (`code`, `name`) VALUES ('CA', 'Canada');
INSERT INTO `state` (`code`, `name`) VALUES ('CC', 'Cocos [Keeling] Islands');
INSERT INTO `state` (`code`, `name`) VALUES ('CD', 'Congo [DRC]');
INSERT INTO `state` (`code`, `name`) VALUES ('CF', 'Central African Republic');
INSERT INTO `state` (`code`, `name`) VALUES ('CG', 'Congo [Republic]');
INSERT INTO `state` (`code`, `name`) VALUES ('CH', 'Switzerland');
INSERT INTO `state` (`code`, `name`) VALUES ('CI', 'Côte d\'Ivoire');
INSERT INTO `state` (`code`, `name`) VALUES ('CK', 'Cook Islands');
INSERT INTO `state` (`code`, `name`) VALUES ('CL', 'Chile');
INSERT INTO `state` (`code`, `name`) VALUES ('CM', 'Cameroon');
INSERT INTO `state` (`code`, `name`) VALUES ('CN', 'China');
INSERT INTO `state` (`code`, `name`) VALUES ('CO', 'Colombia');
INSERT INTO `state` (`code`, `name`) VALUES ('CR', 'Costa Rica');
INSERT INTO `state` (`code`, `name`) VALUES ('CU', 'Cuba');
INSERT INTO `state` (`code`, `name`) VALUES ('CV', 'Cape Verde');
INSERT INTO `state` (`code`, `name`) VALUES ('CX', 'Christmas Island');
INSERT INTO `state` (`code`, `name`) VALUES ('CY', 'Cyprus');
INSERT INTO `state` (`code`, `name`) VALUES ('CZ', 'Czech Republic');
INSERT INTO `state` (`code`, `name`) VALUES ('DE', 'Germany');
INSERT INTO `state` (`code`, `name`) VALUES ('DJ', 'Djibouti');
INSERT INTO `state` (`code`, `name`) VALUES ('DK', 'Denmark');
INSERT INTO `state` (`code`, `name`) VALUES ('DM', 'Dominica');
INSERT INTO `state` (`code`, `name`) VALUES ('DO', 'Dominican Republic');
INSERT INTO `state` (`code`, `name`) VALUES ('DZ', 'Algeria');
INSERT INTO `state` (`code`, `name`) VALUES ('EC', 'Ecuador');
INSERT INTO `state` (`code`, `name`) VALUES ('EE', 'Estonia');
INSERT INTO `state` (`code`, `name`) VALUES ('EG', 'Egypt');
INSERT INTO `state` (`code`, `name`) VALUES ('EH', 'Western Sahara');
INSERT INTO `state` (`code`, `name`) VALUES ('ER', 'Eritrea');
INSERT INTO `state` (`code`, `name`) VALUES ('ES', 'Spain');
INSERT INTO `state` (`code`, `name`) VALUES ('ET', 'Ethiopia');
INSERT INTO `state` (`code`, `name`) VALUES ('FI', 'Finland');
INSERT INTO `state` (`code`, `name`) VALUES ('FJ', 'Fiji');
INSERT INTO `state` (`code`, `name`) VALUES ('FK', 'Falkland Islands [Islas Malvinas]');
INSERT INTO `state` (`code`, `name`) VALUES ('FM', 'Micronesia');
INSERT INTO `state` (`code`, `name`) VALUES ('FO', 'Faroe Islands');
INSERT INTO `state` (`code`, `name`) VALUES ('FR', 'France');
INSERT INTO `state` (`code`, `name`) VALUES ('GA', 'Gabon');
INSERT INTO `state` (`code`, `name`) VALUES ('GB', 'United Kingdom');
INSERT INTO `state` (`code`, `name`) VALUES ('GD', 'Grenada');
INSERT INTO `state` (`code`, `name`) VALUES ('GE', 'Georgia');
INSERT INTO `state` (`code`, `name`) VALUES ('GF', 'French Guiana');
INSERT INTO `state` (`code`, `name`) VALUES ('GG', 'Guernsey');
INSERT INTO `state` (`code`, `name`) VALUES ('GH', 'Ghana');
INSERT INTO `state` (`code`, `name`) VALUES ('GI', 'Gibraltar');
INSERT INTO `state` (`code`, `name`) VALUES ('GL', 'Greenland');
INSERT INTO `state` (`code`, `name`) VALUES ('GM', 'Gambia');
INSERT INTO `state` (`code`, `name`) VALUES ('GN', 'Guinea');
INSERT INTO `state` (`code`, `name`) VALUES ('GP', 'Guadeloupe');
INSERT INTO `state` (`code`, `name`) VALUES ('GQ', 'Equatorial Guinea');
INSERT INTO `state` (`code`, `name`) VALUES ('GR', 'Greece');
INSERT INTO `state` (`code`, `name`) VALUES ('GS', 'South Georgia and the South Sandwich Islands');
INSERT INTO `state` (`code`, `name`) VALUES ('GT', 'Guatemala');
INSERT INTO `state` (`code`, `name`) VALUES ('GU', 'Guam');
INSERT INTO `state` (`code`, `name`) VALUES ('GW', 'Guinea-Bissau');
INSERT INTO `state` (`code`, `name`) VALUES ('GY', 'Guyana');
INSERT INTO `state` (`code`, `name`) VALUES ('GZ', 'Gaza Strip');
INSERT INTO `state` (`code`, `name`) VALUES ('HK', 'Hong Kong');
INSERT INTO `state` (`code`, `name`) VALUES ('HM', 'Heard Island and McDonald Islands');
INSERT INTO `state` (`code`, `name`) VALUES ('HN', 'Honduras');
INSERT INTO `state` (`code`, `name`) VALUES ('HR', 'Croatia');
INSERT INTO `state` (`code`, `name`) VALUES ('HT', 'Haiti');
INSERT INTO `state` (`code`, `name`) VALUES ('HU', 'Hungary');
INSERT INTO `state` (`code`, `name`) VALUES ('ID', 'Indonesia');
INSERT INTO `state` (`code`, `name`) VALUES ('IE', 'Ireland');
INSERT INTO `state` (`code`, `name`) VALUES ('IL', 'Israel');
INSERT INTO `state` (`code`, `name`) VALUES ('IM', 'Isle of Man');
INSERT INTO `state` (`code`, `name`) VALUES ('IN', 'India');
INSERT INTO `state` (`code`, `name`) VALUES ('IO', 'British Indian Ocean Territory');
INSERT INTO `state` (`code`, `name`) VALUES ('IQ', 'Iraq');
INSERT INTO `state` (`code`, `name`) VALUES ('IR', 'Iran');
INSERT INTO `state` (`code`, `name`) VALUES ('IS', 'Iceland');
INSERT INTO `state` (`code`, `name`) VALUES ('IT', 'Italy');
INSERT INTO `state` (`code`, `name`) VALUES ('JE', 'Jersey');
INSERT INTO `state` (`code`, `name`) VALUES ('JM', 'Jamaica');
INSERT INTO `state` (`code`, `name`) VALUES ('JO', 'Jordan');
INSERT INTO `state` (`code`, `name`) VALUES ('JP', 'Japan');
INSERT INTO `state` (`code`, `name`) VALUES ('KE', 'Kenya');
INSERT INTO `state` (`code`, `name`) VALUES ('KG', 'Kyrgyzstan');
INSERT INTO `state` (`code`, `name`) VALUES ('KH', 'Cambodia');
INSERT INTO `state` (`code`, `name`) VALUES ('KI', 'Kiribati');
INSERT INTO `state` (`code`, `name`) VALUES ('KM', 'Comoros');
INSERT INTO `state` (`code`, `name`) VALUES ('KN', 'Saint Kitts and Nevis');
INSERT INTO `state` (`code`, `name`) VALUES ('KP', 'North Korea');
INSERT INTO `state` (`code`, `name`) VALUES ('KR', 'South Korea');
INSERT INTO `state` (`code`, `name`) VALUES ('KW', 'Kuwait');
INSERT INTO `state` (`code`, `name`) VALUES ('KY', 'Cayman Islands');
INSERT INTO `state` (`code`, `name`) VALUES ('KZ', 'Kazakhstan');
INSERT INTO `state` (`code`, `name`) VALUES ('LA', 'Laos');
INSERT INTO `state` (`code`, `name`) VALUES ('LB', 'Lebanon');
INSERT INTO `state` (`code`, `name`) VALUES ('LC', 'Saint Lucia');
INSERT INTO `state` (`code`, `name`) VALUES ('LI', 'Liechtenstein');
INSERT INTO `state` (`code`, `name`) VALUES ('LK', 'Sri Lanka');
INSERT INTO `state` (`code`, `name`) VALUES ('LR', 'Liberia');
INSERT INTO `state` (`code`, `name`) VALUES ('LS', 'Lesotho');
INSERT INTO `state` (`code`, `name`) VALUES ('LT', 'Lithuania');
INSERT INTO `state` (`code`, `name`) VALUES ('LU', 'Luxembourg');
INSERT INTO `state` (`code`, `name`) VALUES ('LV', 'Latvia');
INSERT INTO `state` (`code`, `name`) VALUES ('LY', 'Libya');
INSERT INTO `state` (`code`, `name`) VALUES ('MA', 'Morocco');
INSERT INTO `state` (`code`, `name`) VALUES ('MC', 'Monaco');
INSERT INTO `state` (`code`, `name`) VALUES ('MD', 'Moldova');
INSERT INTO `state` (`code`, `name`) VALUES ('ME', 'Montenegro');
INSERT INTO `state` (`code`, `name`) VALUES ('MG', 'Madagascar');
INSERT INTO `state` (`code`, `name`) VALUES ('MH', 'Marshall Islands');
INSERT INTO `state` (`code`, `name`) VALUES ('MK', 'Macedonia [FYROM]');
INSERT INTO `state` (`code`, `name`) VALUES ('ML', 'Mali');
INSERT INTO `state` (`code`, `name`) VALUES ('MM', 'Myanmar [Burma]');
INSERT INTO `state` (`code`, `name`) VALUES ('MN', 'Mongolia');
INSERT INTO `state` (`code`, `name`) VALUES ('MO', 'Macau');
INSERT INTO `state` (`code`, `name`) VALUES ('MP', 'Northern Mariana Islands');
INSERT INTO `state` (`code`, `name`) VALUES ('MQ', 'Martinique');
INSERT INTO `state` (`code`, `name`) VALUES ('MR', 'Mauritania');
INSERT INTO `state` (`code`, `name`) VALUES ('MS', 'Montserrat');
INSERT INTO `state` (`code`, `name`) VALUES ('MT', 'Malta');
INSERT INTO `state` (`code`, `name`) VALUES ('MU', 'Mauritius');
INSERT INTO `state` (`code`, `name`) VALUES ('MV', 'Maldives');
INSERT INTO `state` (`code`, `name`) VALUES ('MW', 'Malawi');
INSERT INTO `state` (`code`, `name`) VALUES ('MX', 'Mexico');
INSERT INTO `state` (`code`, `name`) VALUES ('MY', 'Malaysia');
INSERT INTO `state` (`code`, `name`) VALUES ('MZ', 'Mozambique');
INSERT INTO `state` (`code`, `name`) VALUES ('NA', 'Namibia');
INSERT INTO `state` (`code`, `name`) VALUES ('NC', 'New Caledonia');
INSERT INTO `state` (`code`, `name`) VALUES ('NE', 'Niger');
INSERT INTO `state` (`code`, `name`) VALUES ('NF', 'Norfolk Island');
INSERT INTO `state` (`code`, `name`) VALUES ('NG', 'Nigeria');
INSERT INTO `state` (`code`, `name`) VALUES ('NI', 'Nicaragua');
INSERT INTO `state` (`code`, `name`) VALUES ('NL', 'Netherlands');
INSERT INTO `state` (`code`, `name`) VALUES ('NO', 'Norway');
INSERT INTO `state` (`code`, `name`) VALUES ('NP', 'Nepal');
INSERT INTO `state` (`code`, `name`) VALUES ('NR', 'Nauru');
INSERT INTO `state` (`code`, `name`) VALUES ('NU', 'Niue');
INSERT INTO `state` (`code`, `name`) VALUES ('NZ', 'New Zealand');
INSERT INTO `state` (`code`, `name`) VALUES ('OM', 'Oman');
INSERT INTO `state` (`code`, `name`) VALUES ('PA', 'Panama');
INSERT INTO `state` (`code`, `name`) VALUES ('PE', 'Peru');
INSERT INTO `state` (`code`, `name`) VALUES ('PF', 'French Polynesia');
INSERT INTO `state` (`code`, `name`) VALUES ('PG', 'Papua New Guinea');
INSERT INTO `state` (`code`, `name`) VALUES ('PH', 'Philippines');
INSERT INTO `state` (`code`, `name`) VALUES ('PK', 'Pakistan');
INSERT INTO `state` (`code`, `name`) VALUES ('PL', 'Poland');
INSERT INTO `state` (`code`, `name`) VALUES ('PM', 'Saint Pierre and Miquelon');
INSERT INTO `state` (`code`, `name`) VALUES ('PN', 'Pitcairn Islands');
INSERT INTO `state` (`code`, `name`) VALUES ('PR', 'Puerto Rico');
INSERT INTO `state` (`code`, `name`) VALUES ('PS', 'Palestinian Territories');
INSERT INTO `state` (`code`, `name`) VALUES ('PT', 'Portugal');
INSERT INTO `state` (`code`, `name`) VALUES ('PW', 'Palau');
INSERT INTO `state` (`code`, `name`) VALUES ('PY', 'Paraguay');
INSERT INTO `state` (`code`, `name`) VALUES ('QA', 'Qatar');
INSERT INTO `state` (`code`, `name`) VALUES ('RE', 'Réunion');
INSERT INTO `state` (`code`, `name`) VALUES ('RO', 'Romania');
INSERT INTO `state` (`code`, `name`) VALUES ('RS', 'Serbia');
INSERT INTO `state` (`code`, `name`) VALUES ('RU', 'Russia');
INSERT INTO `state` (`code`, `name`) VALUES ('RW', 'Rwanda');
INSERT INTO `state` (`code`, `name`) VALUES ('SA', 'Saudi Arabia');
INSERT INTO `state` (`code`, `name`) VALUES ('SB', 'Solomon Islands');
INSERT INTO `state` (`code`, `name`) VALUES ('SC', 'Seychelles');
INSERT INTO `state` (`code`, `name`) VALUES ('SD', 'Sudan');
INSERT INTO `state` (`code`, `name`) VALUES ('SE', 'Sweden');
INSERT INTO `state` (`code`, `name`) VALUES ('SG', 'Singapore');
INSERT INTO `state` (`code`, `name`) VALUES ('SH', 'Saint Helena');
INSERT INTO `state` (`code`, `name`) VALUES ('SI', 'Slovenia');
INSERT INTO `state` (`code`, `name`) VALUES ('SJ', 'Svalbard and Jan Mayen');
INSERT INTO `state` (`code`, `name`) VALUES ('SK', 'Slovakia');
INSERT INTO `state` (`code`, `name`) VALUES ('SL', 'Sierra Leone');
INSERT INTO `state` (`code`, `name`) VALUES ('SM', 'San Marino');
INSERT INTO `state` (`code`, `name`) VALUES ('SN', 'Senegal');
INSERT INTO `state` (`code`, `name`) VALUES ('SO', 'Somalia');
INSERT INTO `state` (`code`, `name`) VALUES ('SR', 'Suriname');
INSERT INTO `state` (`code`, `name`) VALUES ('ST', 'São Tomé and Príncipe');
INSERT INTO `state` (`code`, `name`) VALUES ('SV', 'El Salvador');
INSERT INTO `state` (`code`, `name`) VALUES ('SY', 'Syria');
INSERT INTO `state` (`code`, `name`) VALUES ('SZ', 'Swaziland');
INSERT INTO `state` (`code`, `name`) VALUES ('TC', 'Turks and Caicos Islands');
INSERT INTO `state` (`code`, `name`) VALUES ('TD', 'Chad');
INSERT INTO `state` (`code`, `name`) VALUES ('TF', 'French Southern Territories');
INSERT INTO `state` (`code`, `name`) VALUES ('TG', 'Togo');
INSERT INTO `state` (`code`, `name`) VALUES ('TH', 'Thailand');
INSERT INTO `state` (`code`, `name`) VALUES ('TJ', 'Tajikistan');
INSERT INTO `state` (`code`, `name`) VALUES ('TK', 'Tokelau');
INSERT INTO `state` (`code`, `name`) VALUES ('TL', 'Timor-Leste');
INSERT INTO `state` (`code`, `name`) VALUES ('TM', 'Turkmenistan');
INSERT INTO `state` (`code`, `name`) VALUES ('TN', 'Tunisia');
INSERT INTO `state` (`code`, `name`) VALUES ('TO', 'Tonga');
INSERT INTO `state` (`code`, `name`) VALUES ('TR', 'Turkey');
INSERT INTO `state` (`code`, `name`) VALUES ('TT', 'Trinidad and Tobago');
INSERT INTO `state` (`code`, `name`) VALUES ('TV', 'Tuvalu');
INSERT INTO `state` (`code`, `name`) VALUES ('TW', 'Taiwan');
INSERT INTO `state` (`code`, `name`) VALUES ('TZ', 'Tanzania');
INSERT INTO `state` (`code`, `name`) VALUES ('UA', 'Ukraine');
INSERT INTO `state` (`code`, `name`) VALUES ('UG', 'Uganda');
INSERT INTO `state` (`code`, `name`) VALUES ('UM', 'U.S. Minor Outlying Islands');

INSERT INTO `state` (`code`, `name`) VALUES ('US', 'United States');
INSERT INTO `state` (`code`, `name`) VALUES ('UY', 'Uruguay');
INSERT INTO `state` (`code`, `name`) VALUES ('UZ', 'Uzbekistan');
INSERT INTO `state` (`code`, `name`) VALUES ('VA', 'Vatican City');
INSERT INTO `state` (`code`, `name`) VALUES ('VC', 'Saint Vincent and the Grenadines');
INSERT INTO `state` (`code`, `name`) VALUES ('VE', 'Venezuela');
INSERT INTO `state` (`code`, `name`) VALUES ('VG', 'British Virgin Islands');
INSERT INTO `state` (`code`, `name`) VALUES ('VI', 'U.S. Virgin Islands');
INSERT INTO `state` (`code`, `name`) VALUES ('VN', 'Vietnam');
INSERT INTO `state` (`code`, `name`) VALUES ('VU', 'Vanuatu');
INSERT INTO `state` (`code`, `name`) VALUES ('WF', 'Wallis and Futuna');
INSERT INTO `state` (`code`, `name`) VALUES ('WS', 'Samoa');
INSERT INTO `state` (`code`, `name`) VALUES ('XK', 'Kosovo');
INSERT INTO `state` (`code`, `name`) VALUES ('YE', 'Yemen');
INSERT INTO `state` (`code`, `name`) VALUES ('YT', 'Mayotte');
INSERT INTO `state` (`code`, `name`) VALUES ('ZA', 'South Africa');
INSERT INTO `state` (`code`, `name`) VALUES ('ZM', 'Zambia');
INSERT INTO `state` (`code`, `name`) VALUES ('ZW', 'Zimbabwe');
