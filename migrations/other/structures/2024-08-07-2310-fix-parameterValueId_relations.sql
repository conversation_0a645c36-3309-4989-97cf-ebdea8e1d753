ALTER TABLE `writer`
	DROP FOREIGN KEY `writer_parameter_value_id_fk`;
ALTER TABLE `writer`
	ADD CONSTRAINT `writer_parameter_value_id_fk` FOREIGN KEY (`parameterValueWriterId`) REFERENCES `parameter_value` (`id`) ON UPDATE CASCADE ON DELETE CASCADE;

ALTER TABLE `publisher`
	DROP FOREIGN KEY `publisher_parameter_value_id_fk`;
ALTER TABLE `publisher`
	ADD CONSTRAINT `publisher_parameter_value_id_fk` FOREIGN KEY (`parameterValuePublisherId`) REFERENCES `parameter_value` (`id`) ON UPDATE CASCADE ON DELETE CASCADE;


ALTER TABLE `product_variant_price_log`
	DROP FOREIGN KEY `product_variant_price_log_variant`;
ALTER TABLE `product_variant_price_log`
	ADD CONSTRAINT `product_variant_price_log_variant` FOREIGN KEY (`productVariantId`) REFERENCES `product_variant` (`id`) ON UPDATE CASCADE ON DELETE CASCADE;


ALTER TABLE `order_product`
	DROP FOREIGN KEY `order_product_ibfk_2`;
ALTER TABLE `order_product`
	ADD CONSTRAINT `order_product_ibfk_2` FOREIGN KEY (`variantId`) REFERENCES `product_variant` (`id`) ON UPDATE CASCADE ON DELETE CASCADE;


ALTER TABLE `product_parameter`
DROP FOREIGN KEY `product_parameter_ibfk_5`, DROP FOREIGN KEY `product_parameter_ibfk_6`;

ALTER TABLE `product_parameter`
	ADD CONSTRAINT `product_parameter_ibfk_5` FOREIGN KEY (`parameterId`) REFERENCES `parameter` (`id`) ON UPDATE CASCADE ON DELETE CASCADE,
	ADD CONSTRAINT `product_parameter_ibfk_6` FOREIGN KEY (`parameterValueId`) REFERENCES `parameter_value` (`id`) ON UPDATE CASCADE ON DELETE CASCADE;



ALTER TABLE `tag_localization`
DROP FOREIGN KEY `tag_localization_mutation_id_fk`,
	DROP FOREIGN KEY `tag_localization_tag_id_fk`,
	DROP FOREIGN KEY `tag_localization_user_id_fk`;
ALTER TABLE `tag_localization`
	CHANGE COLUMN `mutation` `mutationId` INT(11) NOT NULL AFTER `tagId`,
DROP INDEX `tag_localization_mutation_id_fk`,
	ADD INDEX `tag_localization_mutation_id_fk` (`mutationId`) USING BTREE,
	ADD CONSTRAINT `tag_localization_mutation_id_fk` FOREIGN KEY (`mutationId`) REFERENCES `mutation` (`id`) ON UPDATE CASCADE ON DELETE CASCADE,
	ADD CONSTRAINT `tag_localization_tag_id_fk` FOREIGN KEY (`tagId`) REFERENCES `tag` (`id`) ON UPDATE CASCADE ON DELETE CASCADE,
	ADD CONSTRAINT `tag_localization_user_id_fk` FOREIGN KEY (`edited`) REFERENCES `user` (`id`) ON UPDATE CASCADE ON DELETE SET NULL;
