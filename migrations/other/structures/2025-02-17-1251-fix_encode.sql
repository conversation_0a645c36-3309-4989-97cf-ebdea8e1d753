alter table author collate = utf8mb4_unicode_520_ci;
alter table blog collate = utf8mb4_unicode_520_ci;
alter table blog_tag collate = utf8mb4_unicode_520_ci;
alter table blog_x_author collate = utf8mb4_unicode_520_ci;
alter table blog_x_blog collate = utf8mb4_unicode_520_ci;
alter table blog_x_blog_tag collate = utf8mb4_unicode_520_ci;
alter table export_cache collate = utf8mb4_unicode_520_ci;
alter table file collate = utf8mb4_unicode_520_ci;
alter table form_failed_submission collate = utf8mb4_unicode_520_ci;
alter table holiday collate = utf8mb4_unicode_520_ci;
alter table import_cache collate = utf8mb4_unicode_520_ci;
alter table import_cache_odoo collate = utf8mb4_unicode_520_ci;
alter table import_cache_time collate = utf8mb4_unicode_520_ci;
alter table library_tree collate = utf8mb4_unicode_520_ci;
alter table image collate = utf8mb4_unicode_520_ci;
alter table log collate = utf8mb4_unicode_520_ci;
alter table menu_main collate = utf8mb4_unicode_520_ci;
alter table migrations collate = utf8mb4_unicode_520_ci;
alter table mutation collate = utf8mb4_unicode_520_ci;
alter table alias collate = utf8mb4_unicode_520_ci;
alter table alias_history collate = utf8mb4_unicode_520_ci;
alter table author_localization collate = utf8mb4_unicode_520_ci;
alter table blog_localization collate = utf8mb4_unicode_520_ci;
alter table blog_tag_localization collate = utf8mb4_unicode_520_ci;
alter table delivery_method collate = utf8mb4_unicode_520_ci;
alter table delivery_method_currency collate = utf8mb4_unicode_520_ci;
alter table elastic_search_index collate = utf8mb4_unicode_520_ci;
alter table email_template collate = utf8mb4_unicode_520_ci;
alter table email_template_file collate = utf8mb4_unicode_520_ci;
alter table menu_main_localization collate = utf8mb4_unicode_520_ci;
alter table newsletter_email collate = utf8mb4_unicode_520_ci;
alter table order_number_sequence collate = utf8mb4_unicode_520_ci;
alter table order_payment_information collate = utf8mb4_unicode_520_ci;
alter table card_payment collate = utf8mb4_unicode_520_ci;
alter table card_payment_status_change collate = utf8mb4_unicode_520_ci;
alter table order_sync_history collate = utf8mb4_unicode_520_ci;
alter table parameter collate = utf8mb4_unicode_520_ci;
alter table parameter_value collate = utf8mb4_unicode_520_ci;
alter table discount collate = utf8mb4_unicode_520_ci;
alter table discount_localization collate = utf8mb4_unicode_520_ci;
alter table payment_method collate = utf8mb4_unicode_520_ci;
alter table delivery_method_x_payment_method collate = utf8mb4_unicode_520_ci;
alter table order_payment collate = utf8mb4_unicode_520_ci;
alter table payment_method_currency collate = utf8mb4_unicode_520_ci;
alter table pickup_point collate = utf8mb4_unicode_520_ci;
alter table price_level collate = utf8mb4_unicode_520_ci;
alter table product_type collate = utf8mb4_unicode_520_ci;
alter table product collate = utf8mb4_unicode_520_ci;
alter table class_section collate = utf8mb4_unicode_520_ci;
alter table discount_product collate = utf8mb4_unicode_520_ci;
alter table gift collate = utf8mb4_unicode_520_ci;
alter table gift_localization collate = utf8mb4_unicode_520_ci;
alter table gift_x_product collate = utf8mb4_unicode_520_ci;
alter table product_image collate = utf8mb4_unicode_520_ci;
alter table product_localization collate = utf8mb4_unicode_520_ci;
alter table product_file collate = utf8mb4_unicode_520_ci;
alter table product_parameter collate = utf8mb4_unicode_520_ci;
alter table product_product collate = utf8mb4_unicode_520_ci;
alter table product_redirect collate = utf8mb4_unicode_520_ci;
alter table product_variant collate = utf8mb4_unicode_520_ci;
alter table product_variant_localization collate = utf8mb4_unicode_520_ci;
alter table product_variant_price collate = utf8mb4_unicode_520_ci;
alter table product_variant_price_log collate = utf8mb4_unicode_520_ci;
alter table promotion collate = utf8mb4_unicode_520_ci;
alter table promotion_localization collate = utf8mb4_unicode_520_ci;
alter table promotion_x_product collate = utf8mb4_unicode_520_ci;
alter table publisher collate = utf8mb4_unicode_520_ci;
alter table publisher_localization collate = utf8mb4_unicode_520_ci;
alter table rate collate = utf8mb4_unicode_520_ci;
alter table redirect collate = utf8mb4_unicode_520_ci;
alter table review collate = utf8mb4_unicode_520_ci;
alter table review_x_product collate = utf8mb4_unicode_520_ci;
alter table search_log collate = utf8mb4_unicode_520_ci;
alter table seolink collate = utf8mb4_unicode_520_ci;
alter table seolink_localization collate = utf8mb4_unicode_520_ci;
alter table seolink_x_parameter_value collate = utf8mb4_unicode_520_ci;
alter table series collate = utf8mb4_unicode_520_ci;
alter table series_localization collate = utf8mb4_unicode_520_ci;
alter table series_x_product collate = utf8mb4_unicode_520_ci;
alter table state collate = utf8mb4_unicode_520_ci;
alter table class_event collate = utf8mb4_unicode_520_ci;
alter table delivery_method_price collate = utf8mb4_unicode_520_ci;
alter table delivery_method_x_state collate = utf8mb4_unicode_520_ci;
alter table ip_address collate = utf8mb4_unicode_520_ci;
alter table mutation_state collate = utf8mb4_unicode_520_ci;
alter table order_delivery_information collate = utf8mb4_unicode_520_ci;
alter table order_delivery collate = utf8mb4_unicode_520_ci;
alter table payment_method_price collate = utf8mb4_unicode_520_ci;
alter table payment_method_x_state collate = utf8mb4_unicode_520_ci;
alter table stock collate = utf8mb4_unicode_520_ci;
alter table string collate = utf8mb4_unicode_520_ci;
alter table tag collate = utf8mb4_unicode_520_ci;
alter table tag_x_product collate = utf8mb4_unicode_520_ci;
alter table theme collate = utf8mb4_unicode_520_ci;
alter table theme_localization collate = utf8mb4_unicode_520_ci;
alter table theme_localization_visit collate = utf8mb4_unicode_520_ci;
alter table theme_x_product collate = utf8mb4_unicode_520_ci;
alter table tree_alternative collate = utf8mb4_unicode_520_ci;
alter table tree_parent collate = utf8mb4_unicode_520_ci;
alter table tree collate = utf8mb4_unicode_520_ci;
alter table blog_localization_tree collate = utf8mb4_unicode_520_ci;
alter table product_tree collate = utf8mb4_unicode_520_ci;
alter table tree_file collate = utf8mb4_unicode_520_ci;
alter table tree_product collate = utf8mb4_unicode_520_ci;
alter table tree_tree collate = utf8mb4_unicode_520_ci;
alter table user collate = utf8mb4_unicode_520_ci;
alter table api_token collate = utf8mb4_unicode_520_ci;
alter table my_library collate = utf8mb4_unicode_520_ci;
alter table my_library_product collate = utf8mb4_unicode_520_ci;
alter table `order` collate = utf8mb4_unicode_520_ci;
alter table order_discount collate = utf8mb4_unicode_520_ci;
alter table order_gift collate = utf8mb4_unicode_520_ci;
alter table order_product collate = utf8mb4_unicode_520_ci;
alter table order_promotion collate = utf8mb4_unicode_520_ci;
alter table order_state_change collate = utf8mb4_unicode_520_ci;
alter table product_review collate = utf8mb4_unicode_520_ci;
alter table supplier collate = utf8mb4_unicode_520_ci;
alter table stock_supplies collate = utf8mb4_unicode_520_ci;
alter table system_message collate = utf8mb4_unicode_520_ci;
alter table tag_localization collate = utf8mb4_unicode_520_ci;
alter table user_hash collate = utf8mb4_unicode_520_ci;
alter table user_mutation collate = utf8mb4_unicode_520_ci;
alter table user_product collate = utf8mb4_unicode_520_ci;
alter table voucher collate = utf8mb4_unicode_520_ci;
alter table voucher_code collate = utf8mb4_unicode_520_ci;
alter table order_voucher collate = utf8mb4_unicode_520_ci;
alter table voucher_limit collate = utf8mb4_unicode_520_ci;
alter table watchdog collate = utf8mb4_unicode_520_ci;
alter table writer collate = utf8mb4_unicode_520_ci;
alter table writer_localization collate = utf8mb4_unicode_520_ci;

ALTER TABLE `alias` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `alias_history` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `api_token` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `author` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `author_localization` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `blog` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `blog_localization` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `blog_localization_tree` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `blog_tag` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `blog_tag_localization` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `blog_x_author` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `blog_x_blog` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `blog_x_blog_tag` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `card_payment` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `card_payment_status_change` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `delivery_method` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `delivery_method_currency` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `delivery_method_price` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `discount` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `discount_localization` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `email_template` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `email_template_file` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `file` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `form_failed_submission` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `gift` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `gift_localization` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `image` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `library_tree` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `log` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `menu_main` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `menu_main_localization` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `mutation` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `order` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `order_delivery` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `order_payment` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `order_product` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `parameter` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `payment_method` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `price_level` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `product` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `product_localization` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `promotion` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `promotion_localization` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `rate` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `redirect` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `review` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `search_log` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `state` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `stock` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `string` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `supplier` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `tag` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `theme` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `tree` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;


ALTER TABLE `product_file` CHANGE COLUMN `name` `name` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `product_file` CHANGE COLUMN `url` `url` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `class_section` CHANGE COLUMN `name` `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `order_payment_information` CHANGE COLUMN `externalId` `externalId` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `order_payment_information` CHANGE COLUMN `type` `type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `order_payment_information` CHANGE COLUMN `state` `state` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `order_payment_information` CHANGE COLUMN `variableSymbol` `variableSymbol` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `review` CHANGE COLUMN `name` `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `review` CHANGE COLUMN `perex` `perex` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `review` CHANGE COLUMN `url` `url` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `review` CHANGE COLUMN `author` `author` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `holiday` CHANGE COLUMN `name` `name` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `state` CHANGE COLUMN `extId` `extId` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `state` CHANGE COLUMN `code` `code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `state` CHANGE COLUMN `name` `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `image` CHANGE COLUMN `name` `name` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `image` CHANGE COLUMN `filename` `filename` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `image` CHANGE COLUMN `sourceImage` `sourceImage` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `image` CHANGE COLUMN `md5` `md5` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `image` CHANGE COLUMN `alts` `alts` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `writer_localization` CHANGE COLUMN `syncChecksum` `syncChecksum` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `writer_localization` CHANGE COLUMN `name` `name` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `writer_localization` CHANGE COLUMN `firstName` `firstName` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `writer_localization` CHANGE COLUMN `lastName` `lastName` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `writer_localization` CHANGE COLUMN `nameAnchor` `nameAnchor` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `writer_localization` CHANGE COLUMN `nameTitle` `nameTitle` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `writer_localization` CHANGE COLUMN `lastNameFirstLetter` `lastNameFirstLetter` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `writer_localization` CHANGE COLUMN `description` `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `writer_localization` CHANGE COLUMN `keywords` `keywords` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `writer_localization` CHANGE COLUMN `title` `title` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `writer_localization` CHANGE COLUMN `customFieldsJson` `customFieldsJson` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `writer_localization` CHANGE COLUMN `customContentJson` `customContentJson` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `newsletter_email` CHANGE COLUMN `email` `email` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `export_cache` CHANGE COLUMN `status` `status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `export_cache` CHANGE COLUMN `model` `model` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `export_cache` CHANGE COLUMN `message` `message` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `export_cache` CHANGE COLUMN `data` `data` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `export_cache` CHANGE COLUMN `response` `response` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `form_failed_submission` CHANGE COLUMN `form_type` `form_type` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `form_failed_submission` CHANGE COLUMN `discriminator` `discriminator` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `tree_alternative` CHANGE COLUMN `type` `type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `tree_alternative` CHANGE COLUMN `alt_name` `alt_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `tree_alternative` CHANGE COLUMN `alt_path` `alt_path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `tree_alternative` CHANGE COLUMN `syncChecksum` `syncChecksum` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `tree_alternative` CHANGE COLUMN `extId` `extId` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `search_log` CHANGE COLUMN `text` `text` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `search_log` CHANGE COLUMN `agent` `agent` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `supplier` CHANGE COLUMN `erpCode` `erpCode` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `supplier` CHANGE COLUMN `internalName` `internalName` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `supplier` CHANGE COLUMN `customFieldsJson` `customFieldsJson` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `supplier` CHANGE COLUMN `syncChecksum` `syncChecksum` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `payment_method_price` CHANGE COLUMN `externalId` `externalId` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `payment_method_price` CHANGE COLUMN `price_currency` `price_currency` char(3) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `redirect` CHANGE COLUMN `oldUrl` `oldUrl` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `redirect` CHANGE COLUMN `newUrl` `newUrl` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `delivery_method` CHANGE COLUMN `externalId` `externalId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `delivery_method` CHANGE COLUMN `deliveryMethodUniqueIdentifier` `deliveryMethodUniqueIdentifier` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `delivery_method` CHANGE COLUMN `name` `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `delivery_method` CHANGE COLUMN `desc` `desc` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `delivery_method` CHANGE COLUMN `pageText` `pageText` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `delivery_method` CHANGE COLUMN `tooltip` `tooltip` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `delivery_method` CHANGE COLUMN `deliveryHourByStock` `deliveryHourByStock` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `delivery_method` CHANGE COLUMN `vats` `vats` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `delivery_method` CHANGE COLUMN `customFieldsJson` `customFieldsJson` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `email_template_file` CHANGE COLUMN `name` `name` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `email_template_file` CHANGE COLUMN `url` `url` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `product_product` CHANGE COLUMN `type` `type` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `user_hash` CHANGE COLUMN `type` `type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `user_hash` CHANGE COLUMN `hash` `hash` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `user_hash` CHANGE COLUMN `data` `data` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `seolink_localization` CHANGE COLUMN `name` `name` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `seolink_localization` CHANGE COLUMN `nameAnchor` `nameAnchor` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `seolink_localization` CHANGE COLUMN `nameTitle` `nameTitle` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `seolink_localization` CHANGE COLUMN `description` `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `seolink_localization` CHANGE COLUMN `keywords` `keywords` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `seolink_localization` CHANGE COLUMN `title` `title` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `seolink_localization` CHANGE COLUMN `customFieldsJson` `customFieldsJson` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `seolink_localization` CHANGE COLUMN `customContentJson` `customContentJson` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `voucher_limit` CHANGE COLUMN `limit` `limit` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `log` CHANGE COLUMN `message` `message` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `log` CHANGE COLUMN `context` `context` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `gift_localization` CHANGE COLUMN `name` `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `gift_localization` CHANGE COLUMN `customFieldsJson` `customFieldsJson` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `gift_localization` CHANGE COLUMN `customContentJson` `customContentJson` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `import_cache_time` CHANGE COLUMN `type` `type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `import_cache_time` CHANGE COLUMN `history` `history` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `order` CHANGE COLUMN `cookieHash` `cookieHash` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `order` CHANGE COLUMN `extId` `extId` varchar(2558) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `order` CHANGE COLUMN `extState` `extState` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `order` CHANGE COLUMN `hash` `hash` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `order` CHANGE COLUMN `state` `state` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `order` CHANGE COLUMN `orderNumber` `orderNumber` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `order` CHANGE COLUMN `email` `email` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `order` CHANGE COLUMN `name` `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `order` CHANGE COLUMN `street` `street` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `order` CHANGE COLUMN `city` `city` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `order` CHANGE COLUMN `zip` `zip` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `order` CHANGE COLUMN `phone` `phone` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `order` CHANGE COLUMN `note` `note` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `order` CHANGE COLUMN `companyName` `companyName` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `order` CHANGE COLUMN `companyIdentifier` `companyIdentifier` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `order` CHANGE COLUMN `vatNumber` `vatNumber` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `order` CHANGE COLUMN `currency` `currency` char(3) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `order` CHANGE COLUMN `metadata` `metadata` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `order` CHANGE COLUMN `syncChecksum` `syncChecksum` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `order` CHANGE COLUMN `heurekaResponse` `heurekaResponse` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `voucher` CHANGE COLUMN `syncChecksum` `syncChecksum` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `voucher` CHANGE COLUMN `name` `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `voucher` CHANGE COLUMN `internalName` `internalName` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `voucher` CHANGE COLUMN `type` `type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `voucher` CHANGE COLUMN `combinationType` `combinationType` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `voucher` CHANGE COLUMN `discount_currency` `discount_currency` char(3) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `parameter_value` CHANGE COLUMN `internalValue` `internalValue` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `parameter_value` CHANGE COLUMN `internalAlias` `internalAlias` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `parameter_value` CHANGE COLUMN `extId` `extId` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `parameter_value` CHANGE COLUMN `customFieldsJson` `customFieldsJson` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `theme_localization` CHANGE COLUMN `name` `name` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `theme_localization` CHANGE COLUMN `nameAnchor` `nameAnchor` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `theme_localization` CHANGE COLUMN `nameTitle` `nameTitle` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `theme_localization` CHANGE COLUMN `description` `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `theme_localization` CHANGE COLUMN `keywords` `keywords` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `theme_localization` CHANGE COLUMN `title` `title` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `theme_localization` CHANGE COLUMN `customFieldsJson` `customFieldsJson` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `theme_localization` CHANGE COLUMN `customContentJson` `customContentJson` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `seolink` CHANGE COLUMN `internalName` `internalName` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `seolink` CHANGE COLUMN `customFieldsJson` `customFieldsJson` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `seolink` CHANGE COLUMN `parameterValuesIds` `parameterValuesIds` varchar(768) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `product_type` CHANGE COLUMN `uid` `uid` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `product_type` CHANGE COLUMN `name` `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `product_type` CHANGE COLUMN `icon` `icon` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `tag_localization` CHANGE COLUMN `name` `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `tag_localization` CHANGE COLUMN `description` `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `tag_localization` CHANGE COLUMN `nameAnchor` `nameAnchor` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `tag_localization` CHANGE COLUMN `nameTitle` `nameTitle` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `tag_localization` CHANGE COLUMN `title` `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `tag_localization` CHANGE COLUMN `CustomContentJson` `CustomContentJson` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `tag_localization` CHANGE COLUMN `customFieldsJson` `customFieldsJson` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `tag_localization` CHANGE COLUMN `keywords` `keywords` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `pickup_point` CHANGE COLUMN `extId` `extId` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `pickup_point` CHANGE COLUMN `name` `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `pickup_point` CHANGE COLUMN `address` `address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `pickup_point` CHANGE COLUMN `lat` `lat` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `pickup_point` CHANGE COLUMN `lng` `lng` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `pickup_point` CHANGE COLUMN `openingHours` `openingHours` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `pickup_point` CHANGE COLUMN `image` `image` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `blog_tag_localization` CHANGE COLUMN `name` `name` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `blog_tag_localization` CHANGE COLUMN `nameAnchor` `nameAnchor` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `blog_tag_localization` CHANGE COLUMN `nameTitle` `nameTitle` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `blog_tag_localization` CHANGE COLUMN `description` `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `blog_tag_localization` CHANGE COLUMN `keywords` `keywords` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `blog_tag_localization` CHANGE COLUMN `customFieldsJson` `customFieldsJson` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `blog_tag_localization` CHANGE COLUMN `customContentJson` `customContentJson` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `user` CHANGE COLUMN `email` `email` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `user` CHANGE COLUMN `password` `password` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `user` CHANGE COLUMN `role` `role` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `user` CHANGE COLUMN `firstname` `firstname` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `user` CHANGE COLUMN `lastname` `lastname` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `user` CHANGE COLUMN `phone` `phone` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `user` CHANGE COLUMN `street` `street` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `user` CHANGE COLUMN `city` `city` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `user` CHANGE COLUMN `zip` `zip` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `user` CHANGE COLUMN `company` `company` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `user` CHANGE COLUMN `ic` `ic` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `user` CHANGE COLUMN `dic` `dic` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `user` CHANGE COLUMN `customAddressJson` `customAddressJson` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `user` CHANGE COLUMN `customFieldsJson` `customFieldsJson` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `user` CHANGE COLUMN `googleId` `googleId` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `user` CHANGE COLUMN `facebookId` `facebookId` varchar(99) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `user` CHANGE COLUMN `preferredCurrency` `preferredCurrency` varchar(5) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `user` CHANGE COLUMN `seznamId` `seznamId` varchar(99) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `product` CHANGE COLUMN `itemType` `itemType` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `product` CHANGE COLUMN `uid` `uid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `product` CHANGE COLUMN `template` `template` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `product` CHANGE COLUMN `syncChecksum` `syncChecksum` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `product` CHANGE COLUMN `internalName` `internalName` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `product` CHANGE COLUMN `availability` `availability` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `product` CHANGE COLUMN `damagedType` `damagedType` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `product` CHANGE COLUMN `customFieldsJson` `customFieldsJson` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `product` CHANGE COLUMN `vats` `vats` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `product` CHANGE COLUMN `extId` `extId` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `product` CHANGE COLUMN `similarBuyProducts` `similarBuyProducts` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `parameter` CHANGE COLUMN `name` `name` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `parameter` CHANGE COLUMN `uid` `uid` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `parameter` CHANGE COLUMN `customFieldsJson` `customFieldsJson` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `parameter` CHANGE COLUMN `extId` `extId` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `parameter` CHANGE COLUMN `typeSort` `typeSort` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `blog_tag` CHANGE COLUMN `internalName` `internalName` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `blog_tag` CHANGE COLUMN `customFieldsJson` `customFieldsJson` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `file` CHANGE COLUMN `name` `name` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `file` CHANGE COLUMN `filename` `filename` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `import_cache` CHANGE COLUMN `extId` `extId` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `import_cache` CHANGE COLUMN `extChecksum` `extChecksum` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `import_cache` CHANGE COLUMN `message` `message` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `import_cache` CHANGE COLUMN `data` `data` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `elastic_search_index` CHANGE COLUMN `type` `type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `elastic_search_index` CHANGE COLUMN `name` `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `elastic_search_index` CHANGE COLUMN `status` `status` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `elastic_search_index` CHANGE COLUMN `errorDetail` `errorDetail` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `migrations` CHANGE COLUMN `group` `group` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `migrations` CHANGE COLUMN `file` `file` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `migrations` CHANGE COLUMN `checksum` `checksum` char(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `email_template` CHANGE COLUMN `key` `key` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `email_template` CHANGE COLUMN `name` `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `email_template` CHANGE COLUMN `subject` `subject` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `email_template` CHANGE COLUMN `body` `body` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `stock` CHANGE COLUMN `name` `name` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `stock` CHANGE COLUMN `alias` `alias` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `stock` CHANGE COLUMN `address` `address` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `stock` CHANGE COLUMN `deliveryHour` `deliveryHour` varchar(5) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `stock` CHANGE COLUMN `extId` `extId` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `author` CHANGE COLUMN `internalName` `internalName` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `author` CHANGE COLUMN `customFieldsJson` `customFieldsJson` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `alias` CHANGE COLUMN `alias` `alias` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `alias` CHANGE COLUMN `module` `module` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `publisher_localization` CHANGE COLUMN `name` `name` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `publisher_localization` CHANGE COLUMN `nameAnchor` `nameAnchor` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `publisher_localization` CHANGE COLUMN `nameTitle` `nameTitle` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `publisher_localization` CHANGE COLUMN `description` `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `publisher_localization` CHANGE COLUMN `keywords` `keywords` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `publisher_localization` CHANGE COLUMN `title` `title` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `publisher_localization` CHANGE COLUMN `customFieldsJson` `customFieldsJson` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `publisher_localization` CHANGE COLUMN `customContentJson` `customContentJson` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `publisher_localization` CHANGE COLUMN `syncChecksum` `syncChecksum` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `system_message` CHANGE COLUMN `internalName` `internalName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `system_message` CHANGE COLUMN `customFieldsJson` `customFieldsJson` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `system_message` CHANGE COLUMN `description` `description` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `tree_product` CHANGE COLUMN `type` `type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `product_review` CHANGE COLUMN `syncChecksum` `syncChecksum` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `product_review` CHANGE COLUMN `importCode` `importCode` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `product_review` CHANGE COLUMN `name` `name` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `product_review` CHANGE COLUMN `email` `email` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `product_review` CHANGE COLUMN `text` `text` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `product_review` CHANGE COLUMN `status` `status` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `product_review` CHANGE COLUMN `source` `source` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `product_review` CHANGE COLUMN `customFieldsJson` `customFieldsJson` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `promotion` CHANGE COLUMN `type` `type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `promotion` CHANGE COLUMN `discountType` `discountType` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `promotion` CHANGE COLUMN `typeBonusItem` `typeBonusItem` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `promotion` CHANGE COLUMN `typeQuantity` `typeQuantity` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `promotion` CHANGE COLUMN `internalName` `internalName` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `promotion` CHANGE COLUMN `customFieldsJson` `customFieldsJson` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `string` CHANGE COLUMN `lg` `lg` varchar(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `string` CHANGE COLUMN `name` `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `string` CHANGE COLUMN `value` `value` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `card_payment_status_change` CHANGE COLUMN `from` `from` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `card_payment_status_change` CHANGE COLUMN `to` `to` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `writer` CHANGE COLUMN `internalName` `internalName` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `writer` CHANGE COLUMN `customFieldsJson` `customFieldsJson` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `payment_method_currency` CHANGE COLUMN `currency` `currency` char(3) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `price_level` CHANGE COLUMN `type` `type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `price_level` CHANGE COLUMN `name` `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `tag` CHANGE COLUMN `internalName` `internalName` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `tag` CHANGE COLUMN `customFieldsJson` `customFieldsJson` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `tag` CHANGE COLUMN `type` `type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `tag` CHANGE COLUMN `color` `color` varchar(7) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `tree_file` CHANGE COLUMN `name` `name` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `tree_file` CHANGE COLUMN `url` `url` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `alias_history` CHANGE COLUMN `alias` `alias` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `alias_history` CHANGE COLUMN `module` `module` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `order_delivery_information` CHANGE COLUMN `externalId` `externalId` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `order_delivery_information` CHANGE COLUMN `type` `type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `order_delivery_information` CHANGE COLUMN `name` `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `order_delivery_information` CHANGE COLUMN `company` `company` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `order_delivery_information` CHANGE COLUMN `street` `street` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `order_delivery_information` CHANGE COLUMN `city` `city` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `order_delivery_information` CHANGE COLUMN `zip` `zip` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `order_delivery_information` CHANGE COLUMN `phoneNumber` `phoneNumber` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `order_delivery_information` CHANGE COLUMN `trackingCode` `trackingCode` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `order_delivery_information` CHANGE COLUMN `pickupPointId` `pickupPointId` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `promotion_localization` CHANGE COLUMN `name` `name` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `promotion_localization` CHANGE COLUMN `customFieldsJson` `customFieldsJson` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `promotion_localization` CHANGE COLUMN `customContentJson` `customContentJson` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `publisher` CHANGE COLUMN `internalName` `internalName` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `publisher` CHANGE COLUMN `customFieldsJson` `customFieldsJson` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `library_tree` CHANGE COLUMN `path` `path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `library_tree` CHANGE COLUMN `name` `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `library_tree` CHANGE COLUMN `nameTitle` `nameTitle` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `library_tree` CHANGE COLUMN `nameAnchor` `nameAnchor` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `library_tree` CHANGE COLUMN `uid` `uid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `product_variant` CHANGE COLUMN `ean` `ean` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `product_variant` CHANGE COLUMN `isbn` `isbn` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `product_variant` CHANGE COLUMN `code` `code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `product_variant` CHANGE COLUMN `extId` `extId` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `series_localization` CHANGE COLUMN `syncChecksum` `syncChecksum` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `series_localization` CHANGE COLUMN `name` `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `series_localization` CHANGE COLUMN `nameAnchor` `nameAnchor` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `series_localization` CHANGE COLUMN `nameTitle` `nameTitle` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `series_localization` CHANGE COLUMN `description` `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `series_localization` CHANGE COLUMN `keywords` `keywords` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `series_localization` CHANGE COLUMN `title` `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `series_localization` CHANGE COLUMN `customFieldsJson` `customFieldsJson` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `series_localization` CHANGE COLUMN `customContentJson` `customContentJson` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `api_token` CHANGE COLUMN `token` `token` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `api_token` CHANGE COLUMN `description` `description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `api_token` CHANGE COLUMN `scope` `scope` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `product_variant_localization` CHANGE COLUMN `name` `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `voucher_code` CHANGE COLUMN `code` `code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `menu_main` CHANGE COLUMN `internalName` `internalName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `menu_main` CHANGE COLUMN `customFieldsJson` `customFieldsJson` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `order_product` CHANGE COLUMN `unitPrice_currency` `unitPrice_currency` char(3) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `order_product` CHANGE COLUMN `vatRate` `vatRate` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `order_product` CHANGE COLUMN `variantCode` `variantCode` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `order_product` CHANGE COLUMN `variantName` `variantName` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `order_product` CHANGE COLUMN `availabilityShortText` `availabilityShortText` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `order_product` CHANGE COLUMN `availabilityText` `availabilityText` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `order_product` CHANGE COLUMN `availabilityType` `availabilityType` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `order_promotion` CHANGE COLUMN `unitPrice_currency` `unitPrice_currency` char(3) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `order_promotion` CHANGE COLUMN `vatRate` `vatRate` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `order_promotion` CHANGE COLUMN `promotionName` `promotionName` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `order_promotion` CHANGE COLUMN `promotionInfo` `promotionInfo` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `order_promotion` CHANGE COLUMN `promotionUniqId` `promotionUniqId` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `blog` CHANGE COLUMN `internalName` `internalName` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `blog` CHANGE COLUMN `customFieldsJson` `customFieldsJson` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `blog_localization` CHANGE COLUMN `name` `name` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `blog_localization` CHANGE COLUMN `nameAnchor` `nameAnchor` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `blog_localization` CHANGE COLUMN `nameTitle` `nameTitle` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `blog_localization` CHANGE COLUMN `description` `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `blog_localization` CHANGE COLUMN `keywords` `keywords` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `blog_localization` CHANGE COLUMN `customFieldsJson` `customFieldsJson` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `blog_localization` CHANGE COLUMN `customContentSchemeJson` `customContentSchemeJson` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `blog_localization` CHANGE COLUMN `customContentJson` `customContentJson` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `mutation` CHANGE COLUMN `langCode` `langCode` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `mutation` CHANGE COLUMN `isoCodePrefix` `isoCodePrefix` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `mutation` CHANGE COLUMN `name` `name` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `mutation` CHANGE COLUMN `langMenu` `langMenu` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `mutation` CHANGE COLUMN `adminEmail` `adminEmail` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `mutation` CHANGE COLUMN `contactEmail` `contactEmail` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `mutation` CHANGE COLUMN `orderEmail` `orderEmail` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `mutation` CHANGE COLUMN `fromEmail` `fromEmail` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `mutation` CHANGE COLUMN `fromEmailName` `fromEmailName` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `mutation` CHANGE COLUMN `currency` `currency` char(3) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `mutation` CHANGE COLUMN `synonyms` `synonyms` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `mutation` CHANGE COLUMN `heurekaOverenoKey` `heurekaOverenoKey` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `mutation` CHANGE COLUMN `customFieldsJson` `customFieldsJson` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `mutation` CHANGE COLUMN `urlReviewPrefix` `urlReviewPrefix` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `series` CHANGE COLUMN `uid` `uid` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `series` CHANGE COLUMN `internalName` `internalName` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `series` CHANGE COLUMN `customFieldsJson` `customFieldsJson` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `product_variant_price` CHANGE COLUMN `price_currency` `price_currency` char(3) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `discount` CHANGE COLUMN `internalName` `internalName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `discount` CHANGE COLUMN `customFieldsJson` `customFieldsJson` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `order_delivery` CHANGE COLUMN `deliveryMethodName` `deliveryMethodName` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `order_delivery` CHANGE COLUMN `vatRate` `vatRate` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `order_delivery` CHANGE COLUMN `unitPrice_currency` `unitPrice_currency` char(3) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `delivery_method_price` CHANGE COLUMN `externalId` `externalId` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `delivery_method_price` CHANGE COLUMN `price_currency` `price_currency` char(3) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `order_state_change` CHANGE COLUMN `from` `from` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `order_state_change` CHANGE COLUMN `to` `to` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `order_gift` CHANGE COLUMN `unitPrice_currency` `unitPrice_currency` char(3) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `order_gift` CHANGE COLUMN `vatRate` `vatRate` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `order_gift` CHANGE COLUMN `giftName` `giftName` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `order_payment` CHANGE COLUMN `paymentMethodName` `paymentMethodName` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `order_payment` CHANGE COLUMN `vatRate` `vatRate` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `order_payment` CHANGE COLUMN `unitPrice_currency` `unitPrice_currency` char(3) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `class_event` CHANGE COLUMN `name` `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `rate` CHANGE COLUMN `currency` `currency` char(3) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `card_payment` CHANGE COLUMN `paymentGatewayUniqueIdentifier` `paymentGatewayUniqueIdentifier` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `card_payment` CHANGE COLUMN `externalId` `externalId` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `card_payment` CHANGE COLUMN `externalUrl` `externalUrl` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `card_payment` CHANGE COLUMN `status` `status` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `card_payment` CHANGE COLUMN `response` `response` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `order_sync_history` CHANGE COLUMN `type` `type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `order_sync_history` CHANGE COLUMN `orderNumber` `orderNumber` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `order_sync_history` CHANGE COLUMN `data` `data` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `order_sync_history` CHANGE COLUMN `response` `response` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `gift` CHANGE COLUMN `internalName` `internalName` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `gift` CHANGE COLUMN `price_currency` `price_currency` char(3) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `gift` CHANGE COLUMN `customFieldsJson` `customFieldsJson` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `product_image` CHANGE COLUMN `name` `name` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `product_image` CHANGE COLUMN `variants` `variants` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `product_image` CHANGE COLUMN `data` `data` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `product_image` CHANGE COLUMN `extId` `extId` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `watchdog` CHANGE COLUMN `email` `email` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `watchdog` CHANGE COLUMN `relatedSended` `relatedSended` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `payment_method` CHANGE COLUMN `paymentMethodUniqueIdentifier` `paymentMethodUniqueIdentifier` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `payment_method` CHANGE COLUMN `name` `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `payment_method` CHANGE COLUMN `desc` `desc` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `payment_method` CHANGE COLUMN `pageText` `pageText` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `payment_method` CHANGE COLUMN `tooltip` `tooltip` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `payment_method` CHANGE COLUMN `vats` `vats` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `payment_method` CHANGE COLUMN `customFieldsJson` `customFieldsJson` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `tree` CHANGE COLUMN `path` `path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `tree` CHANGE COLUMN `uid` `uid` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `tree` CHANGE COLUMN `template` `template` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `tree` CHANGE COLUMN `type` `type` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `tree` CHANGE COLUMN `name` `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `tree` CHANGE COLUMN `nameAnchor` `nameAnchor` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `tree` CHANGE COLUMN `nameAnchorBreadcrumb` `nameAnchorBreadcrumb` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `tree` CHANGE COLUMN `nameTitle` `nameTitle` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `tree` CHANGE COLUMN `nameShort` `nameShort` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `tree` CHANGE COLUMN `nameHeading` `nameHeading` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `tree` CHANGE COLUMN `description` `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `tree` CHANGE COLUMN `keywords` `keywords` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `tree` CHANGE COLUMN `annotation` `annotation` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `tree` CHANGE COLUMN `content` `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `tree` CHANGE COLUMN `links` `links` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `tree` CHANGE COLUMN `seoTitleFilter` `seoTitleFilter` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `tree` CHANGE COLUMN `seoAnnotationFilter` `seoAnnotationFilter` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `tree` CHANGE COLUMN `seoDescriptionFilter` `seoDescriptionFilter` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `tree` CHANGE COLUMN `videos` `videos` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `tree` CHANGE COLUMN `customFieldsJson` `customFieldsJson` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `tree` CHANGE COLUMN `customContentJson` `customContentJson` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `theme` CHANGE COLUMN `internalName` `internalName` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `theme` CHANGE COLUMN `customFieldsJson` `customFieldsJson` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `tree_tree` CHANGE COLUMN `type` `type` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `author_localization` CHANGE COLUMN `name` `name` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `author_localization` CHANGE COLUMN `nameAnchor` `nameAnchor` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `author_localization` CHANGE COLUMN `nameTitle` `nameTitle` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `author_localization` CHANGE COLUMN `description` `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `author_localization` CHANGE COLUMN `keywords` `keywords` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `author_localization` CHANGE COLUMN `customFieldsJson` `customFieldsJson` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `author_localization` CHANGE COLUMN `customContentJson` `customContentJson` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `delivery_method_currency` CHANGE COLUMN `currency` `currency` char(3) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `menu_main_localization` CHANGE COLUMN `name` `name` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `menu_main_localization` CHANGE COLUMN `nameAnchor` `nameAnchor` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `menu_main_localization` CHANGE COLUMN `nameTitle` `nameTitle` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `menu_main_localization` CHANGE COLUMN `description` `description` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `menu_main_localization` CHANGE COLUMN `keywords` `keywords` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `menu_main_localization` CHANGE COLUMN `title` `title` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `menu_main_localization` CHANGE COLUMN `customFieldsJson` `customFieldsJson` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `menu_main_localization` CHANGE COLUMN `customContentJson` `customContentJson` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `ip_address` CHANGE COLUMN `ipAddress` `ipAddress` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `product_localization` CHANGE COLUMN `name` `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `product_localization` CHANGE COLUMN `nameTitle` `nameTitle` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `product_localization` CHANGE COLUMN `nameAnchor` `nameAnchor` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `product_localization` CHANGE COLUMN `description` `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `product_localization` CHANGE COLUMN `keywords` `keywords` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `product_localization` CHANGE COLUMN `annotation` `annotation` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `product_localization` CHANGE COLUMN `content` `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `product_localization` CHANGE COLUMN `setup` `setup` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `product_localization` CHANGE COLUMN `customFieldsJson` `customFieldsJson` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `product_localization` CHANGE COLUMN `customContentJson` `customContentJson` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `my_library` CHANGE COLUMN `uid` `uid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `order_discount` CHANGE COLUMN `unitPrice_currency` `unitPrice_currency` char(3) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `order_discount` CHANGE COLUMN `vatRate` `vatRate` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `order_discount` CHANGE COLUMN `discountTypeUniqueIdentifier` `discountTypeUniqueIdentifier` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `discount_localization` CHANGE COLUMN `name` `name` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `discount_localization` CHANGE COLUMN `nameAnchor` `nameAnchor` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `discount_localization` CHANGE COLUMN `nameTitle` `nameTitle` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `discount_localization` CHANGE COLUMN `description` `description` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `discount_localization` CHANGE COLUMN `keywords` `keywords` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `discount_localization` CHANGE COLUMN `title` `title` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `discount_localization` CHANGE COLUMN `customFieldsJson` `customFieldsJson` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `discount_localization` CHANGE COLUMN `customContentJson` `customContentJson` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `order_voucher` CHANGE COLUMN `unitPrice_currency` `unitPrice_currency` char(3) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `order_voucher` CHANGE COLUMN `vatRate` `vatRate` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `order_voucher` CHANGE COLUMN `voucherName` `voucherName` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `order_voucher` CHANGE COLUMN `voucherCodeString` `voucherCodeString` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `order_voucher` CHANGE COLUMN `voucherType` `voucherType` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `import_cache_odoo` CHANGE COLUMN `status` `status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `import_cache_odoo` CHANGE COLUMN `type` `type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `import_cache_odoo` CHANGE COLUMN `message` `message` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `import_cache_odoo` CHANGE COLUMN `extId` `extId` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
ALTER TABLE `import_cache_odoo` CHANGE COLUMN `data` `data` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci;
