CREATE TABLE `user_interest` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `uid` varchar(20) NOT NULL,
  `name` varchar(100) NOT NULL,
  `public` tinyint(1) NOT NULL DEFAULT 0,
  PRIMARY KEY (`id`),
  UNIQUE KEY `user_interest_uid_IDX` (`uid`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci;

INSERT INTO `tree_parent` () value ();
SET @tree_parent_id = LAST_INSERT_ID();

INSERT INTO `tree`
(mutationId, rootId, extId, treeParentId, parentId, `level`, `path`, sort, `last`, uid, created, createdTime, createdTimeOrder, edited, editedTime, template, `type`, publicFrom, publicTo, name, nameAnchor, nameAnchorBreadcrumb, nameTitle, nameShort, nameHeading, description, keywords, public, score, forceNoIndex, hideInSearch, hideInSitemap, hideInMenu, annotation, content, hideFirstImage, links, seoTitleFilter, seoAnnotationFilter, seoDescriptionFilter, videos, customFieldsJson, customContentJson, productAttachedId, hasLinkedCategories)
VALUES(1, 1, NULL, @tree_parent_id, 72, 2, '1|72|', 20, 1, 'userInterest', 0, '2025-06-16 12:18:31.000', '2025-06-16 12:18:00.000', 34, '2025-06-16 12:18:48.000', ':Front:User:default', 'common', '2025-06-16 12:18:00.000', '2125-06-16 12:18:00.000', 'Mé zájmy', 'Mé zájmy', '', 'Mé zájmy', '', '', '', '', 1, 0.0, 0, 0, 0, 0, '', '', NULL, NULL, NULL, NULL, NULL, NULL, '{"base":[{"show_eu_bnr":true}]}', '{}', NULL, NULL);
SET @treeId = LAST_INSERT_ID();
INSERT INTO `alias` (alias, module, referenceId, mutationId) VALUES ( 'me-zajmy', 'tree',  @treeId, 1);

ALTER TABLE `user` ADD interestsJson TEXT NULL;
ALTER TABLE `user` ADD interestsUpdated DATETIME null;


