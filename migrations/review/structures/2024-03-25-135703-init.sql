-- Adminer 4.8.1 MySQL 10.6.12-MariaDB dump

SET NAMES utf8;
SET time_zone = '+00:00';
SET foreign_key_checks = 0;
SET sql_mode = 'NO_AUTO_VALUE_ON_ZERO';

DROP TABLE IF EXISTS `review`;
CREATE TABLE `review` (
						  `id` int(11) NOT NULL AUTO_INCREMENT,
						  `extId` int(11) DEFAULT NULL,
						  `name` varchar(255) NOT NULL,
						  `perex` text NOT NULL,
						  `url` text NOT NULL,
						  `date` date NOT NULL,
						  `updated` datetime NOT NULL,
						  `author` varchar(255) NOT NULL,
						  `libraryImageId` int(11) DEFAULT NULL,
						  PRIMARY KEY (`id`),
						  KEY `libraryImageId` (`libraryImageId`),
						  CONSTRAINT `review_ibfk_1` FOREIGN KEY (`libraryImageId`) REFERENCES `image` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_czech_ci;


DROP TABLE IF EXISTS `review_x_product`;
CREATE TABLE `review_x_product` (
									`id` int(11) NOT NULL AUTO_INCREMENT,
									`review` int(11) NOT NULL,
									`product` int(11) NOT NULL,
									PRIMARY KEY (`id`),
									KEY `review` (`review`),
									KEY `product` (`product`),
									CONSTRAINT `review_x_product_ibfk_1` FOREIGN KEY (`review`) REFERENCES `review` (`id`),
									CONSTRAINT `review_x_product_ibfk_2` FOREIGN KEY (`product`) REFERENCES `product` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_czech_ci;


-- 2024-03-27 18:02:24

ALTER TABLE `import_cache`
	CHANGE `type` `type` enum('product','stock','review') COLLATE 'utf8mb4_czech_ci' NOT NULL AFTER `id`;

INSERT INTO `library_tree` (`parentId`, `level`, `path`, `sort`, `last`, `created`, `createdTime`, `edited`, `editedTime`, `publicFrom`, `publicTo`, `name`, `nameTitle`, `nameAnchor`, `uid`) VALUES
	(	1,	1,	'1|',	1,	1,	1,	'2024-03-25 13:58:50',	0,	'0000-00-00 00:00:00',	'2024-03-25 13:58:50',	'2100-01-01 00:00:00',	'Recenze knih',	'Recenze knih',	'Recenze knih',	NULL);
