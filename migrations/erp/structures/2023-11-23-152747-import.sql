CREATE TABLE `import_cache`
(
	`id`           int(11)                                                                                 NOT NULL AUTO_INCREMENT,
	`type`         enum ('product') COLLATE utf8mb4_czech_ci                                               NOT NULL,
	`status`       enum ('new','ready','imported','error','processing','skipped') COLLATE utf8mb4_czech_ci NOT NULL DEFAULT 'new',
	`extId`        varchar(32) COLLATE utf8mb4_czech_ci                                                             DEFAULT NULL,
	`createdTime`  datetime                                                                                NOT NULL DEFAULT CURRENT_TIMESTAMP,
	`importedTime` datetime                                                                                         DEFAULT NULL,
	`message`      text COLLATE utf8mb4_czech_ci,
	`data`         mediumtext COLLATE utf8mb4_czech_ci                                                     NOT NULL,
	PRIMARY KEY (`id`),
	KEY `type_status_createdTime` (`type`, `status`, `createdTime`),
	KEY `createdTime` (`createdTime`),
	KEY `status` (`status`),
	KEY `type_status_extId` (`type`, `status`, `extId`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_czech_ci;
