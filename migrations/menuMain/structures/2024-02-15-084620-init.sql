create table if not exists `menu_main`
(
	id               int auto_increment,
	internalName     varchar(50) character set utf8 collate utf8_czech_ci not null default '',
	customFields<PERSON>son longtext                                             null,
	primary key (`id`) using btree
	) ENGINE = InnoDB
	AUTO_INCREMENT = 1
	DEFAULT CHARSET = utf8
	COLLATE = utf8_bin;

alter table menu_main
	add `order` int null after internalName;
