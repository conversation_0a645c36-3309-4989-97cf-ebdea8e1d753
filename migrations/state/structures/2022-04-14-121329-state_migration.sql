
CREATE TABLE IF NOT EXISTS `mutation_state` (
	`id` int(11) NOT NULL AUTO_INCREMENT,
	`mutationId` int(11) NOT NULL,
	`stateId` int(11) NOT NULL,
	PRIMARY KEY (`id`),
	UNIQUE KEY `mutationId_stateId` (`mutationId`,`stateId`),
	<PERSON><PERSON><PERSON> `stateId` (`stateId`),
	CONSTRAINT `mutation_state_ibfk_3` FOREIGN KEY (`mutationId`) REFERENCES `mutation` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
	CONSTRAINT `mutation_state_ibfk_4` FOREIGN KEY (`stateId`) REFERENCES `state` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
	) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8 COLLATE=utf8_czech_ci;
