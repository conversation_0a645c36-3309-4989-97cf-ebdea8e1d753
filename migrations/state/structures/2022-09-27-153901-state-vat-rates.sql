ALTER TABLE `state`
	DROP COLUMN `defaultVatRate`,
	ADD COLUMN `vatRates_standard` decimal(4, 2) DEFAULT NULL,
	ADD COLUMN `vatRates_reduced` decimal(4, 2) DEFAULT NULL,
	ADD COLUMN `vatRates_secondReduced` decimal(4, 2) DEFAULT NULL,
	ADD COLUMN `vatRates_superReduced` decimal(4, 2) DEFAULT NULL,
	ADD COLUMN `vatRates_parking` decimal(4, 2) DEFAULT NULL;

UPDATE `state` SET
	`vatRates_standard` = JSON_EXTRACT(`vatRates`, '$.default'),
	`vatRates_reduced` = JSON_EXTRACT(`vatRates`, '$.low'),
	`vatRates_secondReduced` = JSON_EXTRACT(`vatRates`, '$.lowExtra'),
	`vatRates_superReduced` = JSON_EXTRACT(`vatRates`, '$.lowUltra');

UPDATE `state` SET `vatRates_parking` = 13 WHERE `code` = 'AT';
ALTER TABLE `state` DROP COLUMN `vatRates`;
