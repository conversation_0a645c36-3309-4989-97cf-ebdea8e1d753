DROP TABLE IF EXISTS `system_message`;

CREATE TABLE `system_message`
(
	`id`               INT(11)     NOT NULL AUTO_INCREMENT,
	`internalName`     VARCHAR(50) NOT NULL DEFAULT '' COLLATE 'utf8_czech_ci',
	`customF<PERSON><PERSON><PERSON>son` LONGTEXT    NULL     DEFAULT NULL COLLATE 'utf8_bin',
	`description`      LONGTEXT    NULL     DEFAULT NULL COLLATE 'utf8_bin',
	`publicFrom`       datetime             default CURRENT_TIMESTAMP NULL,
	`publicTo`         datetime             default CURRENT_TIMESTAMP NULL,
	`isPublic`         tinyint(1)           default false,
	`isUpperPosition`  tinyint(1)           default false,
	`editedTime`       datetime             default CURRENT_TIMESTAMP NULL,
	`editedBy`         int                  default NULL,

	PRIMARY KEY (`id`) USING BTREE,
	KEY `FK_system_message_edited_by` (`editedBy`) USING BTREE,
	constraint info_system_message_edited_by_id_fk
		foreign key (editedBy) REFERENCES user (id)
) COLLATE = 'utf8_bin'
  ENGINE = InnoDB
  DEFAULT CHARSET = utf8;
