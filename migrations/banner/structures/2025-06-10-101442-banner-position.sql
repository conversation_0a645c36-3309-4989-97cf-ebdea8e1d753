CREATE TABLE `banner_position` (
   `id` INT(11) NOT NULL AUTO_INCREMENT,
   `name` VARCHAR(50) NOT NULL COLLATE 'utf8mb4_unicode_520_ci',
   PRIMARY KEY (`id`) USING BTREE,
   UNIQUE INDEX `namee` (`name`) USING BTREE
)
	COLLATE='utf8mb4_unicode_520_ci'
	ENGINE=InnoDB
;

CREATE TABLE `banner_x_banner_position` (
	`id` INT NOT NULL AUTO_INCREMENT,
	`bannerId` INT NOT NULL,
	`positionId` INT NOT NULL,
	PRIMARY KEY (`id`),
	CONSTRAINT `FK_banner_x_banner_position_banner` FOREIGN KEY (`bannerId`) REFERENCES `banner` (`id`) ON UPDATE CASCADE ON DELETE CASCADE,
	CONSTRAINT `FK_banner_x_banner_position_banner_position` FOREIGN KEY (`positionId`) REFERENCES `banner_position` (`id`) ON UPDATE CASCADE ON DELETE CASCADE,
	UNIQUE INDEX `bannerId_positionId` (`bannerId`, `positionId`)
)
	COLLATE='utf8mb4_unicode_520_ci'
;
