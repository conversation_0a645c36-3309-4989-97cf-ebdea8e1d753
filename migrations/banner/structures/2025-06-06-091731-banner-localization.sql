CREATE TABLE `banner_localization` (
	`id` INT(11) NOT NULL AUTO_INCREMENT,
	`bannerId` INT(11) NOT NULL,
	`mutationId` INT(11) NOT NULL,
	`name` VARCHAR(50) NOT NULL DEFAULT '' COLLATE 'utf8_czech_ci',
	`public` TINYINT(1) NULL DEFAULT NULL,
	`publicFrom` DATETIME NULL DEFAULT NULL,
	`publicTo` DATETIME NULL DEFAULT NULL,
	`edited` INT(11) NULL DEFAULT NULL,
	`editedTime` DATETIME NULL DEFAULT NULL,
	`customFields<PERSON>son` LONGTEXT NULL DEFAULT NULL COLLATE 'utf8_bin',
	`customContentJson` LONGTEXT NULL DEFAULT NULL COLLATE 'utf8_bin',

	PRIMARY KEY (`id`) USING BTREE,
	INDEX `FK_banner_mutation` (`mutationId`) USING BTREE,
	INDEX `FK_banner_localization_banner` (`bannerId`) USING BTREE,
	CONSTRAINT `FK_banner_localization_banner` FOREIGN KEY (`bannerId`) REFERENCES `banner` (`id`) ON UPDATE CASCADE ON DELETE CASCADE,
	CONSTRAINT `FK_banner_mutation` FOREIGN KEY (`mutationId`) REFERENCES `mutation` (`id`) ON UPDATE CASCADE ON DELETE CASCADE
)
	COLLATE='utf8_bin'
	ENGINE=InnoDB
	AUTO_INCREMENT=1
;
