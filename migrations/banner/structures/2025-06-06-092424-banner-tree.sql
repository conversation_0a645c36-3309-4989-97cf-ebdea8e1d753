CREATE TABLE `banner_localization_tree` (
	`id` INT(11) NOT NULL AUTO_INCREMENT,
	`bannerLocalizationId` INT(11) NOT NULL,
	`treeId` INT(11) NOT NULL,
	`sort` INT(11) NULL,

	PRIMARY KEY (`id`) USING BTREE,
	INDEX `FK_banner_tree_banner` (`bannerLocalizationId`) USING BTREE,
	INDEX `FK_banner_tree_tree` (`treeId`) USING BTREE,
	CONSTRAINT `FK_banner_tree_banner` FOREIGN KEY (`bannerLocalizationId`) REFERENCES `banner_localization` (`id`) ON UPDATE CASCADE ON DELETE CASCADE,
	CONSTRAINT `FK_banner_tree_tree` FOREIGN KEY (`treeId`) REFERENCES `tree` (`id`) ON UPDATE CASCADE ON DELETE CASCADE
)
	COLLATE='utf8_bin'
	ENGINE=InnoDB
;
