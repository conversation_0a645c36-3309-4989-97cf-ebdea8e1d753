create table if not exists `discount`
(
	id               int auto_increment,
	internalName     varchar(50) character set utf8 collate utf8_czech_ci not null default '',
	customFields<PERSON>son longtext                                             null,
	parameterValueDiscountId int                                                  null,
	primary key (`id`) using btree,
	constraint discount_parameter_value_id_fk
	foreign key (parameterValueDiscountId) references parameter_value (id)
	) ENGINE = InnoDB
	AUTO_INCREMENT = 1
	DEFAULT CHARSET = utf8
	COLLATE = utf8_bin;

alter table discount
	add `order` int null after internalName;
