CREATE TABLE IF NOT EXISTS `discount_product`
(
	id         int auto_increment,
	discountId int not null,
	productId  int not null,
	constraint discount_product_pk
		primary key (id),
	constraint discount_product_discount_id_fk
		foreign key (discountId) references discount (id),
	constraint discount_product_product_id_fk
		foreign key (productId) references product (id)
)
	ENGINE = InnoDB
	AUTO_INCREMENT = 1
	DEFAULT CHARSET = utf8
	COLLATE = utf8_bin;

