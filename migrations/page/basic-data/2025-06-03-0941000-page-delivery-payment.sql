SET NAMES utf8mb4;

INSERT INTO `tree_parent` () value ();
SET @tree_parent_id = LAST_INSERT_ID();

INSERT INTO `tree` (`mutationId`, `rootId`, `treeParentId`, `parentId`, `level`, `path`, `sort`, `last`, `uid`,
					`created`, `createdTime`, `createdTimeOrder`, `edited`, `editedTime`, `template`, `type`,
					`publicFrom`, `publicTo`, `name`, `nameAnchor`, `nameTitle`, `description`, `keywords`, `public`,
					`forceNoIndex`, `hideInSearch`, `hideInSitemap`, `annotation`, `content`, `hideFirstImage`, `links`,
					`seoTitleFilter`, `seoAnnotationFilter`, `seoDescriptionFilter`, `videos`, `customFieldsJson`,
					`customContentJson`, `productAttachedId`, `hasLinkedCategories`, `nameAnchorBreadcrumb`, `nameHeading`)

	VALUES (1, 1, @tree_parent_id, 43, 2, '1|43|', 10, 1, 'deliveryAndPayment', 0, '2025-06-03 09:41:00', '2025-06-03 09:41:00', null, null,
			':Front:Page:deliveryAndPayment', 'common', '2025-06-03 09:41:00', '2124-06-10 12:02:50', 'Doprava a platba',
			'Doprava a platba', 'Doprava a platba', '', '', 1, 0, 0, 0, '', '', NULL, NULL, NULL, NULL, NULL, NULL, '{}',
			'{}', NULL, NULL, '', '');

SET @delivery_and_payment_page_id = LAST_INSERT_ID();

INSERT INTO `alias` ( `alias`, `module`, `referenceId`, `mutationId`)
	VALUE ('doprava-a-platba',	'tree',	@delivery_and_payment_page_id,	1);
