INSERT INTO `tree`  (`id`, `rootId`, `parentId`, `level`, `path`, `sort`, `last`, `public`, `uid`, `created`, `createdTime`, `createdTimeOrder`, `edited`, `editedTime`, `template`, `type`, `publicFrom`, `publicTo`, `name`, `nameTitle`, `nameAnchor`, `annotation`, `description`, `keywords`, `content`, `hideFirstImage`, `links`, `seoTitleFilter`, `seoAnnotationFilter`, `seoDescriptionFilter`, `videos`, `hideCrossroad`, `hideInSitemap`, `forceNoIndex`, `hideInSearch`, `showContactForm`, `showOnHomepage`, `customFieldsJson`, `customContentJson`, `customContentSchemeJson`, `productAttachedId`, `hasLinkedCategories`) VALUES (1, 1, NULL, 0, NULL, 1, 0, 1, 'title', 0, '2017-01-27 14:53:52', '2017-01-27 14:53:52', 12, '2021-07-20 10:08:45', 'Homepage:default', 'common', '2013-10-07 23:44:31', '2100-01-01 00:00:00', 'CZ', 'Superadmin', 'Úvod', '', '', '', '', 1, '', '', '', '', '', 0, 0, 0, 0, 0, 0, '{}', NULL, NULL, NULL, NULL);
INSERT INTO `alias` (`id`, `alias`, `module`, `referenceId`, `mutationId`)
	VALUES (2112, '', 'tree', 1, 1);

INSERT INTO `tree`  (`id`, `rootId`, `parentId`, `level`, `path`, `sort`, `last`, `public`, `uid`, `created`, `createdTime`, `createdTimeOrder`, `edited`, `editedTime`, `template`, `type`, `publicFrom`, `publicTo`, `name`, `nameTitle`, `nameAnchor`, `annotation`, `description`, `keywords`, `content`, `hideFirstImage`, `links`, `seoTitleFilter`, `seoAnnotationFilter`, `seoDescriptionFilter`, `videos`, `hideCrossroad`, `hideInSitemap`, `forceNoIndex`, `hideInSearch`, `showContactForm`, `showOnHomepage`, `customFieldsJson`, `customContentJson`, `customContentSchemeJson`, `productAttachedId`, `hasLinkedCategories`) VALUES (8, 1, 1, 1, '1|', 5, 1, 1, 'contact', 40, '2017-01-27 14:53:52', '2017-01-27 14:53:52', 5, '2021-07-19 12:58:00', 'Page:contact', 'common', '2021-07-08 17:00:00', '2100-01-01 00:00:00', 'Kontakt', 'Kontaktujte nás', 'Kontakt', '', '', '', '', 1, '', '', '', '', 'https://www.youtube.com/watch?v=nUwTnJ8yFXY|Tacos', 0, 0, 0, 0, 0, 0, '{}', NULL, NULL, NULL, NULL);
INSERT INTO `alias` (`id`, `alias`, `module`, `referenceId`, `mutationId`)
	VALUES (231, 'kontakt', 'tree', 8, 1);

INSERT INTO `tree`  (`id`, `rootId`, `parentId`, `level`, `path`, `sort`, `last`, `public`, `uid`, `created`, `createdTime`, `createdTimeOrder`, `edited`, `editedTime`, `template`, `type`, `publicFrom`, `publicTo`, `name`, `nameTitle`, `nameAnchor`, `annotation`, `description`, `keywords`, `content`, `hideFirstImage`, `links`, `seoTitleFilter`, `seoAnnotationFilter`, `seoDescriptionFilter`, `videos`, `hideCrossroad`, `hideInSitemap`, `forceNoIndex`, `hideInSearch`, `showContactForm`, `showOnHomepage`, `customFieldsJson`, `customContentJson`, `customContentSchemeJson`, `productAttachedId`, `hasLinkedCategories`) VALUES (12, 1, 43, 2, '1|43|', 2, 1, 1, 'cookie', 5, '2017-01-24 14:19:44', '2017-01-24 14:19:44', 5, '2020-03-11 14:38:14', 'Page:default', 'common', '2017-01-24 14:19:44', '2100-01-01 00:00:00', 'Prohlášení o používání cookies', 'Prohlášení o používání cookies', 'Prohlášení o používání cookies', 'Prohlášení o používání cookies společností ...\r\n', '', '', '<h2 class="left">Co jsou cookies</h2>\r\n<p class="left">Cookies jsou krátké textové soubory vytvářené webovým serverem a ukládané ve Vašem počítači prostřednictvím prohlížeče. Když se později vrátíte na stejný web, prohlížeč pošle uloženou cookie zpět a server tak získá všechny informace, které si u vás předtím uložil. Cookies využívá pro svou činnost naprostá většina webových stránek.</p>\r\n<h2 class="left">Jak se dělí cookies</h2>\r\n<p class="left">Cookies lze rozdělit podle toho, kdo je k Vám na web umisťuje, tj. na:</p>\r\n<ul class="left">\r\n<li>Cookie první strany (first party cookie) – jejich platnost je omezena na doménu webu, který prohlížíte. Tyto cookies jsou považovány za bezpečnější.</li>\r\n<li>Cookie třetí strany (third party cookie) – jsou umístěny pomocí skriptu z jiné domény. Uživatele tak lze sledovat napříč doménami. Používají se často pro vyhodnocení účinnosti reklamních kanálů.</li>\r\n</ul>\r\n<p class="left">Podle trvanlivosti lze cookies rozdělit na:</p>\r\n<ul>\r\n<li class="left">Krátkodobé (session cookie) – vymažou se z vašeho počítače po zavření prohlížeče.</li>\r\n<li class="left">Dlouhodobé (persistent cookie) – po zavření prohlížeče zůstávají zachovány, vymažou se teprve po uplynutí velmi dlouhé doby (ta záleží na nastavení Vašeho prohlížeče a nastavení cookie). Můžete je také ručně odstranit.</li>\r\n</ul>\r\n<h2 class="left">K čemu cookies používáme</h2>\r\n<p class="left">Na našem webu používáme tyto cookies:</p>\r\n<ul class="left">\r\n<li>Technické – první strany, krátkodobé. Zajišťují základní technickou funkčnost webu, tj. přihlašování, využívání služeb apod.</li>\r\n<li>Google Analytics – první strany, dlouhodobé. Jsou využity ke generování anonymních statistik o používání webu.</li>\r\n<li>\r\n<p>Hotjar – první strany, krátkodobé i dlouhodobé. Pro analýzu návštěvnosti a zlepšení ovladatelnosti tohoto webu používáme nástroj Hotjar.</p>\r\n</li>\r\n</ul>\r\n<p class="left">Do cookies nikdy neumisťujeme citlivá nebo osobní data.</p>\r\n<h2 class="left">Jak lze upravit využívání cookies</h2>\r\n<h3 class="left">Vymazání</h3>\r\n<p class="left">Vymazat můžete cookies ve Vašem prohlížeči – zpravidla bývá umístěno v „Historii“ navštívených stránek.</p>\r\n<h3 class="left">Blokování</h3>\r\n<p class="left">Prohlížeče umožňují umísťování cookies na Vás počítač zablokovat. V takovém případě bude ale funkcionalita těchto stránek omezena. Informace o nastavení ukládání souborů cookies ve Vašem prohlížeči najdete na stránkách poskytovatele konkrétního prohlížeče:</p>\r\n<ul class="left">\r\n<li><a href="https://support.google.com/accounts/answer/61416?hl=cs">Chrome</a></li>\r\n<li><a href="https://support.mozilla.org/cs/kb/Práce%20s%20cookies">Firefox</a></li>\r\n<li><a href="http://support.microsoft.com/gp/cookies/cs">Internet Explorer</a></li>\r\n<li><a href="https://support.google.com/xoom/answer/169022?rd=1">Android</a></li>\r\n</ul>\r\n<p class="left">Další informace o cookies a jejich využití najdete na stránkách <a href="http://aboutcookies.org">AboutCookies.org</a></p>\r\n<h2 class="left">Tento web používá Google Analytics</h2>\r\n<p class="left">Tato stránka používá službu Google Analytics, poskytovanou společností Google, Inc. (dále jen "Google"). Služba Google Analytics používá souborů cookies. Informace o užívání stránky spolu s obsahem souboru <a href="#cookies">cookie</a> bude společností Google přenesen a uložen na serverech ve Spojených státech. Google bude užívat těchto informací pro účely vyhodnocování užívání stránky a vytváření zpráv o její aktivitě, určených pro její provozovatele, a pro poskytování dalších služeb týkajících se činností na stránce a užívání internetu vůbec. Google může také poskytnout tyto informace třetím osobám, bude-li to požadováno zákonem nebo budu-li takovéto třetí osoby zpracovávat tyto informace pro Google.</p>\r\n<p class="left">Služba Google Analytics je rozšířena o související reklamní funkce poskytované společností Google, a to:</p>\r\n<ul class="left">\r\n<li>přehledy zobrazení v reklamní síti Google,</li>\r\n<li>remarketing (zobrazování reklam v obsahové síti na základě zhlédnutých produktů),</li>\r\n<li>rozšířené demografické přehledy (reportování anonymních demografických dat).</li>\r\n</ul>\r\n<p class="left">Více informací o zpracování a využití dat najdete ve <a href="http://www.google.com/intl/cs/policies/privacy/partners/">smluvních podmínkách společnosti Google</a></p>\r\n<h2 class="left">Jak zakázat sledování Google Analytics</h2>\r\n<p class="left">Pokud nechcete poskytovat anonymní data o používání webu službě Google Analytics, můžete použít <a href="https://tools.google.com/dlpage/gaoptout">plugin poskytovaný společností Google</a>. Po nainstalování do Vašeho prohlížeče a aktivaci nebudou dále data odesílána.</p>\r\n', 0, '', '', '', '', '', 0, 0, 0, 0, 0, 0, '{}', NULL, NULL, NULL, NULL);
INSERT INTO `alias` (`id`, `alias`, `module`, `referenceId`, `mutationId`)
	VALUES (167, 'prohlaseni-o-pouzivani-cookies', 'tree', 12, 1);


INSERT INTO `tree`  (`id`, `rootId`, `parentId`, `level`, `path`, `sort`, `last`, `public`, `uid`, `created`, `createdTime`, `createdTimeOrder`, `edited`, `editedTime`, `template`, `type`, `publicFrom`, `publicTo`, `name`, `nameTitle`, `nameAnchor`, `annotation`, `description`, `keywords`, `content`, `hideFirstImage`, `links`, `seoTitleFilter`, `seoAnnotationFilter`, `seoDescriptionFilter`, `videos`, `hideCrossroad`, `hideInSitemap`, `forceNoIndex`, `hideInSearch`, `showContactForm`, `showOnHomepage`, `customFieldsJson`, `customContentJson`, `customContentSchemeJson`, `productAttachedId`, `hasLinkedCategories`) VALUES (40, 1, 43, 2, '1|43|', 1, 1, 1, 'conditions', 5, '2017-01-24 13:44:14', '2017-01-24 13:44:14', 4, '2019-01-14 10:40:50', 'Page:default', 'common', '2017-01-24 13:44:14', '2100-01-01 00:00:00', 'Obchodní podmínky', 'Obchodní podmínky', 'Obchodní podmínky', 'Anotace', '', '', '<p>Lorem ipsum dolor sit amet, consectetuer adipiscing elit. Nunc dapibus tortor vel mi dapibus sollicitudin. Integer malesuada. Nullam justo enim, consectetuer nec, ullamcorper ac, vestibulum in, elit. Fusce tellus. Nunc dapibus tortor vel mi dapibus sollicitudin. Nulla est. Lorem ipsum dolor sit amet, consectetuer adipiscing elit. Fusce tellus odio, dapibus id fermentum quis, suscipit id erat. Curabitur sagittis hendrerit ante. Sed convallis magna eu sem. Fusce suscipit libero eget elit. Nullam sit amet magna in magna gravida vehicula. Etiam neque. Nulla accumsan, elit sit amet varius semper, nulla mauris mollis quam, tempor suscipit diam nulla vel leo. Curabitur bibendum justo non orci. Phasellus faucibus molestie nisl.</p>\r\n<p>Maecenas sollicitudin. Curabitur sagittis hendrerit ante. Fusce dui leo, imperdiet in, aliquam sit amet, feugiat eu, orci. Praesent id justo in neque elementum ultrices. In laoreet, magna id viverra tincidunt, sem odio bibendum justo, vel imperdiet sapien wisi sed libero. Pellentesque habitant morbi tristique senectus et netus et malesuada fames ac turpis egestas. Ut tempus purus at lorem. Etiam commodo dui eget wisi. Aenean placerat. Nulla accumsan, elit sit amet varius semper, nulla mauris mollis quam, tempor suscipit diam nulla vel leo. Morbi imperdiet, mauris ac auctor dictum, nisl ligula egestas nulla, et sollicitudin sem purus in lacus. Fusce consectetuer risus a nunc. Nunc dapibus tortor vel mi dapibus sollicitudin. Nunc tincidunt ante vitae massa. Duis risus. Praesent in mauris eu tortor porttitor accumsan. Nulla non arcu lacinia neque faucibus fringilla. Praesent id justo in neque elementum ultrices. In laoreet, magna id viverra tincidunt, sem odio bibendum justo, vel imperdiet sapien wisi sed libero. Nunc dapibus tortor vel mi dapibus sollicitudin.</p>\r\n<p>Etiam bibendum elit eget erat. Nunc auctor. Nullam dapibus fermentum ipsum. Quis autem vel eum iure reprehenderit qui in ea voluptate velit esse quam nihil molestiae consequatur, vel illum qui dolorem eum fugiat quo voluptas nulla pariatur? Maecenas lorem. Fusce suscipit libero eget elit. In dapibus augue non sapien. Fusce wisi. Nunc tincidunt ante vitae massa. Mauris tincidunt sem sed arcu. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Aliquam ante. Nulla pulvinar eleifend sem. Duis sapien nunc, commodo et, interdum suscipit, sollicitudin et, dolor. Duis bibendum, lectus ut viverra rhoncus, dolor nunc faucibus libero, eget facilisis enim ipsum id lacus. Mauris tincidunt sem sed arcu. Curabitur vitae diam non enim vestibulum interdum. Morbi leo mi, nonummy eget tristique non, rhoncus non leo.</p>\r\n<p>Cras elementum. Nam libero tempore, cum soluta nobis est eligendi optio cumque nihil impedit quo minus id quod maxime placeat facere possimus, omnis voluptas assumenda est, omnis dolor repellendus. Aenean vel massa quis mauris vehicula lacinia. Etiam ligula pede, sagittis quis, interdum ultricies, scelerisque eu. Integer rutrum, orci vestibulum ullamcorper ultricies, lacus quam ultricies odio, vitae placerat pede sem sit amet enim. Nulla quis diam. Nulla est. Aliquam erat volutpat. Cum sociis natoque penatibus et magnis dis parturient montes, nascetur ridiculus mus. Nulla accumsan, elit sit amet varius semper, nulla mauris mollis quam, tempor suscipit diam nulla vel leo. Vivamus porttitor turpis ac leo. Etiam dui sem, fermentum vitae, sagittis id, malesuada in, quam. Fusce consectetuer risus a nunc. Nullam eget nisl. Mauris suscipit, ligula sit amet pharetra semper, nibh ante cursus purus, vel sagittis velit mauris vel metus. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.</p>\r\n<p>Maecenas ipsum velit, consectetuer eu lobortis ut, dictum at dui. Etiam egestas wisi a erat. Nulla turpis magna, cursus sit amet, suscipit a, interdum id, felis. Nunc auctor. Proin in tellus sit amet nibh dignissim sagittis. Integer vulputate sem a nibh rutrum consequat. In dapibus augue non sapien. Proin pede metus, vulputate nec, fermentum fringilla, vehicula vitae, justo. Nunc auctor. Etiam ligula pede, sagittis quis, interdum ultricies, scelerisque eu. Etiam dictum tincidunt diam. Fusce dui leo, imperdiet in, aliquam sit amet, feugiat eu, orci.</p>', 0, '', NULL, NULL, NULL, '', 0, 0, 0, 0, 0, 0, '{}', NULL, NULL, NULL, NULL);
INSERT INTO `alias` (`id`, `alias`, `module`, `referenceId`, `mutationId`)
	VALUES (128, 'obchodni-podminky', 'tree', 40, 1);

INSERT INTO `tree`  (`id`, `rootId`, `parentId`, `level`, `path`, `sort`, `last`, `public`, `uid`, `created`, `createdTime`, `createdTimeOrder`, `edited`, `editedTime`, `template`, `type`, `publicFrom`, `publicTo`, `name`, `nameTitle`, `nameAnchor`, `annotation`, `description`, `keywords`, `content`, `hideFirstImage`, `links`, `seoTitleFilter`, `seoAnnotationFilter`, `seoDescriptionFilter`, `videos`, `hideCrossroad`, `hideInSitemap`, `forceNoIndex`, `hideInSearch`, `showContactForm`, `showOnHomepage`, `customFieldsJson`, `customContentJson`, `customContentSchemeJson`, `productAttachedId`, `hasLinkedCategories`) VALUES (42, 1, 43, 2, '1|43|', 3, 1, 1, 'search', 1, '2015-07-29 09:42:10', '2015-07-29 09:42:10', 4, '2018-10-30 08:07:58', 'Search:default', 'common', '2015-07-29 09:42:10', '2100-01-01 00:00:00', 'Výsledky vyhledávání', 'Výsledky vyhledávání', 'Výsledky vyhledávání', '', '', '', '', 0, '', NULL, NULL, NULL, '', 0, 0, 0, 0, 0, 0, '{}', NULL, NULL, NULL, NULL);
INSERT INTO `alias` (`id`, `alias`, `module`, `referenceId`, `mutationId`)
	VALUES (75, 'hledat', 'tree', 42, 1);

INSERT INTO `tree`  (`id`, `rootId`, `parentId`, `level`, `path`, `sort`, `last`, `public`, `uid`, `created`, `createdTime`, `createdTimeOrder`, `edited`, `editedTime`, `template`, `type`, `publicFrom`, `publicTo`, `name`, `nameTitle`, `nameAnchor`, `annotation`, `description`, `keywords`, `content`, `hideFirstImage`, `links`, `seoTitleFilter`, `seoAnnotationFilter`, `seoDescriptionFilter`, `videos`, `hideCrossroad`, `hideInSitemap`, `forceNoIndex`, `hideInSearch`, `showContactForm`, `showOnHomepage`, `customFieldsJson`, `customContentJson`, `customContentSchemeJson`, `productAttachedId`, `hasLinkedCategories`) VALUES (43, 1, 1, 1, '1|', 7, 0, 0, '', 0, '2018-09-05 17:18:45', '2018-09-05 17:18:45', 2319, '2018-09-05 17:20:46', 'Page:default', 'common', '2018-09-05 17:18:45', '2118-09-05 17:18:45', 'Stránky mimo hlavní navigaci', 'Stránky mimo hlavní navigaci', 'Stránky mimo hlavní navigaci', '', '', '', '', 0, '', NULL, NULL, NULL, '', 0, 0, 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL);


INSERT INTO `tree`  (`id`, `rootId`, `parentId`, `level`, `path`, `sort`, `last`, `public`, `uid`, `created`, `createdTime`, `createdTimeOrder`, `edited`, `editedTime`, `template`, `type`, `publicFrom`, `publicTo`, `name`, `nameTitle`, `nameAnchor`, `annotation`, `description`, `keywords`, `content`, `hideFirstImage`, `links`, `seoTitleFilter`, `seoAnnotationFilter`, `seoDescriptionFilter`, `videos`, `hideCrossroad`, `hideInSitemap`, `forceNoIndex`, `hideInSearch`, `showContactForm`, `showOnHomepage`, `customFieldsJson`, `customContentJson`, `customContentSchemeJson`, `productAttachedId`, `hasLinkedCategories`) VALUES (102, 1, 1, 1, '1|', 3, 1, 1, 'aboutUs', 0, '2018-10-10 13:56:24', '2018-10-10 13:56:24', 28, '2022-04-08 16:03:02', 'Page:default', 'common', '2021-07-08 17:00:00', '2118-10-10 13:56:24', 'O nás', 'O nás', 'O nás', 'Suspendisse link vel nisi accumsan pretium. Etiam id massa ut neque iaculis auctor. Fermentum eu dictum risus consequat. Cras magna justo, iaculis non scelerisque eget, condimentum in tellus!', '', '', '<h2>Heading h2</h2>\r\n<p>Suspendisse <a href="#">link</a> vel nisi accumsan pretium. Etiam id massa ut neque iaculis auctor. Fermentum eu dictum risus consequat. Cras magna justo, iaculis non scelerisque eget, condimentum in tellus. Maecenas a sem quam. Suspendisse facilisis neque tempor enim imperdiet sodales. Vitae lobortis sapien vestibulum. Suspendisse vehicula libero vel nisi accumsan. Suspendisse vehicula libero vel nisi accumsan pretium. Etiam id massa ut neque iaculis auctor. Fermentum eu dictum risus consequat. Cras magna justo, iaculis non scelerisque eget, condimentum in tellus. Maecenas a sem quam. Suspendisse.</p>\r\n<p>Nam liber tempor cum soluta nobis eleifend option congue nihil imperdiet doming id quod mazim placerat facer possim assuiber tempor cum soluta nobis eleifend option congue nihil imperdiet doming id quod mazim placerat facer possim assum.</p>\r\n<figure><img src="/data/images-lg/20-superkoderi-oldrichhrb-1200px-01.jpg" alt="" />\r\n<figcaption>Image description</figcaption>\r\n</figure>\r\n<h3>Heading h3</h3>\r\n<ul>\r\n<li><strong>Etiam ante sem,</strong> porta a porttitor ut, varius varius metus.</li>\r\n<li><strong>Nunc eu felis</strong> quis metus volutpat pellentesque.</li>\r\n<li><strong>Duis gravida</strong> tincidunt enim sed cursus.</li>\r\n<li><strong>Nunc eu felis</strong> quis metus volutpat pellentesque.</li>\r\n<li><strong>Duis gravida</strong> tincidunt enim sed cursus.</li>\r\n</ul>\r\n<ol>\r\n<li><strong>Etiam ante sem,</strong> porta a porttitor ut, varius varius metus.</li>\r\n<li><strong>Nunc eu felis</strong> quis metus volutpat pellentesque.</li>\r\n<li><strong>Duis gravida</strong> tincidunt enim sed cursus.</li>\r\n<li><strong>Nunc eu felis</strong> quis metus volutpat pellentesque.</li>\r\n<li><strong>Duis gravida</strong> tincidunt enim sed cursus.</li>\r\n</ol>\r\n<p>Suspendisse <a href="#">link</a> vel nisi accumsan pretium. Etiam id massa ut neque iaculis auctor. Fermentum eu dictum risus consequat. Cras magna justo, iaculis non scelerisque eget, condimentum in tellus. Maecenas a sem quam. Suspendisse facilisis neque tempor enim imperdiet sodales. Vitae lobortis sapien vestibulum. Suspendisse vehicula libero vel nisi accumsan. Suspendisse vehicula libero vel nisi accumsan pretium. Etiam id massa ut neque iaculis auctor. Fermentum eu dictum risus consequat. Cras magna justo, iaculis non scelerisque eget, condimentum in tellus. Maecenas a sem quam. Suspendisse.</p>\r\n<p>Nam liber tempor cum soluta nobis eleifend option congue nihil imperdiet doming id quod mazim placerat facer possim assuiber tempor cum soluta nobis eleifend option congue nihil imperdiet doming id quod mazim placerat facer possim assum.</p>\r\n<blockquote>\r\n<p><strong>Blockquote</strong> – Lorem ipsum dolor sit amet, consectetur adipisicing elit. Dolorum quia ipsa corrupti temporibus ratione voluptatibus, voluptatem eos culpa a, numquam suscipit deleniti veniam libero. Dicta soluta sint, officiis enim voluptate.</p>\r\n</blockquote>\r\n<p>Mirum est notare quam littera gothica, quam nunc putamus parum claram, anteposuerit litterarum formas humanitatis per seacula quarta decima et quinta decima. Eodem modo typi, qui nunc nobis videntur parum clari, fiant sollemnes in futurum.<br />Duis autem vel eum iriure dolor in hendrerit in vulputate velit esse molestie consequat, vel illum dolore eu feugiat nulla facilisis at vero eros et accumsan et iusto odio dignissim qui blandit praesent luptatum zzril delenit augue duis dolore te feugait nulla facilisi. Nam <strong>liber tempor cum soluta nobis</strong> eleifend option congue nihil imperdiet doming id quod mazim placerat facer possim assum.</p>\r\n<h4>Heading 4 úrovně</h4>\r\n<ol>\r\n<li>Lorem ipsum dolor sit amet.\r\n<ol>\r\n<li>Lorem ipsum dolor sit amet, consectetur adipisicing elit. Facere consequuntur id quidem, expedita, dicta, eos temporibus incidunt, mollitia aspernatur vitae excepturi vel nam dolorem voluptates fuga assumenda reprehenderit? Aut, nesciunt.</li>\r\n<li>Lorem ipsum dolor sit amet, consectetur adipisicing elit. Tenetur deleniti aliquid, voluptatibus ratione! Id consequatur aperiam iusto nam in beatae atque a, voluptate iste fugit ea ex officia architecto impedit?</li>\r\n</ol>\r\n</li>\r\n<li>Lorem ipsum dolor sit amet, consectetur adipisicing elit, sed do eiusmod</li>\r\n<li>tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, uis nostrud exercitation ullamco laboris nisi ut iquip x ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse</li>\r\n<li>cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.</li>\r\n</ol>\r\n<ul>\r\n<li>Lorem ipsum dolor sit amet.\r\n<ul>\r\n<li>Lorem ipsum dolor sit amet, consectetur adipisicing elit. Facere consequuntur id quidem, expedita, dicta, eos temporibus incidunt, mollitia aspernatur vitae excepturi vel nam dolorem voluptates fuga assumenda reprehenderit? Aut, nesciunt.</li>\r\n<li>Lorem ipsum dolor sit amet, consectetur adipisicing elit. Tenetur deleniti aliquid, voluptatibus ratione! Id consequatur aperiam iusto nam in beatae atque a, voluptate iste fugit ea ex officia architecto impedit?</li>\r\n</ul>\r\n</li>\r\n<li>Lorem ipsum dolor sit amet, consectetur adipisicing elit, sed do eiusmod</li>\r\n<li>tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, uis nostrud exercitation ullamco laboris nisi ut iquip x ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse</li>\r\n<li>cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.</li>\r\n</ul>\r\n<hr />\r\n<p>Nam liber tempor cum soluta nobis eleifend option congue nihil imperdiet doming id quod mazim placerat facer possim assum.</p>\r\n<dl>\r\n<dt>Definition List Title</dt>\r\n<dd>This is a definition list division.</dd>\r\n<dt>Definition List Title</dt>\r\n<dd>This is a definition list division.</dd>\r\n<dt>Definition List Title</dt>\r\n<dd>This is a definition list division.</dd>\r\n</dl>\r\n<h5>Heading 5 úrovně</h5>\r\n<p>Lorem ipsum dolor sit amet, consectetur adipisicing elit. Vitae odio minima nostrum, blanditiis maxime ex reiciendis laborum voluptatum molestiae sequi optio, repudiandae sit explicabo. Obcaecati neque eligendi, totam debitis aperiam!</p>\r\n<h6>Heading 6 úrovně</h6>\r\n<p><cite>Duis autem vel eum iriure dolor in hendrerit in vulputate velit esse molestie consequat, vel illum dolore eu feugiat nulla facilisis at vero eros et accumsan et iusto odio dignissim qui blandit praesent luptatum zzril delenit augue duis dolore te feugait nulla facilisi. Nam liber tempor cum soluta nobis eleifend option congue nihil imperdiet doming id quod mazim placerat facer possim assum.</cite></p>\r\n<h2>Tabular data</h2>\r\n<table border="0"><caption>Table Caption</caption>\r\n<thead>\r\n<tr>\r\n<th>Table Heading 1</th>\r\n<th>Table Heading 2</th>\r\n<th>Table Heading 3</th>\r\n<th>Table Heading 4</th>\r\n<th>Table Heading 5</th>\r\n</tr>\r\n</thead>\r\n<tbody>\r\n<tr>\r\n<td>Table Cell 1</td>\r\n<td>Table Cell 2</td>\r\n<td>Table Cell 3</td>\r\n<td>Table Cell 4</td>\r\n<td>Table Cell 5</td>\r\n</tr>\r\n<tr>\r\n<td>Table Cell 1</td>\r\n<td>Table Cell 2</td>\r\n<td>Table Cell 3</td>\r\n<td>Table Cell 4</td>\r\n<td>Table Cell 5</td>\r\n</tr>\r\n<tr>\r\n<td>Table Cell 1</td>\r\n<td>Table Cell 2</td>\r\n<td>Table Cell 3</td>\r\n<td>Table Cell 4</td>\r\n<td>Table Cell 5</td>\r\n</tr>\r\n<tr>\r\n<td>Table Cell 1</td>\r\n<td>Table Cell 2</td>\r\n<td>Table Cell 3</td>\r\n<td>Table Cell 4</td>\r\n<td>Table Cell 5</td>\r\n</tr>\r\n</tbody>\r\n<tfoot>\r\n<tr>\r\n<th>Table Footer 1</th>\r\n<th>Table Footer 2</th>\r\n<th>Table Footer 3</th>\r\n<th>Table Footer 4</th>\r\n<th>Table Footer 5</th>\r\n</tr>\r\n</tfoot>\r\n</table>\r\n<h2>Headings</h2>\r\n<p>Heading 1</p>\r\n<h2>Heading 2</h2>\r\n<h3>Heading 3</h3>\r\n<h4>Heading 4</h4>\r\n<h5>Heading 5</h5>\r\n<h6>Heading 6</h6>\r\n<h2>Paragraphs</h2>\r\n<p>A paragraph (from the Greek paragraphos, “to write beside” or “written beside”) is a self-contained unit of a discourse in writing dealing with a particular point or idea. A paragraph consists of one or more sentences. Though not required by the syntax of any language, paragraphs are usually an expected part of formal writing, used to organize longer prose.</p>\r\n<h2>Blockquotes</h2>\r\n<blockquote>\r\n<p>A block quotation (also known as a long quotation or extract) is a quotation in a written document, that is set off from the main text as a paragraph, or block of text.</p>\r\n<p>It is typically distinguished visually using indentation and a different typeface or smaller size quotation. It may or may not include a citation, usually placed at the bottom.</p>\r\n<cite> <a href="#"> Said no one, ever. </a> </cite></blockquote>\r\n<h2>Lists</h2>\r\n<h3>Definition list</h3>\r\n<dl>\r\n<dt>Definition List Title</dt>\r\n<dd>This is a definition list division.</dd>\r\n</dl>\r\n<h3>Ordered List</h3>\r\n<ol>\r\n<li>List Item 1</li>\r\n<li>List Item 2</li>\r\n<li>List Item 3</li>\r\n</ol>\r\n<h3>Unordered List</h3>\r\n<ul>\r\n<li>List Item 1</li>\r\n<li>List Item 2</li>\r\n<li>List Item 3</li>\r\n</ul>\r\n<h2>Horizontal rules</h2>\r\n<hr />\r\n<h2>Tabular data</h2>\r\n<table border="0"><caption>Table Caption</caption>\r\n<thead>\r\n<tr>\r\n<th>Table Heading 1</th>\r\n<th>Table Heading 2</th>\r\n<th>Table Heading 3</th>\r\n<th>Table Heading 4</th>\r\n<th>Table Heading 5</th>\r\n</tr>\r\n</thead>\r\n<tbody>\r\n<tr>\r\n<td>Table Cell 1</td>\r\n<td>Table Cell 2</td>\r\n<td>Table Cell 3</td>\r\n<td>Table Cell 4</td>\r\n<td>Table Cell 5</td>\r\n</tr>\r\n<tr>\r\n<td>Table Cell 1</td>\r\n<td>Table Cell 2</td>\r\n<td>Table Cell 3</td>\r\n<td>Table Cell 4</td>\r\n<td>Table Cell 5</td>\r\n</tr>\r\n<tr>\r\n<td>Table Cell 1</td>\r\n<td>Table Cell 2</td>\r\n<td>Table Cell 3</td>\r\n<td>Table Cell 4</td>\r\n<td>Table Cell 5</td>\r\n</tr>\r\n<tr>\r\n<td>Table Cell 1</td>\r\n<td>Table Cell 2</td>\r\n<td>Table Cell 3</td>\r\n<td>Table Cell 4</td>\r\n<td>Table Cell 5</td>\r\n</tr>\r\n</tbody>\r\n<tfoot>\r\n<tr>\r\n<th>Table Footer 1</th>\r\n<th>Table Footer 2</th>\r\n<th>Table Footer 3</th>\r\n<th>Table Footer 4</th>\r\n<th>Table Footer 5</th>\r\n</tr>\r\n</tfoot>\r\n</table>\r\n<h2>Code</h2>\r\n<p><strong>Keyboard input:</strong> <kbd>Cmd</kbd></p>\r\n<p><strong>Inline code:</strong> <code>&lt;div&gt;code&lt;/div&gt;</code></p>\r\n<p><strong>Sample output:</strong> <samp>This is sample output from a computer program.</samp></p>\r\n<h2>Pre-formatted text</h2>\r\n<pre>P R E F O R M A T T E D T E X T\r\n! " # $ % &amp; \' ( ) * + , - . /\r\n0 1 2 3 4 5 6 7 8 9 : ; &lt; = &gt; ?\r\n@ A B C D E F G H I J K L M N O\r\nP Q R S T U V W X Y Z [ \\ ] ^ _\r\n` a b c d e f g h i j k l m n o\r\np q r s t u v w x y z { | } ~ </pre>\r\n<h2>Inline elements</h2>\r\n<p><a href="#">This is a text link</a>.</p>\r\n<p><strong>Strong is used to indicate strong importance.</strong></p>\r\n<p><em>This text has added emphasis.</em></p>\r\n<p><del>This text is deleted</del> and <ins>This text is inserted</ins>.</p>\r\n<p>This text has a strikethrough.</p>\r\n<p>Superscript<sup>®</sup>.</p>\r\n<p>Subscript for things like H<sub>2</sub>O.</p>\r\n<p>Abbreviation: <abbr title="HyperText Markup Language">HTML</abbr></p>\r\n<p><q cite="https://developer.mozilla.org/en-US/docs/HTML/Element/q">This text is a short inline quotation.</q></p>\r\n<p><cite>This is a citation.</cite></p>\r\n<p>The <dfn>dfn element</dfn> indicates a definition.</p>\r\n<p>The mark element indicates a highlight.</p>\r\n<p>The <var>variable element</var>, such as <var>x</var> = <var>y</var>.</p>\r\n<p>The time element: 2 weeks ago</p>\r\n<p><a class="btn"><span class="btn__text">Odkaz</span></a></p>\r\n<p><img src="/data/images-l/28-cameron-venti-pqyvyqqa87s-unsplash.jpeg" alt="cameron-venti-pqyvyqqa87s-unsplash" width="1024" height="682" /></p>', 0, '', '', '', '', 'https://www.youtube.com/watch?v=B8P2fewY4n4|Google mapy\nhttps://www.youtube.com/watch?v=o3_TEKZ-axw|When JavaScript bytes', 0, 0, 0, 0, 0, 0, '{"file":{"id":99,"name":"bydleni-mobil.jpg"},"file_multiple":[{"id":100,"name":"A4.pdf"},{"id":101,"name":"A4.pdf"}],"name":"Zuzana Sumlanska","image":"22","image_multiple":["23","55","57","40","74","53","47","40"],"suggest":20,"description":"tady něco píšu kemo","tinymce":"<h2>tady začíná test custom fieldu</h2>\\n<p>Suspendisse<span> </span><a href=\\"/o-nas\\">link</a><span> </span>vel nisi accumsan pretium. Etiam id massa ut neque iaculis auctor. Fermentum eu dictum risus consequat. Cras magna justo, iaculis non scelerisque eget, condimentum in tellus. Maecenas a sem quam. Suspendisse facilisis neque tempor enim imperdiet sodales. Vitae lobortis sapien vestibulum. Suspendisse vehicula libero vel nisi accumsan. Suspendisse vehicula libero vel nisi accumsan pretium. Etiam id massa ut neque iaculis auctor. Fermentum eu dictum risus consequat. Cras magna justo, iaculis non scelerisque eget, condimentum in tellus. Maecenas a sem quam. Suspendisse.</p>\\n<p>Nam liber tempor cum soluta nobis eleifend option congue nihil imperdiet doming id quod mazim placerat facer possim assuiber tempor cum soluta nobis eleifend option congue nihil imperdiet doming id quod mazim placerat facer possim assum.</p>\\n<h2>tady končí test custom fieldu</h2>","checkbox":true,"select":"first","select_multiple":"first,second","list":[{"demopage":37,"firstName":"ahoj","lastName":"hellou"},{"demopage":31,"firstName":"luděk","lastName":"ahoj"}]}', NULL, NULL, NULL, NULL);
INSERT INTO `alias` (`id`, `alias`, `module`, `referenceId`, `mutationId`)
	VALUES (454, 'o-nas', 'tree', 102, 1);


INSERT INTO `tree`  (`id`, `rootId`, `parentId`, `level`, `path`, `sort`, `last`, `public`, `uid`, `created`, `createdTime`, `createdTimeOrder`, `edited`, `editedTime`, `template`, `type`, `publicFrom`, `publicTo`, `name`, `nameTitle`, `nameAnchor`, `annotation`, `description`, `keywords`, `content`, `hideFirstImage`, `links`, `seoTitleFilter`, `seoAnnotationFilter`, `seoDescriptionFilter`, `videos`, `hideCrossroad`, `hideInSitemap`, `forceNoIndex`, `hideInSearch`, `showContactForm`, `showOnHomepage`, `customFieldsJson`, `customContentJson`, `customContentSchemeJson`, `productAttachedId`, `hasLinkedCategories`) VALUES (255, 1, 43, 2, '1|43|', 4, 1, 1, 'personalData', 0, '2018-12-18 10:07:15', '2018-12-18 10:07:15', 4, '2019-03-05 09:09:48', 'Page:default', 'common', '2018-12-18 10:07:14', '2118-12-18 10:07:14', 'Zpracováním osobních údajů', 'Zpracováním osobních údajů', 'Zpracováním osobních údajů', 'aaaa', '', '', '<p>ddddddd</p>', 0, '', '', '', '', '', 0, 0, 0, 0, 0, 0, '{}', NULL, NULL, NULL, NULL);
INSERT INTO `alias` (`id`, `alias`, `module`, `referenceId`, `mutationId`)
	VALUES (604, 'zpracovanim-osobnich-udaju', 'tree', 255, 1);

INSERT INTO `tree`  (`id`, `rootId`, `parentId`, `level`, `path`, `sort`, `last`, `public`, `uid`, `created`, `createdTime`, `createdTimeOrder`, `edited`, `editedTime`, `template`, `type`, `publicFrom`, `publicTo`, `name`, `nameTitle`, `nameAnchor`, `annotation`, `description`, `keywords`, `content`, `hideFirstImage`, `links`, `seoTitleFilter`, `seoAnnotationFilter`, `seoDescriptionFilter`, `videos`, `hideCrossroad`, `hideInSitemap`, `forceNoIndex`, `hideInSearch`, `showContactForm`, `showOnHomepage`, `customFieldsJson`, `customContentJson`, `customContentSchemeJson`, `productAttachedId`, `hasLinkedCategories`) VALUES (262, 1, 43, 2, '1|43|', 5, 1, 1, '404', 0, '2019-01-10 13:50:59', '2019-01-10 13:50:59', 4, '2019-01-10 14:04:18', 'Page:default', 'common', '2019-01-10 13:50:59', '2119-01-10 13:50:59', 'Stránka nenalezena', 'Stránka nenalezena', 'Stránka nenalezena', '', '', '', '', 0, '', NULL, NULL, NULL, '', 0, 0, 0, 0, 0, 0, '{}', NULL, NULL, NULL, NULL);
INSERT INTO `alias` (`id`, `alias`, `module`, `referenceId`, `mutationId`)
	VALUES (615, 'stranka-nenalezena', 'tree', 262, 1);



INSERT INTO `tree`  (`id`, `rootId`, `parentId`, `level`, `path`, `sort`, `last`, `public`, `uid`, `created`, `createdTime`, `createdTimeOrder`, `edited`, `editedTime`, `template`, `type`, `publicFrom`, `publicTo`, `name`, `nameTitle`, `nameAnchor`, `annotation`, `description`, `keywords`, `content`, `hideFirstImage`, `links`, `seoTitleFilter`, `seoAnnotationFilter`, `seoDescriptionFilter`, `videos`, `hideCrossroad`, `hideInSitemap`, `forceNoIndex`, `hideInSearch`, `showContactForm`, `showOnHomepage`, `customFieldsJson`, `customContentJson`, `customContentSchemeJson`, `productAttachedId`, `hasLinkedCategories`) VALUES (394, 1, 398, 2, '1|398|', 1, 1, 1, 'popupContact', 0, '2019-03-21 13:35:53', '2019-03-21 13:35:52', 3, '2021-07-08 12:11:49', 'Internal:default', 'common', '2019-03-21 13:35:52', '2119-03-21 13:35:52', 'Popup', 'Popup', 'Popup', '', '', '', '', 0, '', '', '', '', '', 0, 1, 0, 1, 0, 0, '{}', NULL, NULL, NULL, NULL);
INSERT INTO `alias` (`id`, `alias`, `module`, `referenceId`, `mutationId`)
	VALUES (2107, 'popupcontact', 'tree', 394, 1);

INSERT INTO `tree`  (`id`, `rootId`, `parentId`, `level`, `path`, `sort`, `last`, `public`, `uid`, `created`, `createdTime`, `createdTimeOrder`, `edited`, `editedTime`, `template`, `type`, `publicFrom`, `publicTo`, `name`, `nameTitle`, `nameAnchor`, `annotation`, `description`, `keywords`, `content`, `hideFirstImage`, `links`, `seoTitleFilter`, `seoAnnotationFilter`, `seoDescriptionFilter`, `videos`, `hideCrossroad`, `hideInSitemap`, `forceNoIndex`, `hideInSearch`, `showContactForm`, `showOnHomepage`, `customFieldsJson`, `customContentJson`, `customContentSchemeJson`, `productAttachedId`, `hasLinkedCategories`) VALUES (398, 1, 1, 1, '1|', 9, 0, 0, 'systemPageId', 0, '2020-09-25 15:13:45', '2020-09-25 15:13:44', 3, '2021-07-08 12:12:18', 'Homepage:default', 'common', '2020-09-25 15:13:44', '2120-09-25 15:13:44', 'System pages (only develpers)', 'System pages (only develpers)', 'System pages (only develpers)', '', '', '', '', 0, '', '', '', '', '', 0, 1, 0, 1, 0, 0, '{}', NULL, NULL, NULL, NULL);
INSERT INTO `alias` (`id`, `alias`, `module`, `referenceId`, `mutationId`)
	VALUES (2139, 'system-pages-only-develpers', 'tree', 398, 1);


INSERT INTO `tree`  (`id`, `rootId`, `parentId`, `level`, `path`, `sort`, `last`, `public`, `uid`, `created`, `createdTime`, `createdTimeOrder`, `edited`, `editedTime`, `template`, `type`, `publicFrom`, `publicTo`, `name`, `nameTitle`, `nameAnchor`, `annotation`, `description`, `keywords`, `content`, `hideFirstImage`, `links`, `seoTitleFilter`, `seoAnnotationFilter`, `seoDescriptionFilter`, `videos`, `hideCrossroad`, `hideInSitemap`, `forceNoIndex`, `hideInSearch`, `showContactForm`, `showOnHomepage`, `customFieldsJson`, `customContentJson`, `customContentSchemeJson`, `productAttachedId`, `hasLinkedCategories`) VALUES (445, 1, 398, 2, '1|398|', 1, 1, 1, '', 0, '2021-07-14 17:23:51', '2021-07-14 17:23:51', 1, '2021-07-14 17:24:03', 'Page:styleguide', 'common', '2021-07-14 17:23:51', '2121-07-14 17:23:51', 'Styleguide', 'Styleguide', 'Styleguide', '', '', '', '', 0, '', NULL, NULL, NULL, '', 0, 0, 0, 0, 0, 0, '{}', NULL, NULL, NULL, NULL);
INSERT INTO `alias` (`id`, `alias`, `module`, `referenceId`, `mutationId`)
	VALUES (2197, 'styleguide', 'tree', 445, 1);



