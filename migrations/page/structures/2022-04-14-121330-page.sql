CREATE TABLE IF NOT EXISTS `tree` (
	`id` int(11) NOT NULL AUTO_INCREMENT,
	`rootId` int(11) NOT NULL,
	`parentId` int(11) DEFAULT NULL,
	`level` tinyint(4) DEFAULT NULL,
	`path` varchar(255) DEFAULT NULL,
	`sort` int(11) DEFAULT NULL,
	`last` tinyint(1) DEFAULT NULL,
	`public` tinyint(1) DEFAULT NULL,
	`uid` varchar(200) NOT NULL,
	`created` int(11) NOT NULL,
	`createdTime` datetime NOT NULL,
	`createdTimeOrder` datetime DEFAULT NULL,
	`edited` int(11) DEFAULT NULL,
	`editedTime` datetime DEFAULT NULL,
	`template` varchar(60) NOT NULL,
	`type` varchar(60) NOT NULL,
	`publicFrom` datetime DEFAULT NULL,
	`publicTo` datetime DEFAULT NULL,
	`name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_czech_ci DEFAULT NULL,
	`nameTitle` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_czech_ci DEFAULT NULL,
	`nameAnchor` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_czech_ci DEFAULT NULL,
	`annotation` text CHARACTER SET utf8mb4 COLLATE utf8mb4_czech_ci,
	`description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_czech_ci,
	`keywords` text CHARACTER SET utf8mb4 COLLATE utf8mb4_czech_ci,
	`content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_czech_ci,
	`hideFirstImage` tinyint(1) DEFAULT NULL,
	`links` text CHARACTER SET utf8mb4 COLLATE utf8mb4_czech_ci,
	`seoTitleFilter` text CHARACTER SET utf8mb4 COLLATE utf8mb4_czech_ci,
	`seoAnnotationFilter` text CHARACTER SET utf8mb4 COLLATE utf8mb4_czech_ci,
	`seoDescriptionFilter` text CHARACTER SET utf8mb4 COLLATE utf8mb4_czech_ci,
	`videos` text CHARACTER SET utf8mb4 COLLATE utf8mb4_czech_ci,
	`hideCrossroad` tinyint(4) NOT NULL,
	`hideInSitemap` tinyint(4) DEFAULT '0',
	`forceNoIndex` tinyint(4) DEFAULT '0',
	`hideInSearch` tinyint(4) DEFAULT '0',
	`showContactForm` tinyint(4) DEFAULT NULL,
	`showOnHomepage` tinyint(4) DEFAULT NULL,
	`customFieldsJson` text CHARACTER SET utf8mb4 COLLATE utf8mb4_czech_ci,
	`customContentJson` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_czech_ci,
	`customContentSchemeJson` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_czech_ci,
	`productAttachedId` int(11) DEFAULT NULL COMMENT 'produkt voucher napojený na skoleni ',
	`hasLinkedCategories` int(11) DEFAULT NULL,
	PRIMARY KEY (`id`),
	KEY `idParent` (`parentId`),
	KEY `uid` (`uid`),
	KEY `public` (`public`),
	CONSTRAINT `tree_ibfk_1` FOREIGN KEY (`parentId`) REFERENCES `tree` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
	) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;
