
CREATE TABLE IF NOT EXISTS `tree_tree` (
	`id` int(11) NOT NULL AUTO_INCREMENT,
	`mainTreeId` int(11) NOT NULL,
	`attachedTreeId` int(11) NOT NULL,
	`type` varchar(250) COLLATE utf8_czech_ci DEFAULT NULL,
	`sort` int(11) NOT NULL,
	PRIMARY KEY (`id`),
	UNIQUE KEY `mainTreeId_attachedTreeId_type` (`mainTreeId`,`attachedTreeId`,`type`),
	KEY `idtree` (`mainTreeId`),
	KEY `id_attached_tree` (`attachedTreeId`),
	CONSTRAINT `tree_tree_ibfk_3` FOREIGN KEY (`mainTreeId`) REFERENCES `tree` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
	CONSTRAINT `tree_tree_ibfk_4` FOREIGN KEY (`attachedTreeId`) REFERENCES `tree` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
	) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 COLLATE=utf8_czech_ci;
