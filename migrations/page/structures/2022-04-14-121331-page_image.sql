
CREATE TABLE IF NOT EXISTS `tree_image` (
	`id` int(11) NOT NULL AUTO_INCREMENT,
	`treeId` int(11) NOT NULL,
	`imageId` int(11) NOT NULL COMMENT 'idimage',
	`name` varchar(250) DEFAULT NULL,
	`url` varchar(250) DEFAULT NULL,
	`sort` int(11) DEFAULT NULL,
	`type` varchar(200) NOT NULL,
	PRIMARY KEY (`id`),
	UNIQUE KEY `imageId_treeId` (`imageId`,`treeId`),
	KEY `idtree_idx` (`treeId`),
	KEY `idfile_idx` (`imageId`),
	CONSTRAINT `tree_image_ibfk_1` FOREIGN KEY (`treeId`) REFERENCES `tree` (`id`) ON DELETE CASCADE,
	CONSTRAINT `tree_image_ibfk_2` FOREIGN KEY (`imageId`) REFERENCES `image` (`id`) ON DELETE CASCADE
	) ENGINE=InnoDB AUTO_INCREMENT=27 DEFAULT CHARSET=utf8;
