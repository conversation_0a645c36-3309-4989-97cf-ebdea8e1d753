
CREATE TABLE IF NOT EXISTS `tree_parameter` (
	`id` int(11) NOT NULL AUTO_INCREMENT,
	`treeId` int(11) NOT NULL,
	`parameterId` int(11) NOT NULL,
	`parameterValueId` int(11) NOT NULL,
	PRIMARY KEY (`id`),
	UNIQUE KEY `treeId_parameterId_parameterValueId` (`treeId`,`parameterId`,`parameterValueId`),
	<PERSON><PERSON><PERSON> `parameterId` (`parameterId`),
	KEY `parameterValueId` (`parameterValueId`),
	CONSTRAINT `tree_parameter_ibfk_3` FOREIGN KEY (`treeId`) REFERENCES `tree` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
	CONSTRAINT `tree_parameter_ibfk_4` FOREIGN KEY (`parameterId`) REFERENCES `parameter` (`id`) ON UPDATE CASCADE,
	CONSTRAINT `tree_parameter_ibfk_5` FOREIGN KEY (`parameterValueId`) REFERENCES `parameter_value` (`id`) ON UPDATE CASCADE
	) ENGINE=InnoDB DEFAULT CHARSET=utf8;
