
CREATE TABLE IF NOT EXISTS `tree_file` (
	`id` int(11) NOT NULL AUTO_INCREMENT,
	`fileId` int(11) NOT NULL COMMENT 'idfile',
	`treeId` int(11) NOT NULL,
	`name` varchar(250) DEFAULT NULL,
	`url` varchar(250) DEFAULT NULL,
	`sort` mediumint(9) DEFAULT NULL,
	`size` int(11) DEFAULT NULL,
	PRIMARY KEY (`id`),
	UNIQUE KEY `fileId_treeId` (`fileId`,`treeId`),
	KEY `idtree_idx` (`treeId`),
	KEY `idfile_idx` (`fileId`),
	CONSTRAINT `tree_file_ibfk_1` FOREIGN KEY (`fileId`) REFERENCES `file` (`id`) ON DELETE CASCADE,
	CONSTRAINT `tree_file_ibfk_2` FOREIGN KEY (`treeId`) REFERENCES `tree` (`id`) ON DELETE CASCADE
	) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8;
