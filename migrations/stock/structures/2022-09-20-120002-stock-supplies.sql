CREATE TABLE IF NOT EXISTS `stock_supplies` (
	`id` int(11) NOT NULL AUTO_INCREMENT,
	`stockId` int(11) NOT NULL,
	`variantId` int(11) DEFAULT NULL,
	`amount` int(11) NOT NULL,
	`lastImport` datetime DEFAULT NULL,
	PRIMARY KEY (`id`),
	UNIQUE KEY `stockId_variantId` (`stockId`,`variantId`),
	<PERSON><PERSON>Y `productVariantId` (`variantId`),
	KEY `stockId` (`stockId`),
	CONSTRAINT `stock_supplies_ibfk_3` FOREIGN KEY (`stockId`) REFERENCES `stock` (`id`) ON DELETE CASCADE,
	CONSTRAINT `stock_supplies_ibfk_4` FOREIGN KEY (`variantId`) REFERENCES `product_variant` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
