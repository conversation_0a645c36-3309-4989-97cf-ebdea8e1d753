CREATE TABLE IF NOT EXISTS `stock` (
	`id` int(11) NOT NULL AUTO_INCREMENT,
	`name` varchar(250) COLLATE utf8_unicode_ci DEFAULT NULL,
	`alias` varchar(250) COLLATE utf8_unicode_ci DEFAULT NULL,
	`address` varchar(250) COLLATE utf8_unicode_ci DEFAULT NULL,
	`order` int(11) DEFAULT NULL,
	`deliveryHour` varchar(5) COLLATE utf8_unicode_ci NOT NULL DEFAULT '11:00',
	`extId` varchar(32) COLLATE utf8_unicode_ci DEFAULT NULL,
	PRIMARY KEY (`id`),
	UNIQUE KEY `extId` (`extId`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
