CREATE TABLE IF NOT EXISTS `email_template` (
	`id` int(11) NOT NULL AUTO_INCREMENT,
	`mutationId` int(11) NOT NULL DEFAULT '1',
	`key` varchar(255) NOT NULL,
	`isDeveloper` tinyint(1) NOT NULL DEFAULT '0',
	`isHidden` tinyint(1) NOT NULL DEFAULT '0',
	`name` varchar(100) DEFAULT NULL,
	`subject` varchar(255) DEFAULT NULL,
	`body` text,
	PRIMARY KEY (`id`),
	KEY `mutationId` (`mutationId`),
	CONSTRAINT `email_template_ibfk_1` FOREIGN KEY (`mutationId`) REFERENCES `mutation` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
	) ENGINE=InnoDB AUTO_INCREMENT=63 DEFAULT CHARSET=utf8;
