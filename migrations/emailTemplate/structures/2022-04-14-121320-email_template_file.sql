CREATE TABLE IF NOT EXISTS `email_template_file` (
	`id` int(11) NOT NULL AUTO_INCREMENT,
	`fileId` int(11) NOT NULL COMMENT 'idfile',
	`emailTemplateId` int(11) NOT NULL,
	`name` varchar(250) DEFAULT NULL,
	`url` varchar(250) DEFAULT NULL,
	`sort` mediumint(9) DEFAULT NULL,
	`size` int(11) DEFAULT NULL,
	PRIMARY KEY (`id`),
	UNIQUE KEY `fileId_emailTemplateId` (`fileId`,`emailTemplateId`),
	<PERSON><PERSON>Y `emailTemplateId` (`emailTemplateId`),
	<PERSON><PERSON><PERSON> `fileId` (`fileId`),
	CONSTRAINT `email_template_file_ibfk_1` FOREIGN KEY (`fileId`) REFERENCES `file` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
	CONSTRAINT `email_template_file_ibfk_2` FOREI<PERSON>N <PERSON>EY (`emailTemplateId`) REFERENCES `email_template` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
	) ENGINE=InnoDB DEFAULT CHARSET=utf8;
