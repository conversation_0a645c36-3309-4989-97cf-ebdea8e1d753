ALTER TABLE `publisher`
	CHANGE `internalName` `internalName` varchar(255) COLLATE 'utf8mb3_czech_ci' NOT NULL DEFAULT '' AFTER `id`;
ALTER TABLE `theme`
	CHANGE `internalName` `internalName` varchar(255) COLLATE 'utf8mb3_czech_ci' NOT NULL DEFAULT '' AFTER `parameterValueTheme`;
ALTER TABLE `writer`
	CHANGE `internalName` `internalName` varchar(255) COLLATE 'utf8mb3_czech_ci' NOT NULL DEFAULT '' AFTER `id`;
