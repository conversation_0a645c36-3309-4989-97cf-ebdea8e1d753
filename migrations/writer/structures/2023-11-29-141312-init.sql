create table if not exists `writer`
(
	id               int auto_increment,
	internalName     varchar(50) character set utf8 collate utf8_czech_ci not null default '',
	customFields<PERSON>son longtext                                             null,
	parameterValueId int                                                  null,
	primary key (`id`) using btree,
	constraint writer_parameter_value_id_fk
		foreign key (parameterValueId) references parameter_value (id)
) ENGINE = InnoDB
  AUTO_INCREMENT = 1
  DEFAULT CHARSET = utf8
  COLLATE = utf8_bin;
