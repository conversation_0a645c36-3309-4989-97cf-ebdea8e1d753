CREATE TABLE IF NOT EXISTS `writer_localization`
(
	`id`                int(11) NOT NULL AUTO_INCREMENT,
	`mutationId`        int(11) NOT NULL,
	`writerId`          int(11) NOT NULL,
	`name`              varchar(250) CHARACTER SET utf8 COLLATE utf8_czech_ci DEFAULT '',
	`nameAnchor`        varchar(250) COLLATE utf8_bin                         DEFAULT '',
	`nameTitle`         varchar(250) COLLATE utf8_bin                         DEFAULT '',
	`description`       text COLLATE utf8_bin,
	`keywords`          text COLLATE utf8_bin,
	`title`             varchar(250) COLLATE utf8_bin                         DEFAULT '',
	`public`            tinyint(1)                                            DEFAULT 0,
	`isCollective`      tinyint(1)                                            DEFAULT 0,
	`publicFrom`        datetime                                              DEFAULT NULL,
	`publicTo`          datetime                                              DEFAULT NULL,
	`forceNoIndex`      int(11)                                               DEFAULT 0,
	`hideInSearch`      int(11)                                               DEFAULT 0,
	`hideInSitemap`     int(11)                                               DEFAULT 0,
	`edited`            int(11)                                               DEFAULT NULL,
	`editedTime`        datetime                                              DEFAULT NULL,
	`customFieldsJson`  longtext COLLATE utf8_bin,
	`customContentJson` longtext COLLATE utf8_bin,
	PRIMARY KEY (`id`) USING BTREE,
	UNIQUE KEY `mutationId_writerId` (`mutationId`, `writerId`),
	KEY `FK_writer_mutation` (`mutationId`) USING BTREE,
	KEY `FK_writer_localization_writer` (`writerId`) USING BTREE,
	CONSTRAINT `FK_writer_localization_writer` FOREIGN KEY (`writerId`) REFERENCES `writer` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
	CONSTRAINT `FK_writer_localization_mutation` FOREIGN KEY (`mutationId`) REFERENCES `mutation` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB
  AUTO_INCREMENT = 1
  DEFAULT CHARSET = utf8
  COLLATE = utf8_bin;
