CREATE TABLE IF NOT EXISTS `author_localization` (
	`id` int(11) NOT NULL AUTO_INCREMENT,
	`mutationId` int(11) NOT NULL,
	`authorId` int(11) NOT NULL DEFAULT '1',
	`name` varchar(250) CHARACTER SET utf8 COLLATE utf8_czech_ci DEFAULT NULL,
	`nameAnchor` varchar(250) COLLATE utf8_bin DEFAULT NULL,
	`nameTitle` varchar(250) COLLATE utf8_bin DEFAULT NULL,
	`description` text COLLATE utf8_bin,
	`keywords` text COLLATE utf8_bin,
	`title` varchar(250) COLLATE utf8_bin DEFAULT NULL,
	`public` int(11) DEFAULT NULL,
	`publicFrom` datetime DEFAULT NULL,
	`publicTo` datetime DEFAULT NULL,
	`forceNoIndex` int(11) DEFAULT '0',
	`hideInSearch` int(11) DEFAULT '0',
	`hideInSitemap` int(11) DEFAULT '0',
	`edited` int(11) DEFAULT NULL,
	`editedTime` datetime DEFAULT NULL,
	`customFieldsJson` longtext COLLATE utf8_bin,
	`customContentJson` longtext COLLATE utf8_bin,
	PRIMARY KEY (`id`) USING BTREE,
	UNIQUE KEY `mutationId_authorId` (`mutationId`,`authorId`),
	KEY `FK_author_mutation` (`mutationId`) USING BTREE,
	KEY `FK_author_localization_author` (`authorId`) USING BTREE,
	CONSTRAINT `FK_author_localization_author` FOREIGN KEY (`authorId`) REFERENCES `author` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
	CONSTRAINT `FK_author_localization_mutation` FOREIGN KEY (`mutationId`) REFERENCES `mutation` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
	) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8 COLLATE=utf8_bin;
