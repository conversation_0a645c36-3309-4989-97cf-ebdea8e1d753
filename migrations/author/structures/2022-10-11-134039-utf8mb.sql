ALTER TABLE `author`
	<PERSON><PERSON><PERSON> COLUMN `internalName` `internalName` VARCHAR(50) NOT NULL DEFAULT '' COLLATE 'utf8mb4_unicode_520_ci' AFTER `id`,
	<PERSON><PERSON><PERSON> COLUMN `customFieldsJson` `customFields<PERSON>son` LONGTEXT NULL DEFAULT NULL COLLATE 'utf8mb4_unicode_520_ci' AFTER `internalName`;


ALTER TABLE `author_localization`
	<PERSON>AN<PERSON> COLUMN `name` `name` VARCHAR(250) NOT NULL DEFAULT '' COLLATE 'utf8mb4_unicode_520_ci' AFTER `authorId`,
	<PERSON>AN<PERSON> COLUMN `nameAnchor` `nameAnchor` VARCHAR(250) NOT NULL DEFAULT '' COLLATE 'utf8mb4_unicode_520_ci' AFTER `name`,
	CHANGE COLUMN `nameTitle` `nameTitle` VARCHAR(250) NOT NULL DEFAULT '' COLLATE 'utf8mb4_unicode_520_ci' AFTER `nameAnchor`,
	<PERSON>AN<PERSON> COLUMN `description` `description` TEXT NOT NULL COLLATE 'utf8mb4_unicode_520_ci' AFTER `nameTitle`,
	CHANGE COLUMN `keywords` `keywords` TEXT NOT NULL COLLATE 'utf8mb4_unicode_520_ci' AFTER `description`,
	CHANGE COLUMN `customFieldsJson` `customFieldsJson` LONGTEXT NULL DEFAULT NULL COLLATE 'utf8mb4_unicode_520_ci' AFTER `editedTime`,
	CHANGE COLUMN `customContentJson` `customContentJson` LONGTEXT NULL DEFAULT NULL COLLATE 'utf8mb4_unicode_520_ci' AFTER `customFieldsJson`;
