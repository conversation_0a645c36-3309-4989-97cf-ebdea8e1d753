CREATE TABLE `blog_x_author` (
	 `blogId` INT(11) NOT NULL,
	 `authorId` INT(11) NOT NULL,
	 PRIMARY KEY (`blogId`, `authorId`) USING BTREE,
	 INDEX `FK_blog_x_author_blog` (`blogId`) USING BTREE,
	 INDEX `FK_blog_x_author_author` (`authorId`) USING BTREE,
	 CONSTRAINT `FK_blog_x_author_author` FOREI<PERSON><PERSON> (`authorId`) REFERENCES `author` (`id`) ON UPDATE CASCADE ON DELETE CASCADE,
	 CONSTRAINT `FK_blog_x_author_blog` FOREI<PERSON><PERSON> KEY (`blogId`) REFERENCES `blog` (`id`) ON UPDATE CASCADE ON DELETE CASCADE
)
	COLLATE='utf8_bin'
ENGINE=InnoDB
;
