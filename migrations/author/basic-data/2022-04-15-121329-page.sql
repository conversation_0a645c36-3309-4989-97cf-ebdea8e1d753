/** tree entry point */
INSERT INTO `tree` (`id`, `rootId`, `parentId`, `level`, `path`, `sort`, `last`, `public`, `uid`, `created`, `createdTime`, `createdTimeOrder`, `edited`, `editedTime`, `template`, `type`, `publicFrom`, `publicTo`, `name`, `nameTitle`, `nameAnchor`, `annotation`, `description`, `keywords`, `content`, `hideFirstImage`, `links`, `seoTitleFilter`, `seoAnnotationFilter`, `seoDescriptionFilter`, `videos`, `hideCrossroad`, `hideInSitemap`, `forceNoIndex`, `hideInSearch`, `showContactForm`, `showOnHomepage`, `customFieldsJson`, `customContentJson`, `customContentSchemeJson`, `productAttachedId`, `hasLinkedCategories`) VALUES (449, 1, 1, 1, '1|', 1, 0, 1, 'authors', 0, '2021-08-24 10:20:43', '2021-08-24 10:20:43', 3, '2021-08-24 10:20:57', 'Author:default', 'common', '2021-08-24 10:20:43', '2121-08-24 10:20:43', 'Autoři', 'Autoři', 'Autoři', '', '', '', '', 0, '', NULL, NULL, NULL, '', 0, 0, 0, 0, 0, 0, '{}', '{}', '{}', NULL, NULL);
INSERT INTO `alias`  (`id`, `alias`, `module`, `referenceId`, `mutationId`)  VALUES (2207, 'autori', 'tree', 449, 1);


