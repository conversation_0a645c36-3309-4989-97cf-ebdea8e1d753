CREATE TABLE IF NOT EXISTS `publisher_parameter_value`
(
	id             int auto_increment,
	publisherId       int not null,
	parameterValue int not null,
	constraint publisher_parameter_value_pk
	primary key (id),
	constraint publisher_parameter_value_parameter_value_id_fk
	foreign key (parameterValue) references parameter_value (id),
	constraint publisher_parameter_value_publisher_id_fk
	foreign key (publisherId) references publisher (id)
	)
	ENGINE = InnoDB
	AUTO_INCREMENT = 1
	DEFAULT CHARSET = utf8
	COLLATE = utf8_bin;
