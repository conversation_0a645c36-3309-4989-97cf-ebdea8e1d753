INSERT INTO `parameter` (`id`, `parentId`, `name`, `uid`, `type`, `sort`, `variantParameter`, `isInFilter`, `customFieldsJson`, `isProtected`, `extId`) VALUES
	(1, NULL, 'Parametry', 'test', 'text', 0, 0, 0, NULL, 0, NULL);
INSERT INTO `parameter` (`id`, `parentId`, `name`, `uid`, `type`, `sort`, `variantParameter`, `isInFilter`, `customFieldsJson`, `isProtected`, `extId`) VALUES
	(112, NULL, 'Stránky', 'stranky', 'multiselect', 1, 0, 0, NULL, 0, NULL);
INSERT INTO `parameter` (`id`, `parentId`, `name`, `uid`, `type`, `sort`, `variantParameter`, `isInFilter`, `customFieldsJson`, `isProtected`, `extId`) VALUES
	(113, 1, 'Select', 'select', 'select', 2, 1, 1, '[]', 0, NULL);
INSERT INTO `parameter` (`id`, `parentId`, `name`, `uid`, `type`, `sort`, `variantParameter`, `isInFilter`, `customFieldsJson`, `isProtected`, `extId`) VALUES
	(114, 1, 'Multiselect', 'multiselect', 'multiselect', 1, 0, 1, '[]', 0, NULL);
INSERT INTO `parameter` (`id`, `parentId`, `name`, `uid`, `type`, `sort`, `variantParameter`, `isInFilter`, `customFieldsJson`, `isProtected`, `extId`) VALUES
	(115, 112, 'Tag', 'tag', 'multiselect', 1, 0, 0, '[]', 0, NULL);
INSERT INTO `parameter` (`id`, `parentId`, `name`, `uid`, `type`, `sort`, `variantParameter`, `isInFilter`, `customFieldsJson`, `isProtected`, `extId`) VALUES
	(116, 1, 'Bool', 'bool', 'bool', 3, 0, 1, '[]', 0, NULL);
INSERT INTO `parameter` (`id`, `parentId`, `name`, `uid`, `type`, `sort`, `variantParameter`, `isInFilter`, `customFieldsJson`, `isProtected`, `extId`) VALUES
	(117, 1, 'Text', 'text', 'text', 4, 0, 0, '[]', 0, NULL);
INSERT INTO `parameter` (`id`, `parentId`, `name`, `uid`, `type`, `sort`, `variantParameter`, `isInFilter`, `customFieldsJson`, `isProtected`, `extId`) VALUES
	(118, 1, 'Textarea', 'textarea', 'textarea', 5, 0, 0, '[]', 0, NULL);
INSERT INTO `parameter` (`id`, `parentId`, `name`, `uid`, `type`, `sort`, `variantParameter`, `isInFilter`, `customFieldsJson`, `isProtected`, `extId`) VALUES
	(119, 1, 'Číslo', 'cislo', 'number', 6, 0, 1, '[]', 0, NULL);
INSERT INTO `parameter` (`id`, `parentId`, `name`, `uid`, `type`, `sort`, `variantParameter`, `isInFilter`, `customFieldsJson`, `isProtected`, `extId`) VALUES
	(120, 1, 'Wysiwyg', 'wysiwyg', 'wysiwyg', 7, 0, 0, '[]', 0, NULL);
INSERT INTO `parameter` (`id`, `parentId`, `name`, `uid`, `type`, `sort`, `variantParameter`, `isInFilter`, `customFieldsJson`, `isProtected`, `extId`) VALUES
	(121, 1, 'Kategorie zbraně', 'kategorieZbrane', 'select', 8, 0, 1, '{}', 0, NULL);
INSERT INTO `parameter` (`id`, `parentId`, `name`, `uid`, `type`, `sort`, `variantParameter`, `isInFilter`, `customFieldsJson`, `isProtected`, `extId`) VALUES
	(127, 1, 'Ráže', 'raze', 'select', 9, 0, 1, '{}', 0, NULL);
INSERT INTO `parameter` (`id`, `parentId`, `name`, `uid`, `type`, `sort`, `variantParameter`, `isInFilter`, `customFieldsJson`, `isProtected`, `extId`) VALUES
	(128, 1, 'Délka hlavně', 'delkaHlavne', 'number', 10, 0, 1, '{}', 0, NULL);
INSERT INTO `parameter` (`id`, `parentId`, `name`, `uid`, `type`, `sort`, `variantParameter`, `isInFilter`, `customFieldsJson`, `isProtected`, `extId`) VALUES
	(129, 112, 'Enter a new name', NULL, 'multiselect', 1, 0, 0, '{}', 0, NULL);
INSERT INTO `parameter` (`id`, `parentId`, `name`, `uid`, `type`, `sort`, `variantParameter`, `isInFilter`, `customFieldsJson`, `isProtected`, `extId`) VALUES
	(130, 1, 'Enter a new name', NULL, 'multiselect', 1, 0, 0, '{}', 0, NULL);
INSERT INTO `parameter` (`id`, `parentId`, `name`, `uid`, `type`, `sort`, `variantParameter`, `isInFilter`, `customFieldsJson`, `isProtected`, `extId`) VALUES
	(131, 1, 'Enter a new name', NULL, 'multiselect', 1, 0, 0, '{}', 0, NULL);
