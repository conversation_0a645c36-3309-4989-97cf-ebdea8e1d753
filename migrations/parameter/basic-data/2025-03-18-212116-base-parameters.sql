INSERT INTO parameter (id, name, uid, type, sort, pendingSort, variantParameter, isInFilter, isLockedForES, isInDetail, customFieldsJson, isProtected, extId, typeSort) VALUES (1, '<PERSON>r<PERSON><PERSON><PERSON><PERSON> kurzu', 'pru<PERSON><PERSON><PERSON><PERSON><PERSON>', 'select', 0, 0, 0, 1, 0, 0, '{}', 0, null, null);
INSERT INTO parameter (id, name, uid, type, sort, pendingSort, variantParameter, isInFilter, isLockedForES, isInDetail, customFieldsJson, isProtected, extId, typeSort) VALUES (2, 'Typ kurzu', 'typKurzu', 'select', 0, 0, 0, 1, 0, 0, '{}', 0, null, null);
INSERT INTO parameter (id, name, uid, type, sort, pendingSort, variantParameter, isInFilter, isLockedForES, isInDetail, customFieldsJson, isProtected, extId, typeSort) VALUES (3, 'Obtížnost', 'obtiznost', 'multiselect', 0, 0, 0, 1, 0, 0, '{}', 0, null, null);
INSERT INTO parameter (id, name, uid, type, sort, pendingSort, variantParameter, isInFilter, isLockedForES, isInDetail, customFieldsJson, isProtected, extId, typeSort) VALUES (4, 'Podle užití', 'podleUziti', 'multiselect', 0, 0, 0, 1, 0, 0, '{}', 0, null, null);
INSERT INTO parameter (id, name, uid, type, sort, pendingSort, variantParameter, isInFilter, isLockedForES, isInDetail, customFieldsJson, isProtected, extId, typeSort) VALUES (5, 'Délka kurzu (hladiny)', 'delkaKurzu', 'select', 0, 1, 0, 1, 0, 0, '{}', 0, null, null);


INSERT INTO parameter_value (id, parameterId, internalValue, internalAlias, sort, parameterSort, prioritySort, isHidden, extId, customFieldsJson) VALUES (1, 1, 'Online', 'online', 0, 0, 0, 0, null, '{}');
INSERT INTO parameter_value (id, parameterId, internalValue, internalAlias, sort, parameterSort, prioritySort, isHidden, extId, customFieldsJson) VALUES (2, 1, 'Na místě', 'na-miste', 0, 0, 0, 0, null, '{}');
INSERT INTO parameter_value (id, parameterId, internalValue, internalAlias, sort, parameterSort, prioritySort, isHidden, extId, customFieldsJson) VALUES (3, 1, 'Na živo', 'na-zivo', 0, 0, 0, 0, null, '{}');
INSERT INTO parameter_value (id, parameterId, internalValue, internalAlias, sort, parameterSort, prioritySort, isHidden, extId, customFieldsJson) VALUES (4, 1, 'Kombinované', 'kombinovane', 0, 0, 0, 0, null, '{}');
INSERT INTO parameter_value (id, parameterId, internalValue, internalAlias, sort, parameterSort, prioritySort, isHidden, extId, customFieldsJson) VALUES (5, 2, 'Bez státního příspěvku', 'bez-statniho-prispevku', 0, 0, 0, 0, null, '{}');
INSERT INTO parameter_value (id, parameterId, internalValue, internalAlias, sort, parameterSort, prioritySort, isHidden, extId, customFieldsJson) VALUES (6, 2, 'Se státním příspěvkem', 'se-statnim-prispevkem', 0, 0, 0, 0, null, '{}');
INSERT INTO parameter_value (id, parameterId, internalValue, internalAlias, sort, parameterSort, prioritySort, isHidden, extId, customFieldsJson) VALUES (7, 3, 'Pro začátečníky', 'pro-zacatecniky', 0, 0, 0, 0, null, '{}');
INSERT INTO parameter_value (id, parameterId, internalValue, internalAlias, sort, parameterSort, prioritySort, isHidden, extId, customFieldsJson) VALUES (8, 3, 'Pro pokročilé', 'pro-pokrocile', 0, 0, 0, 0, null, '{}');
INSERT INTO parameter_value (id, parameterId, internalValue, internalAlias, sort, parameterSort, prioritySort, isHidden, extId, customFieldsJson) VALUES (9, 3, 'Pro profesionály', 'pro-profesionaly', 0, 0, 0, 0, null, '{}');
INSERT INTO parameter_value (id, parameterId, internalValue, internalAlias, sort, parameterSort, prioritySort, isHidden, extId, customFieldsJson) VALUES (10, 4, 'Zemědělství', 'zemedelstvi', 0, 0, 0, 0, null, '{}');
INSERT INTO parameter_value (id, parameterId, internalValue, internalAlias, sort, parameterSort, prioritySort, isHidden, extId, customFieldsJson) VALUES (11, 4, 'Stavebnictví', 'stavebnictvi', 0, 0, 0, 0, null, '{}');
INSERT INTO parameter_value (id, parameterId, internalValue, internalAlias, sort, parameterSort, prioritySort, isHidden, extId, customFieldsJson) VALUES (12, 4, 'Lesnictví', 'lesnictvi', 0, 0, 0, 0, null, '{}');
INSERT INTO parameter_value (id, parameterId, internalValue, internalAlias, sort, parameterSort, prioritySort, isHidden, extId, customFieldsJson) VALUES (13, 4, 'Film & reklama', 'film-reklama', 0, 0, 0, 0, null, '{}');
INSERT INTO parameter_value (id, parameterId, internalValue, internalAlias, sort, parameterSort, prioritySort, isHidden, extId, customFieldsJson) VALUES (14, 4, 'Průmysl a inspekci', 'prumysl-a-inspekci', 0, 0, 0, 0, null, '{}');
INSERT INTO parameter_value (id, parameterId, internalValue, internalAlias, sort, parameterSort, prioritySort, isHidden, extId, customFieldsJson) VALUES (15, 4, 'Záchranné drony', 'zachranne-drony', 0, 0, 0, 0, null, '{}');
INSERT INTO parameter_value (id, parameterId, internalValue, internalAlias, sort, parameterSort, prioritySort, isHidden, extId, customFieldsJson) VALUES (16, 4, 'Policejní drony', 'policejni-drony', 0, 0, 0, 0, null, '{}');
INSERT INTO parameter_value (id, parameterId, internalValue, internalAlias, sort, parameterSort, prioritySort, isHidden, extId, customFieldsJson) VALUES (17, 4, 'Vojenské drony', 'vojenske-drony', 0, 0, 0, 0, null, '{}');
INSERT INTO parameter_value (id, parameterId, internalValue, internalAlias, sort, parameterSort, prioritySort, isHidden, extId, customFieldsJson) VALUES (18, 4, 'Indoor drony', 'indoor-drony', 0, 0, 0, 0, null, '{}');
INSERT INTO parameter_value (id, parameterId, internalValue, internalAlias, sort, parameterSort, prioritySort, isHidden, extId, customFieldsJson) VALUES (19, 4, 'Autonomní drony a roje', 'autonomni-drony-a-roje', 0, 0, 0, 0, null, '{}');
INSERT INTO parameter_value (id, parameterId, internalValue, internalAlias, sort, parameterSort, prioritySort, isHidden, extId, customFieldsJson) VALUES (20, 4, 'Lightshow drony', 'lightshow-drony', 0, 0, 0, 0, null, '{}');
INSERT INTO parameter_value (id, parameterId, internalValue, internalAlias, sort, parameterSort, prioritySort, isHidden, extId, customFieldsJson) VALUES (21, 5, 'Půldenní a kratší', 'puldenni-a-kratsi', 0, 0, 0, 0, null, '{}');
INSERT INTO parameter_value (id, parameterId, internalValue, internalAlias, sort, parameterSort, prioritySort, isHidden, extId, customFieldsJson) VALUES (22, 5, 'Celodenní', 'celodenni', 0, 0, 0, 0, null, '{}');
INSERT INTO parameter_value (id, parameterId, internalValue, internalAlias, sort, parameterSort, prioritySort, isHidden, extId, customFieldsJson) VALUES (23, 5, 'Vícedenní', 'vicedenni', 0, 0, 0, 0, null, '{}');
