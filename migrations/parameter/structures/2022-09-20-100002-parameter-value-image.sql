CREATE TABLE IF NOT EXISTS `parameter_value_image` (
	`id` int(11) NOT NULL AUTO_INCREMENT,
	`parameterValueId` int(11) NOT NULL COMMENT 'id hodnoty parametru',
	`imageId` int(11) NOT NULL COMMENT 'idfile',
	`name` varchar(250) COLLATE utf8_czech_ci NOT NULL,
	`url` varchar(250) COLLATE utf8_czech_ci NOT NULL,
	`sort` tinyint(4) NOT NULL,
	PRIMARY KEY (`id`),
	UNIQUE KEY `parameterValueId_imageId` (`parameterValueId`,`imageId`),
	KEY `imageId` (`imageId`),
	KEY `parameterValueId` (`parameterValueId`),
	CONSTRAINT `parameter_value_image_ibfk_1` FOREIGN KEY (`parameterValueId`) REFERENCES `parameter_value` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
	CONSTRAINT `parameter_value_image_ibfk_2` FOREIGN KEY (`imageId`) REFERENCES `image` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_czech_ci;
