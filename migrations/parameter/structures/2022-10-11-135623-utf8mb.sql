ALTER TABLE `parameter`
	<PERSON>AN<PERSON> COLUMN `name` `name` VA<PERSON>HA<PERSON>(250) NOT NULL COLLATE 'utf8mb4_unicode_520_ci' AFTER `id`,
	CHANGE COLUMN `uid` `uid` VARCHAR(250) NULL DEFAULT NULL COLLATE 'utf8mb4_unicode_520_ci' AFTER `name`,
	CHANGE COLUMN `type` `type` ENUM('text','textarea','wysiwyg','number','bool','select','multiselect') NOT NULL COLLATE 'utf8mb4_unicode_520_ci' AFTER `uid`,
	<PERSON><PERSON><PERSON> COLUMN `customFieldsJson` `customFields<PERSON>son` TEXT NULL DEFAULT NULL COLLATE 'utf8mb4_unicode_520_ci' AFTER `isInFilter`,
	CHANGE COLUMN `extId` `extId` VARCHAR(32) NULL DEFAULT NULL COLLATE 'utf8mb4_unicode_520_ci' AFTER `isProtected`;


ALTER TABLE `parameter_value`
	CHANGE COLUMN `internalValue` `internalValue` TEXT NOT NULL COLLATE 'utf8mb4_unicode_520_ci' AFTER `parameterId`,
	CHANGE COLUMN `internalAlias` `internalAlias` VARCHAR(200) NOT NULL COLLATE 'utf8mb4_unicode_520_ci' AFTER `internalValue`,
	CHANGE COLUMN `extId` `extId` VARCHAR(32) NULL DEFAULT NULL COLLATE 'utf8mb4_unicode_520_ci' AFTER `parameterSort`,
	CHANGE COLUMN `customFieldsJson` `customFieldsJson` LONGTEXT NULL DEFAULT NULL COLLATE 'utf8mb4_unicode_520_ci' AFTER `extId`;
