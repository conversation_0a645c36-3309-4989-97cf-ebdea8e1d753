INSERT INTO parameter (name, uid, type, sort, pendingSort, variantParameter, isInFilter, isLockedForES, isInDetail, customFieldsJson, isProtected, extId, typeSort, productType)
VALUES ('Barva', 'color', 'select', 0, 0, 1, 0, 0, 0, '{}', 0, null, null, 'product');

SET @colorId = LAST_INSERT_ID();

INSERT INTO parameter_value (parameterId, internalValue, internalAlias, sort, parameterSort, prioritySort, isHidden, extId, customFieldsJson)
VALUES (@colorId, 'Červená', 'red', 0, 0, 0, 0, null, '{}');

INSERT INTO parameter_value (parameterId, internalValue, internalAlias, sort, parameterSort, prioritySort, isHidden, extId, customFieldsJson)
VALUES (@colorId, 'Modrá', 'blue', 0, 0, 0, 0, null, '{}');


INSERT INTO parameter (name, uid, type, sort, pendingSort, variantParameter, isInFilter, isLockedForES, isInDetail, customFieldsJson, isProtected, extId, typeSort, productType)
VALUES ('Velikost', 'size', 'select', 0, 0, 1, 0, 0, 0, '{}', 0, null, null, 'product');
SET @sizeId = LAST_INSERT_ID();

INSERT INTO parameter_value (parameterId, internalValue, internalAlias, sort, parameterSort, prioritySort, isHidden, extId, customFieldsJson)
VALUES (@sizeId, 'XL', 'xl', 0, 0, 0, 0, null, '{}');

INSERT INTO parameter_value (parameterId, internalValue, internalAlias, sort, parameterSort, prioritySort, isHidden, extId, customFieldsJson)
VALUES (@sizeId, 'L', 'l', 0, 0, 0, 0, null, '{}');

INSERT INTO parameter_value (parameterId, internalValue, internalAlias, sort, parameterSort, prioritySort, isHidden, extId, customFieldsJson)
VALUES (@sizeId, 'S', 's', 0, 0, 0, 0, null, '{}');
