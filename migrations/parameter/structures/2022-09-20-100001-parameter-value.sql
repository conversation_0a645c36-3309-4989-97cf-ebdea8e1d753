CREATE TABLE IF NOT EXISTS `parameter_value` (
	`id` int(11) NOT NULL AUTO_INCREMENT,
	`parameterId` int(11) NOT NULL,
	`internalValue` text NOT NULL,
	`internalAlias` varchar(200) NOT NULL,
	`sort` int(11) NOT NULL,
	`parameterSort` int(11) NOT NULL,
	`extId` varchar(32) DEFAULT NULL,
	PRIMARY KEY (`id`),
	UNIQUE KEY `extId` (`extId`),
	KEY `parameterId` (`parameterId`),
	CONSTRAINT `parameter_value_ibfk_1` FOREIGN KEY (`parameterId`) REFERENCES `parameter` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;
