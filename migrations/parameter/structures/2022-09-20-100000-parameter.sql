CREATE TABLE IF NOT EXISTS `parameter` (
	`id` int(11) NOT NULL AUTO_INCREMENT,
	`parentId` int(11) DEFAULT NULL,
	`name` varchar(250) NOT NULL,
	`uid` varchar(250) DEFAULT NULL,
	`type` enum('text','textarea','wysiwyg','number','bool','select','multiselect') NOT NULL,
	`sort` int(11) NOT NULL,
	`variantParameter` tinyint(4) NOT NULL,
	`isInFilter` int(11) NOT NULL DEFAULT '0',
	`customFieldsJson` text,
	`isProtected` tinyint(4) DEFAULT '0',
	`extId` varchar(32) DEFAULT NULL,
	PRIMARY KEY (`id`),
	UNIQUE KEY `uid` (`uid`),
	UNIQUE KEY `extId` (`extId`),
	KEY `parentId` (`parentId`),
	CONSTRAINT `parameter_ibfk_1` FOREIGN KEY (`parentId`) REFERENCES `parameter` (`id`) ON DELETE CASCADE ON UPDATE NO ACTION
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;
