CREATE TABLE `promotion` (
	 `id` INT(11) NOT NULL AUTO_INCREMENT,
	 `internalName` VARCHAR(255) NOT NULL DEFAULT '' COLLATE 'utf8mb4_unicode_520_ci',
	 `customFieldsJson` LONGTEXT NULL DEFAULT NULL COLLATE 'utf8mb4_unicode_520_ci',
	 PRIMARY KEY (`id`) USING BTREE
)
	COLLATE='utf8mb3_bin'
ENGINE=InnoDB
;


CREATE TABLE `promotion_localization` (
  `id` INT(11) NOT NULL AUTO_INCREMENT,
  `promotionId` INT(11) NOT NULL,
  `mutationId` INT(11) NOT NULL,
  `name` VARCHAR(250) NOT NULL DEFAULT '' COLLATE 'utf8mb4_unicode_520_ci',
  `public` INT(11) NOT NULL DEFAULT '0',
	`edited` INT(11) NULL DEFAULT NULL,
	`editedTime` DATETIME NULL DEFAULT NULL,
	`customFieldsJson` LONGTEXT NULL DEFAULT NULL COLLATE 'utf8mb4_unicode_520_ci',
	`customContentJson` LONGTEXT NULL DEFAULT NULL COLLATE 'utf8mb4_unicode_520_ci',
	PRIMARY KEY (`id`) USING BTREE,
	INDEX `FK_promotion_mutation` (`mutationId`) USING BTREE,
	INDEX `FK_promotion_localization_promotion` (`promotionId`) USING BTREE,
	CONSTRAINT `FK_promotion_localization_promotion` FOREIGN KEY (`promotionId`) REFERENCES `promotion` (`id`) ON UPDATE CASCADE ON DELETE CASCADE,
	CONSTRAINT `FK_promotion_mutation` FOREIGN KEY (`mutationId`) REFERENCES `mutation` (`id`) ON UPDATE CASCADE ON DELETE CASCADE
	)
	COLLATE='utf8mb3_bin'
ENGINE=InnoDB
AUTO_INCREMENT=1000
;

ALTER TABLE `promotion`
	ADD COLUMN `type` VARCHAR(50) NOT NULL AFTER `id`;

ALTER TABLE `promotion_localization`
	ADD COLUMN `publicFrom` DATETIME NOT NULL AFTER `editedTime`,
	ADD COLUMN `publicTo` DATETIME NOT NULL AFTER `publicFrom`;

ALTER TABLE `promotion`
	ADD COLUMN `typeBonusItem` LONGTEXT NOT NULL AFTER `type`;

ALTER TABLE `promotion`
	ADD COLUMN `typeQuantity` LONGTEXT NOT NULL AFTER `typeBonusItem`;


CREATE TABLE `promotion_x_product` (
   `id` INT(11) NOT NULL AUTO_INCREMENT,
   `promotionId` INT(11) NOT NULL,
   `productId` INT(11) NOT NULL,
   PRIMARY KEY (`id`) USING BTREE,
   INDEX `FK_promotion_x_product_promotion` (`promotionId`) USING BTREE,
   INDEX `FK_promotion_x_product_product` (`productId`) USING BTREE,
   CONSTRAINT `FK_promotion_x_product_product` FOREIGN KEY (`productId`) REFERENCES `product` (`id`) ON UPDATE CASCADE ON DELETE CASCADE,
   CONSTRAINT `FK_promotion_x_product_promotion` FOREIGN KEY (`promotionId`) REFERENCES `promotion` (`id`) ON UPDATE CASCADE ON DELETE CASCADE
)
	COLLATE='utf8mb4_unicode_520_ci'
ENGINE=InnoDB
;


ALTER TABLE `promotion_x_product`
	ADD UNIQUE INDEX `promotionId_productId` (`promotionId`, `productId`);

