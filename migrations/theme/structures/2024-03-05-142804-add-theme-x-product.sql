drop table theme_parameter_value;

CREATE TABLE IF NOT EXISTS `theme_x_product`
(
	id        int auto_increment,
	themeId   int not null,
	productId int not null,
	constraint theme_x_product_pk
		primary key (id),
	constraint theme_x_product_product_id_fk
		foreign key (productId) references product (id),
	constraint theme_x_product_theme_id_fk
		foreign key (themeId) references theme (id)
)
	ENGINE = InnoDB
	AUTO_INCREMENT = 1
	DEFAULT CHARSET = utf8
	COLLATE = utf8_bin;
