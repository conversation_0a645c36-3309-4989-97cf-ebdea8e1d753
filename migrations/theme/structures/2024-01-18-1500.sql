CREATE TABLE IF NOT EXISTS `theme_parameter_value`
(
	id             int auto_increment,
	themeId       int not null,
	parameterValue int not null,
	constraint theme_parameter_value_pk
	primary key (id),
	constraint theme_parameter_value_parameter_value_id_fk
	foreign key (parameterValue) references parameter_value (id),
	constraint theme_parameter_value_theme_id_fk
	foreign key (themeId) references theme (id)
	)
	ENGINE = InnoDB
	AUTO_INCREMENT = 1
	DEFAULT CHARSET = utf8
	COLLATE = utf8_bin;
