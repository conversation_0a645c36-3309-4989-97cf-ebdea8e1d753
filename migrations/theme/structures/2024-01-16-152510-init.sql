create table if not exists `theme`
(
	id               int auto_increment,
	internalName     varchar(50) character set utf8 collate utf8_czech_ci not null default '',
	customFields<PERSON>son longtext                                             null,
	parameterValueId int                                                  null,
	primary key (`id`) using btree,
	constraint theme_parameter_value_id_fk
	foreign key (parameterValueId) references parameter_value (id)
	) ENGINE = InnoDB
	AUTO_INCREMENT = 1
	DEFAULT CHARSET = utf8
	COLLATE = utf8_bin;

alter table theme
	modify parameterValueId int null after id;

alter table theme
	add `order` int null;

alter table theme
	add isTrending tinyint(1) default 0 not null after internalName;

alter table theme
	add isPopular tinyint(1) default 0 not null after isTrending;

alter table theme
	modify `order` int null after isPopular;

alter table theme
	change parameterValueId parameterValueTheme int null;

CREATE TABLE IF NOT EXISTS `writer_parameter_value`
(
	id             int auto_increment,
	writerId       int not null,
	parameterValue int not null,
	constraint writer_parameter_value_pk
	primary key (id),
	constraint writer_parameter_value_parameter_value_id_fk
	foreign key (parameterValue) references parameter_value (id),
	constraint writer_parameter_value_writer_id_fk
	foreign key (writerId) references writer (id)
	)
	ENGINE = InnoDB
	AUTO_INCREMENT = 1
	DEFAULT CHARSET = utf8
	COLLATE = utf8_bin;
