create table theme_localization_visit
(
	id                  int auto_increment,
	themeLocalizationId int                            not null,
	visitDate           date default CURRENT_TIMESTAMP not null,
	view                int  default 0                 not null,
	constraint theme_localization_visit_pk
		primary key (id),
	constraint theme_localization_visit_theme_localization_id_fk
		foreign key (themeLocalizationId) references theme_localization (id)
) engine=InnoDB;

