CREATE TABLE `seolink` (
	`id` INT(11) NOT NULL AUTO_INCREMENT,
	`internalName` VARCHAR(50) NOT NULL DEFAULT '' COLLATE 'utf8mb4_unicode_520_ci',
	`customFields<PERSON>son` LONGTEXT DEFAULT NULL COLLATE 'utf8mb4_unicode_520_ci',
	`parameterValuesIds` VARCHAR(768) NOT NULL DEFAULT '' COLLATE 'utf8mb4_bin',
	PRIMARY KEY (`id`) USING BTREE,
	UNIQUE INDEX `parameterValuesIds` (`parameterValuesIds`) USING BTREE
)
	COLLATE 'utf8mb4_unicode_520_ci'
	ENGINE=InnoDB
	AUTO_INCREMENT=1;


CREATE TABLE `seolink_localization` (
	`id` INT(11) NOT NULL AUTO_INCREMENT,
	`mutationId` INT(11) NOT NULL DEFAULT '1',
	`seoLinkId` INT(11) NOT NULL,
	`name` VARCHAR(250) DEFAULT NULL COLLATE 'utf8mb4_unicode_520_ci',
	`nameAnchor` VARCHAR(250) DEFAULT NULL COLLATE 'utf8mb4_unicode_520_ci',
	`nameTitle` VARCHAR(250) DEFAULT NULL COLLATE 'utf8mb4_unicode_520_ci',
	`description` TEXT DEFAULT NULL COLLATE 'utf8mb4_unicode_520_ci',
	`keywords` TEXT DEFAULT NULL COLLATE 'utf8mb4_unicode_520_ci',
	`title` VARCHAR(250) DEFAULT NULL COLLATE 'utf8mb4_unicode_520_ci',
	`customFieldsJson` LONGTEXT NULL DEFAULT NULL COLLATE 'utf8mb4_unicode_520_ci',
	`customContentJson` LONGTEXT NULL DEFAULT NULL COLLATE 'utf8mb4_unicode_520_ci',
	PRIMARY KEY (`id`) USING BTREE,
	INDEX (`mutationId`) USING BTREE,
	INDEX (`seolinkId`) USING BTREE,
	CONSTRAINT FOREIGN KEY (`mutationId`) REFERENCES `mutation` (`id`) ON UPDATE CASCADE ON DELETE CASCADE,
	CONSTRAINT FOREIGN KEY (`seolinkId`) REFERENCES `seolink` (`id`) ON UPDATE CASCADE ON DELETE CASCADE
)
	COLLATE 'utf8mb4_unicode_520_ci'
	ENGINE=InnoDB
	AUTO_INCREMENT=1;


CREATE TABLE `seolink_x_parameter_value` (
	`seolinkId` INT(11) NOT NULL,
	`parameterValueId` INT(11) NOT NULL,
	PRIMARY KEY (`seolinkId`, `parameterValueId`) USING BTREE,
	INDEX (`seolinkId`) USING BTREE,
	INDEX (`parameterValueId`) USING BTREE,
	CONSTRAINT FOREIGN KEY (`seolinkId`) REFERENCES `seolink` (`id`) ON UPDATE CASCADE ON DELETE CASCADE,
	CONSTRAINT FOREIGN KEY (`parameterValueId`) REFERENCES `parameter_value` (`id`) ON UPDATE CASCADE ON DELETE CASCADE
)
	COLLATE='utf8mb4_bin'
	ENGINE=InnoDB;
