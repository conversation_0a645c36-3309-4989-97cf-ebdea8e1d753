CREATE TABLE IF NOT EXISTS `shop_review` (
	id int(11) NOT NULL AUTO_INCREMENT,
	syncChecksum varchar(255) DEFAULT NULL COLLATE 'utf8mb4_unicode_520_ci',
	syncTime datetime DEFAULT NULL,
	extId varchar(255) DEFAULT NULL COLLATE 'utf8mb4_unicode_520_ci',
	mutationId int(11) DEFAULT 1,
	importCode varchar(255) DEFAULT NULL COLLATE 'utf8mb4_unicode_520_ci',
	source varchar(50) DEFAULT NULL COLLATE 'utf8mb4_unicode_520_ci',
	reviewData longtext COLLATE 'utf8mb4_unicode_520_ci',
	date datetime DEFAULT NULL,
	createdAt datetime DEFAULT NOW(),
	PRIMARY KEY (id),
	FOREIGN KEY (mutationId) REFERENCES mutation(id) ON DELETE CASCADE ON UPDATE CASCADE
)
