SET NAMES utf8mb4;

INSERT INTO `tree` (`mutationId`, `rootId`, `extId`, `treeParentId`, `parentId`, `level`, `path`, `sort`, `last`, `uid`, `created`, `createdTime`, `createdTimeOrder`, `edited`, `editedTime`, `template`, `type`, `publicFrom`, `publicTo`, `name`, `nameAnchor`, `nameTitle`, `nameShort`, `description`, `keywords`, `public`, `score`, `forceNoIndex`, `hideInSearch`, `hideInSitemap`, `hideInMenu`, `annotation`, `content`, `hideFirstImage`, `links`, `seoTitleFilter`, `seoAnnotationFilter`, `seoDescriptionFilter`, `videos`, `customFieldsJson`, `customContentJson`, `productAttachedId`, `hasLinkedCategories`) VALUES
	(1,	1,	NULL,	496,	398,	2,	'1|398|',	9,	1,	'paymentLanding',	0,	'2024-06-10 20:35:51',	'2024-06-10 20:35:00',	32,	'2024-06-10 20:41:53',	':Front:Order:paymentLandingPage',	'common',	'2024-06-10 20:35:00',	'2124-06-10 20:35:00',	'Stav zaplacení objednávky',	'Stav zaplacení objednávky',	'Stav zaplacení objednávky',	'',	'',	'',	1,	0,	1,	1,	0,	1,	'',	'',	NULL,	NULL,	NULL,	NULL,	NULL,	NULL,	'{}',	'{}',	NULL,	NULL);

SET @paymentLanding = LAST_INSERT_ID();
INSERT INTO `alias` ( `alias`, `module`, `referenceId`, `mutationId`) VALUES ('stav-objednavky',	'tree',	@paymentLanding,	1);
