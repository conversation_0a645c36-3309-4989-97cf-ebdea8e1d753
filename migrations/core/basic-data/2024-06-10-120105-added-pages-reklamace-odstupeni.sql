SET NAMES utf8mb4;

INSERT INTO `tree` (`mutationId`, `rootId`, `treeParentId`, `parentId`, `level`, `path`, `sort`, `last`, `uid`, `created`, `createdTime`, `createdTimeOrder`, `edited`, `editedTime`, `template`, `type`, `publicFrom`, `publicTo`, `name`, `nameAnchor`, `nameTitle`, `description`, `keywords`, `public`, `forceNoIndex`, `hideInSearch`, `hideInSitemap`, `annotation`, `content`, `hideFirstImage`, `links`, `seoTitleFilter`, `seoAnnotationFilter`, `seoDescriptionFilter`, `videos`, `customFieldsJson`, `customContentJson`, `productAttachedId`, `hasLinkedCategories`) VALUES
	(1,	1,	495,	1523,	2,	'1|1523|',	18,	1,	'reclamation',	0,	'2024-06-10 12:02:50',	'2024-06-10 12:02:50',	35,	'2024-06-10 12:02:50',	':Front:Page:default',	'common',	'2024-06-10 12:02:50',	'2124-06-10 12:02:50',	'Reklamace a odstoupení od smlouvy',	'Reklamace a odstoupení od smlouvy',	'Reklamace a odstoupení od smlouvy',	'',	'',	1,	0,	0,	0,	'',	'',	NULL,	NULL,	NULL,	NULL,	NULL,	NULL,	'{}',	'{}',	NULL,	NULL);
SET @reclamation = LAST_INSERT_ID();
INSERT INTO `alias` ( `alias`, `module`, `referenceId`, `mutationId`) VALUES ('reklamace-a-odstoupeni-od-smlouvy',	'tree',	@reclamation,	1);
