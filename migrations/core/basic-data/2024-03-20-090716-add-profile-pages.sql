SET NAMES utf8mb4;

INSERT INTO `tree` (`mutationId`, `rootId`, `treeParentId`, `parentId`, `level`, `path`, `sort`, `last`, `uid`, `created`, `createdTime`, `createdTimeOrder`, `edited`, `editedTime`, `template`, `type`, `publicFrom`, `publicTo`, `name`, `nameAnchor`, `nameTitle`, `description`, `keywords`, `public`, `forceNoIndex`, `hideInSearch`, `hideInSitemap`, `annotation`, `content`, `hideFirstImage`, `links`, `seoTitleFilter`, `seoAnnotationFilter`, `seoDescriptionFilter`, `videos`, `customFieldsJson`, `customContentJson`, `productAttachedId`, `hasLinkedCategories`) VALUES
   (1,	1,	451,	72,	2,	'1|72|',	9,	1,	'userAddress',	0,	'2024-03-20 08:56:26',	'2024-03-20 08:56:00',	33,	'2024-03-20 09:05:29',	':Front:User:default',	'common',	'2024-03-20 08:56:00',	'2124-03-20 08:56:00',	'Moje adresy',	'Moje adresy',	'Moje adresy',	'',	'',	1,	1,	1,	0,	'',	'',	NULL,	NULL,	NULL,	NULL,	NULL,	NULL,	'{}',	'{}',	NULL,	NULL);
SET @step1Id = LAST_INSERT_ID();
INSERT INTO `alias` ( `alias`, `module`, `referenceId`, `mutationId`) VALUES ('moje-adresy',	'tree',	@step1Id,	1);

INSERT INTO `tree` (`mutationId`, `rootId`, `treeParentId`, `parentId`, `level`, `path`, `sort`, `last`, `uid`, `created`, `createdTime`, `createdTimeOrder`, `edited`, `editedTime`, `template`, `type`, `publicFrom`, `publicTo`, `name`, `nameAnchor`, `nameTitle`, `description`, `keywords`, `public`, `forceNoIndex`, `hideInSearch`, `hideInSitemap`, `annotation`, `content`, `hideFirstImage`, `links`, `seoTitleFilter`, `seoAnnotationFilter`, `seoDescriptionFilter`, `videos`, `customFieldsJson`, `customContentJson`, `productAttachedId`, `hasLinkedCategories`) VALUES
   (1,	1,	452,	72,	2,	'1|72|',	10,	1,	'userOrderHistory',	0,	'2024-03-20 08:58:38',	'2024-03-20 08:58:00',	33,	'2024-03-20 09:02:48',	':Front:User:orderHistory',	'common',	'2024-03-20 08:58:00',	'2124-03-20 08:58:00',	'Historie objednávek',	'Historie objednávek',	'Historie objednávek',	'',	'',	1,	1,	1,	0,	'',	'',	NULL,	NULL,	NULL,	NULL,	NULL,	NULL,	'{}',	'{}',	NULL,	NULL);
SET @step2Id = LAST_INSERT_ID();
INSERT INTO `alias` ( `alias`, `module`, `referenceId`, `mutationId`) VALUES ('historie-objednavek',	'tree',	@step2Id,	1);

INSERT INTO `tree` (`mutationId`, `rootId`, `treeParentId`, `parentId`, `level`, `path`, `sort`, `last`, `uid`, `created`, `createdTime`, `createdTimeOrder`, `edited`, `editedTime`, `template`, `type`, `publicFrom`, `publicTo`, `name`, `nameAnchor`, `nameTitle`, `description`, `keywords`, `public`, `forceNoIndex`, `hideInSearch`, `hideInSitemap`, `annotation`, `content`, `hideFirstImage`, `links`, `seoTitleFilter`, `seoAnnotationFilter`, `seoDescriptionFilter`, `videos`, `customFieldsJson`, `customContentJson`, `productAttachedId`, `hasLinkedCategories`) VALUES
   (1,	1,	453,	72,	2,	'1|72|',	11,	1,	'userOrderHistoryDetail',	0,	'2024-03-20 09:02:19',	'2024-03-20 09:02:00',	33,	'2024-03-20 09:06:13',	':Front:User:orderHistory',	'common',	'2024-03-20 09:02:00',	'2124-03-20 09:02:00',	'Historie objednávek detail',	'Historie objednávek detail',	'Historie objednávek detail',	'',	'',	1,	1,	1,	0,	'',	'',	NULL,	NULL,	NULL,	NULL,	NULL,	NULL,	'{}',	'{}',	NULL,	NULL);
SET @step3Id = LAST_INSERT_ID();
INSERT INTO `alias` ( `alias`, `module`, `referenceId`, `mutationId`) VALUES ('historie-objednavek-detail',	'tree',	@step3Id,	1);


UPDATE tree SET customFieldsJson = '{"userMenuUnloggedUser":[{"tree":37},{"tree":76},{"tree":74},{}],"userMenuLoggedUser":[{"tree":72},{"tree":75},{"tree":455},{"tree":456},{"tree":405}],"userSideMenu":[{"tree":72},{"tree":413},{"tree":75},{"tree":455},{"tree":456},{"tree":405}]}' WHERE uid='userSection';
