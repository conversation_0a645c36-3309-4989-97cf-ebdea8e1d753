UPDATE `email_template` SET `body`='<h2><PERSON><PERSON><PERSON><PERSON> hesla</h2>\n<h4><PERSON><PERSON><PERSON><PERSON>te svoje he<PERSON>lo k<PERSON>nutím na odkaz:</h4>\n<p><span style="color: #848587; font-family: GTAmerica, sans-serif;"><a href="[DATA-link]">[DATA-link]</a></span></p>\n<p>________<strong></strong></p>\n<p>Úsp<PERSON>šn<PERSON> den přeje</p>\n<p>tým superadmin</p>' WHERE  `id`=6;


UPDATE `string` SET `value`='Odkaz pro reset hesla není platný' WHERE  `id`=7942;
UPDATE `string` SET `value`='Password reset link is not valid' WHERE  `id`=7943;



REPLACE INTO `string` (`lg`, `name`, `value`, `usedAt`) VALUES ('en', 'user_not_found', 'Account not found', NULL);
REPLACE INTO `string` (`lg`, `name`, `value`, `usedAt`) VALUES ('cs', 'user_not_found', 'Účet nenalezen', NULL);


REPLACE INTO `string` (`lg`, `name`, `value`, `usedAt`) VALUES ('en', 'message_ok_login', 'Successfully logged in', NULL);
REPLACE INTO `string` (`lg`, `name`, `value`, `usedAt`) VALUES ('cs', 'message_ok_login', 'Úspěšně přihlášen', NULL);

REPLACE INTO `string` (`lg`, `name`, `value`, `usedAt`) VALUES ('en', 'message_bad_login', 'Login failed', NULL);
REPLACE INTO `string` (`lg`, `name`, `value`, `usedAt`) VALUES ('cs', 'message_bad_login', 'Přihlášení se nepovedlo', NULL);
