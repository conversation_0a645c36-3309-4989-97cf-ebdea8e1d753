UPDATE string SET value='+ Přidat novou dodac<PERSON> ad<PERSON> (pro <PERSON><PERSON><PERSON><PERSON>)' WHERE name='add_address' and lg='cs';
UPDATE string SET value='Chci nakupovat na firmu' WHERE name='title_company_info' and lg='cs';

INSERT INTO string (lg, name, value, usedAt) VALUES( 'cs', 'form_label_address_title', 'Interní n<PERSON> adres<PERSON> (nezobrazujeme na zásilce)', NULL);
INSERT INTO string (lg, name, value, usedAt) VALUES( 'cs', 'form_label_address_note', 'Poznámka k adrese', NULL);
INSERT INTO string (lg, name, value, usedAt) VALUES( 'cs', 'title_personal_addresses', 'Moje adresy', NULL);
INSERT INTO string (lg, name, value, usedAt) VALUES( 'cs', 'form_label_default_address', 'Nastavit jako v<PERSON><PERSON><PERSON> adres<PERSON>', NULL);
INSERT INTO string (lg, name, value, usedAt) VALUES( 'cs', 'title_new_address', 'Nová dodací adresa', NULL);
INSERT INTO string (lg, name, value, usedAt) VALUES( 'cs', 'title_delivery_address_toggle', 'Chci zboží doručit na jinou než fakturační adresu', NULL);
INSERT INTO string (lg, name, value, usedAt) VALUES( 'cs', 'form_address_note', 'Poznámka k adrese', NULL);
