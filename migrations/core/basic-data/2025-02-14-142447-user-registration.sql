-- mail template new registration
INSERT INTO email_template (id, mutationId, `key`, isDeveloper, isHidden, name, subject, body) VALUES
	(null, 1, 'newRegistration', 0, 0, 'Registrace nového z<PERSON>azníka', 'Děkujeme za registraci', '<h2>Děkujeme za registrace</h2>
<h4>Dokončete svou registraci kliknutím na tento odkaz:</h4>
<p><span style="color: #848587; font-family: GTAmerica, sans-serif;"><a href="[DATA-link]">[DATA-link]</a></span></p>
<p>________<strong></strong></p>
<p>Úspěšný den přeje</p>
<p><span>tým <PERSON></span></p>');

alter table user add isActive tinyint(1) default 0 not null;
alter table user add activatedTime datetime null;

INSERT INTO string (id, lg, name, value, usedAt) VALUES (NULL, 'cs', 'user_is_already_active', 'Uživatel je jiz aktivován', null);
INSERT INTO string (id, lg, name, value, usedAt) VALUES (NULL, 'cs', 'registration_activated_ok', 'Registrace byla úspěšně potvrzena', null);
INSERT INTO string (id, lg, name, value, usedAt) VALUES (NULL, 'cs', 'registration_activated_error', 'Registrace nebyla aktivována', null);
INSERT INTO string (id, lg, name, value, usedAt) VALUES (NULL, 'cs', 'suspicious_activity_try_again_later', 'Podezřelá aktivita, zkuste to prosím za chvíli', null);
INSERT INTO string (id, lg, name, value, usedAt) VALUES (NULL, 'cs', 'form_register_ok', 'Registrace proběhla v pořádku. Dokončete svou registraci kliknutím na odkaz v e-mailu, který jsme právě odeslali.', null);
