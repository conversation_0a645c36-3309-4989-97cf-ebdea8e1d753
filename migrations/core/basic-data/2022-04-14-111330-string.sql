
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(3, 'cs', 'title', '##title');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(4, 'en', 'title', 'test');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(5, 'cs', 'skip_main', '##skip_main');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(6, 'en', 'skip_main', 'Přejít k obsahu');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7, 'cs', 'skip_menu', '##skip_menu');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8, 'en', 'skip_menu', '##skip_menu');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(9, 'cs', 'skip_search', '##skip_search');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(10, 'en', 'skip_search', '##skip_search');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(11, 'cs', 'logo', '##logo');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(12, 'en', 'logo', '##logo');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(13, 'cs', 'search_placeholder', 'Co hledáte?');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(14, 'en', 'search_placeholder', '##search_placeholder');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(15, 'cs', 'btn_search', 'Hledat');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(16, 'en', 'btn_search', '##btn_search');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(17, 'cs', 'compare_title', 'Porovnání');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(18, 'en', 'compare_title', '##compare_title');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(19, 'cs', 'login_title', 'Přihlášení');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(20, 'en', 'login_title', '##login_title');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(21, 'cs', 'e-mail', '##E-mail:');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(22, 'en', 'e-mail', '##E-mail:');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(23, 'cs', 'email', 'E-mail');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(24, 'en', 'email', '##email');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(25, 'cs', 'form_enter_username', '##form_enter_username');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(26, 'en', 'form_enter_username', '##form_enter_username');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(27, 'cs', 'heslo', '##Heslo:');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(28, 'en', 'heslo', '##Heslo:');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(31, 'cs', 'form_enter_password', '##form_enter_password');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(32, 'en', 'form_enter_password', '##form_enter_password');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(33, 'cs', 'link_lost_password', 'Zapomenuté heslo');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(34, 'en', 'link_lost_password', '##link_lost_password');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(35, 'cs', 'link_registration', 'Registrace');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(36, 'en', 'link_registration', '##link_registration');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(37, 'cs', 'btn_login', 'Přihlásit se');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(38, 'en', 'btn_login', '##btn_login');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(39, 'cs', 'basket_empty', '##basket_empty');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(40, 'en', 'basket_empty', '##basket_empty');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(41, 'cs', 'basket_empty_title', '##basket_empty_title');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(42, 'en', 'basket_empty_title', '##basket_empty_title');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(43, 'cs', 'basket_empty_text', '##basket_empty_text');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(44, 'en', 'basket_empty_text', '##basket_empty_text');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(45, 'cs', 'please_rewrite_value', '##please_rewrite_value');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(46, 'en', 'please_rewrite_value', '##please_rewrite_value');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(47, 'cs', 'newsletter_title', 'novinky, akce a recenze do vaší pošty');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(48, 'en', 'newsletter_title', '##newsletter_title');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(49, 'cs', 'enter_email', 'Zadejte e-mail');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(50, 'en', 'enter_email', '##enter_email');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(51, 'cs', 'form_error', 'Chyba ve formuláři');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(52, 'en', 'form_error', '##form_error');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(53, 'cs', 'copyright', '##copyright');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(54, 'en', 'copyright', '##copyright');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(55, 'cs', 'profile_title', 'Můj účet');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(56, 'en', 'profile_title', '##profile_title');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(57, 'cs', 'breadcrumb', '##breadcrumb');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(58, 'en', 'breadcrumb', '##breadcrumb');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(59, 'cs', 'breadcrumb_title', '##breadcrumb_title');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(60, 'en', 'breadcrumb_title', '##breadcrumb_title');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(61, 'cs', 'title_search_results', '##title_search_results');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(62, 'en', 'title_search_results', '##title_search_results');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(63, 'cs', 'search_results_categories', '##search_results_categories');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(64, 'en', 'search_results_categories', '##search_results_categories');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(65, 'cs', 'search_results_products', '##search_results_products');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(66, 'en', 'search_results_products', '##search_results_products');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(67, 'cs', 'search_results_blog', '##search_results_blog');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(68, 'en', 'search_results_blog', '##search_results_blog');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(69, 'cs', 'search_results_other', '##search_results_other');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(70, 'en', 'search_results_other', '##search_results_other');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(71, 'cs', 'search_empty_1', '##search_empty_1');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(72, 'en', 'search_empty_1', '##search_empty_1');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(73, 'cs', 'search_empty_2', '##search_empty_2');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(74, 'en', 'search_empty_2', '##search_empty_2');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(75, 'cs', 'contact_phone', '##contact_phone');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(76, 'en', 'contact_phone', '##contact_phone');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(77, 'cs', 'contact_hours', '##contact_hours');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(78, 'en', 'contact_hours', '##contact_hours');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(79, 'cs', 'filter_dial_color', '##filter_dial_color');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(80, 'en', 'filter_dial_color', '##filter_dial_color');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(81, 'cs', 'filter_flag_is_new', '##filter_flag_isNew');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(82, 'en', 'filter_flag_is_new', '##filter_flag_isNew');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(83, 'cs', 'filter_flag_is_old', '##filter_flag_isOld');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(84, 'en', 'filter_flag_is_old', '##filter_flag_isOld');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(85, 'cs', 'filter_flag_price_final_dph', '##filter_flag_priceFinalDPH');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(86, 'en', 'filter_flag_price_final_dph', '##filter_flag_priceFinalDPH');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(87, 'cs', 'filter_range_price_final_dph', '##filter_range_priceFinalDPH');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(88, 'en', 'filter_range_price_final_dph', '##filter_range_priceFinalDPH');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(89, 'cs', 'btn_filter_products', '##btn_filter_products');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(90, 'en', 'btn_filter_products', '##btn_filter_products');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(91, 'cs', 'is_new', '##isNew');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(92, 'en', 'is_new', '##isNew');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(93, 'cs', 'is_old', '##isOld');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(94, 'en', 'is_old', '##isOld');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(95, 'cs', 'filter_btn', '##filter_btn');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(96, 'en', 'filter_btn', '##filter_btn');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(97, 'cs', 'best_sellers', '##best_sellers');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(98, 'en', 'best_sellers', '##best_sellers');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(99, 'cs', 'latest', '##latest');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(100, 'en', 'latest', '##latest');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(101, 'cs', 'cheapest', '##cheapest');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(102, 'en', 'cheapest', '##cheapest');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(103, 'cs', 'most_expensive', '##most_expensive');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(104, 'en', 'most_expensive', '##most_expensive');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(105, 'cs', 'price_tax', 's DPH');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(106, 'en', 'price_tax', '##price_tax');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(107, 'cs', 'add_to_cart', '##add_to_cart');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(108, 'en', 'add_to_cart', '##add_to_cart');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(109, 'cs', 'blog_categories', '##blog_categories');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(110, 'en', 'blog_categories', '##blog_categories');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(111, 'cs', 'tags_title', '##tags_title');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(112, 'en', 'tags_title', '##tags_title');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(113, 'cs', 'product_flag_delivery', 'Doprava zdarma');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(114, 'en', 'product_flag_delivery', '##product_flag_delivery');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(115, 'cs', 'stock_one_piece', 'Poslední kus');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(116, 'en', 'stock_one_piece', '##stock_one_piece');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(117, 'cs', 'ihned-k-odberu', '##ihned k odběru');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(118, 'en', 'ihned-k-odberu', '##ihned k odběru');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(119, 'cs', 'stock_zero', 'Není skladem');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(120, 'en', 'stock_zero', '##stock_zero');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(121, 'cs', 'btn_delivery_info', '##btn_delivery_info');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(122, 'en', 'btn_delivery_info', '##btn_delivery_info');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(123, 'cs', 'title_warranties', '##title_warranties');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(124, 'en', 'title_warranties', '##title_warranties');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(125, 'cs', 'title_special_services', '##title_special_services');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(126, 'en', 'title_special_services', '##title_special_services');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(127, 'cs', 'price_without_tax', 'bez DPH');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(128, 'en', 'price_without_tax', '##price_without_tax');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(129, 'cs', 'count_title', '##count_title');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(130, 'en', 'count_title', '##count_title');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(131, 'cs', 'count_short', '##count_short');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(132, 'en', 'count_short', '##count_short');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(133, 'cs', 'product_id', '##product_id');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(134, 'en', 'product_id', '##product_id');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(135, 'cs', 'btn_compare_add', '##btn_compare_add');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(136, 'en', 'btn_compare_add', '##btn_compare_add');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(137, 'cs', 'title_parameters', 'Parametry');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(138, 'en', 'title_parameters', '##title_parameters');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(139, 'cs', 'title_products', '##title_products');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(140, 'en', 'title_products', '##title_products');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(141, 'cs', 'contact_address_1', '##contact_address_1');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(142, 'en', 'contact_address_1', '##contact_address_1');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(143, 'cs', 'contact_address_2', '##contact_address_2');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(144, 'en', 'contact_address_2', '##contact_address_2');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(145, 'cs', 'contact_hours_long', '##contact_hours_long');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(146, 'en', 'contact_hours_long', '##contact_hours_long');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(147, 'cs', 'day_2', 'úterý');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(148, 'en', 'day_2', '##day_2');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(149, 'cs', 'at_yours', '##at_yours');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(150, 'en', 'at_yours', '##at_yours');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(151, 'cs', 'day_3', 'středu');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(152, 'en', 'day_3', '##day_3');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(153, 'cs', 'day_4', 'čtvrtek');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(154, 'en', 'day_4', '##day_4');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(155, 'cs', 'free_delivery_treshold_logged', '##free_delivery_treshold_logged');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(156, 'en', 'free_delivery_treshold_logged', '##free_delivery_treshold_logged');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(157, 'cs', 'store_prague', '##store_prague');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(158, 'en', 'store_prague', '##store_prague');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(159, 'cs', 'store_map', '##store_map');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(160, 'en', 'store_map', '##store_map');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(161, 'cs', 'cards_accepted', '##cards_accepted');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(162, 'en', 'cards_accepted', '##cards_accepted');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(163, 'cs', 'free_parking', '##free_parking');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(164, 'en', 'free_parking', '##free_parking');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(165, 'cs', 'comment_form', '##comment_form');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(166, 'en', 'comment_form', '##comment_form');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(167, 'cs', 'form_valid_email', 'E-mailová adresa není platná.');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(168, 'en', 'form_valid_email', '##form_valid_email');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(169, 'cs', 'message', '##message');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(170, 'en', 'message', '##message');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(171, 'cs', 'comment', '##Comment');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(172, 'en', 'comment', '##Comment');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(173, 'cs', 'btn_comment', '##btn_comment');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(174, 'en', 'btn_comment', '##btn_comment');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(175, 'cs', 'btn_last_visited', '##btn_last_visited');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(176, 'en', 'btn_last_visited', '##btn_last_visited');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(177, 'cs', 'btn_show_all', '##btn_show_all');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(178, 'en', 'btn_show_all', '##btn_show_all');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(179, 'cs', 'contact_form_title', '##contact_form_title');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(180, 'en', 'contact_form_title', '##contact_form_title');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(181, 'cs', 'contact_type_complaint', '##contact_type_complaint');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(182, 'en', 'contact_type_complaint', '##contact_type_complaint');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(183, 'cs', 'contact_type_cooperation', '##contact_type_cooperation');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(184, 'en', 'contact_type_cooperation', '##contact_type_cooperation');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(185, 'cs', 'contact_type_question', '##contact_type_question');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(186, 'en', 'contact_type_question', '##contact_type_question');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(187, 'cs', 'contact_type_acclaim', '##contact_type_acclaim');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(188, 'en', 'contact_type_acclaim', '##contact_type_acclaim');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(189, 'cs', 'contact_type_other', '##contact_type_other');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(190, 'en', 'contact_type_other', '##contact_type_other');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(191, 'cs', 'name', '##name');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(192, 'en', 'name', '##name');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(193, 'cs', 'surname', '##surname');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(194, 'en', 'surname', '##surname');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(195, 'cs', 'phone', '##phone');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(196, 'en', 'phone', '##phone');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(197, 'cs', 'text', '##text');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(198, 'en', 'text', '##text');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(199, 'cs', 'form_files', 'Soubory');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(200, 'en', 'form_files', '##form_files');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(201, 'cs', 'select_file', '##select_file');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(202, 'en', 'select_file', '##select_file');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(203, 'cs', 'the-size-of-the-uploaded-file-can-be-up-to-d-bytes', '##The size of the uploaded file can be up to %d bytes.');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(204, 'en', 'the-size-of-the-uploaded-file-can-be-up-to-d-bytes', '##The size of the uploaded file can be up to %d bytes.');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(205, 'cs', 'form_agree', '##form_agree');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(206, 'en', 'form_agree', '##form_agree');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(207, 'cs', 'form_agree_text', '##form_agree_text');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(208, 'en', 'form_agree_text', '##form_agree_text');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(209, 'cs', 'form_btn_send', 'Odeslat formulář');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(210, 'en', 'form_btn_send', '##form_btn_send');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(211, 'cs', 'message_antispam_error', '##messageAntispamError');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(212, 'en', 'message_antispam_error', '##messageAntispamError');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(213, 'cs', 'antispam_error', '##antispam_error');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(214, 'en', 'antispam_error', '##antispam_error');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(215, 'cs', 'error_voucher_bad_using_min_order', 'Slevový poukaz nelze uplatnit, minimální hodnota nákupu pro uplatnění slevy je %minOrderPrice%');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(216, 'cs', 'server_error_500_msg', 'Na našem serveru došlo k neočekávané chybě.<br>Mějte s námi strpení a zkuste to, prosím, znovu.');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(217, 'en', 'server_error_500_msg', 'We\'re sorry! The server encountered an internal error and was unable to complete your request. <br>Please try again later.');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(218, 'cs', 'title_personal_info', 'Osobní údaje');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(219, 'en', 'title_personal_info', '##title_personal_info');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(220, 'cs', '420', '##+420');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(221, 'en', '420', '##+420');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(222, 'cs', '421', '##+421');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(223, 'en', '421', '##+421');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(224, 'cs', '43', '##+43');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(225, 'en', '43', '##+43');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(226, 'cs', '49', '##+49');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(227, 'en', '49', '##+49');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(228, 'cs', '48', '##+48');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(229, 'en', '48', '##+48');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(230, 'cs', '39', '##+39');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(231, 'en', '39', '##+39');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(232, 'cs', '31', '##+31');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(233, 'en', '31', '##+31');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(234, 'cs', '44', '##+44');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(235, 'en', '44', '##+44');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(236, 'cs', '32', '##+32');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(237, 'en', '32', '##+32');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(238, 'cs', '385', '##+385');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(239, 'en', '385', '##+385');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(240, 'cs', '45', '##+45');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(241, 'en', '45', '##+45');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(242, 'cs', '372', '##+372');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(243, 'en', '372', '##+372');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(244, 'cs', '33', '##+33');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(245, 'en', '33', '##+33');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(246, 'cs', '353', '##+353');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(247, 'en', '353', '##+353');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(248, 'cs', '370', '##+370');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(249, 'en', '370', '##+370');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(250, 'cs', '371', '##+371');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(251, 'en', '371', '##+371');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(252, 'cs', '352', '##+352');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(253, 'en', '352', '##+352');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(254, 'cs', '36', '##+36');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(255, 'en', '36', '##+36');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(256, 'cs', '40', '##+40');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(257, 'en', '40', '##+40');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(258, 'cs', '386', '##+386');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(259, 'en', '386', '##+386');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(260, 'cs', '34', '##+34');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(261, 'en', '34', '##+34');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(262, 'cs', 'street', '##street');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(263, 'en', 'street', '##street');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(264, 'cs', 'city', '##city');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(265, 'en', 'city', '##city');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(266, 'cs', 'zip', '##zip');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(267, 'en', 'zip', '##zip');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(268, 'cs', 'title_invoice_address', 'Fakturační adresa');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(269, 'en', 'title_invoice_address', '##title_invoice_address');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(270, 'cs', 'state', '##state');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(271, 'en', 'state', '##state');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(272, 'cs', 'ceska-republika', '##Česká republika');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(273, 'en', 'ceska-republika', '##Česká republika');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(274, 'cs', 'slovensko', '##Slovensko');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(275, 'en', 'slovensko', '##Slovensko');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(276, 'cs', 'rakousko', '##Rakousko');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(277, 'en', 'rakousko', '##Rakousko');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(278, 'cs', 'nemecko', '##Německo');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(279, 'en', 'nemecko', '##Německo');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(280, 'cs', 'polsko', '##Polsko');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(281, 'en', 'polsko', '##Polsko');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(282, 'cs', 'italie', '##Itálie');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(283, 'en', 'italie', '##Itálie');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(284, 'cs', 'holandsko', '##Holandsko');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(285, 'en', 'holandsko', '##Holandsko');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(286, 'cs', 'anglie', '##Anglie');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(287, 'en', 'anglie', '##Anglie');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(288, 'cs', 'belgie', '##Belgie');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(289, 'en', 'belgie', '##Belgie');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(290, 'cs', 'chorvatsko', '##Chorvatsko');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(291, 'en', 'chorvatsko', '##Chorvatsko');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(292, 'cs', 'dansko', '##Dánsko');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(293, 'en', 'dansko', 'Test');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(294, 'cs', 'estonsko', '##Estonsko');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(295, 'en', 'estonsko', '##Estonsko');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(296, 'cs', 'francie', '##Francie');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(297, 'en', 'francie', '##Francie');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(298, 'cs', 'irsko', '##Irsko');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(299, 'en', 'irsko', '##Irsko');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(300, 'cs', 'litva', '##Litva');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(301, 'en', 'litva', '##Litva');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(302, 'cs', 'lotyssko', '##Lotyšsko');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(303, 'en', 'lotyssko', '##Lotyšsko');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(304, 'cs', 'lucembursko', '##Lucembursko');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(305, 'en', 'lucembursko', '##Lucembursko');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(306, 'cs', 'madarsko', '##Maďarsko');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(307, 'en', 'madarsko', '##Maďarsko');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(308, 'cs', 'rumunsko', '##Rumunsko');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(309, 'en', 'rumunsko', '##Rumunsko');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(310, 'cs', 'slovinsko', '##Slovinsko');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(311, 'en', 'slovinsko', '##Slovinsko');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(312, 'cs', 'spanelsko', '##Španělsko');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(313, 'en', 'spanelsko', '##Španělsko');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(314, 'cs', 'company', '##company');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(315, 'en', 'company', '##company');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(316, 'cs', 'ic', '##ic');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(317, 'en', 'ic', '##ic');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(320, 'cs', 'title_delivery_address', 'Dodací adresa');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(321, 'en', 'title_delivery_address', '##title_delivery_address');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(322, 'cs', 'add_address', 'Přidat další adresu');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(323, 'en', 'add_address', '##add_address');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(324, 'cs', 'newsletter', '##newsletter');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(325, 'en', 'newsletter', '##newsletter');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(326, 'cs', 'btn_change', 'Uložit změny');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(327, 'en', 'btn_change', '##btn_change');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(328, 'cs', 'title_password_change', 'Změnit heslo');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(329, 'en', 'title_password_change', '##title_password_change');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(332, 'cs', 'form_password_not_same', 'Hesla se neshodují.');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(333, 'en', 'form_password_not_same', '##form_password_not_same');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(334, 'cs', 'btn_change_password', 'Změnit heslo');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(335, 'en', 'btn_change_password', '##btn_change_password');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7573, 'cs', 'form_label_name', 'Jméno');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7574, 'en', 'form_label_name', '##form_label_name');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7575, 'cs', 'form_label_surname', 'Příjmení');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7576, 'en', 'form_label_surname', '##form_label_surname');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7577, 'cs', 'form_label_email', 'E-mail');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7578, 'en', 'form_label_email', '##form_label_email');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7579, 'cs', 'form_label_phone', 'Telefon');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7580, 'en', 'form_label_phone', '##form_label_phone');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7581, 'cs', 'form_label_text', 'Zpráva');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7582, 'en', 'form_label_text', '##form_label_text');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7583, 'cs', 'form_label_file', 'Soubor');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7584, 'en', 'form_label_file', '##form_label_file');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7585, 'cs', 'form_label_password', 'Heslo');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7586, 'en', 'form_label_password', '##form_label_password');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7587, 'cs', 'user_logout', '##user_logout');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7588, 'en', 'user_logout', '##user_logout');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7589, 'cs', 'day_5', 'pátek');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7590, 'en', 'day_5', '##day_5');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7591, 'cs', 'day_1', 'pondělí');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7592, 'en', 'day_1', '##day_1');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7593, 'cs', 'form_label_message', 'Zpráva');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7594, 'en', 'form_label_message', '##form_label_message');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7595, 'cs', 'free_delivery_treshold', '##free_delivery_treshold');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7596, 'en', 'free_delivery_treshold', '##free_delivery_treshold');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7597, 'cs', 'price_not_determined', '##price_not_determined');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7598, 'en', 'price_not_determined', '##price_not_determined');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7599, 'cs', 'availability_soldout', 'Vyprodáno');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7600, 'en', 'availability_soldout', '##availability_soldout');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7601, 'cs', 'btn_watch_availability', 'Sledovat dostupnost');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7602, 'en', 'btn_watch_availability', '##btn_watch_availability');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7603, 'cs', 'form_label_agree_text', 'Souhlasím se');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7604, 'en', 'form_label_agree_text', '##form_label_agree_text');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7605, 'cs', 'form_label_agree_link', 'zpracováním osobních údajů');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7606, 'en', 'form_label_agree_link', '##form_label_agree_link');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7607, 'cs', 'empty_filter_title', '##empty_filter_title');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7608, 'en', 'empty_filter_title', '##empty_filter_title');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7609, 'cs', 'title_files', 'Soubory ke stažení');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7610, 'en', 'title_files', '##title_files');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7611, 'cs', 'title_links', '##title_links');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7612, 'en', 'title_links', '##title_links');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7613, 'cs', 'external_link', '##external_link');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7614, 'en', 'external_link', '##external_link');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7615, 'cs', 'title_images', '##title_images');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7616, 'en', 'title_images', '##title_images');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7617, 'cs', 'title_videos', 'Videa');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7618, 'en', 'title_videos', '##title_videos');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7619, 'cs', 'basket_product_title', '##basket_product_title');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7620, 'en', 'basket_product_title', '##basket_product_title');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7621, 'cs', 'price_sum', '##price_sum');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7622, 'en', 'price_sum', '##price_sum');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7623, 'cs', 'continue', '##continue');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7624, 'en', 'continue', '##continue');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7625, 'cs', 'btn_basket', '##btn_basket');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7626, 'en', 'btn_basket', '##btn_basket');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7627, 'cs', 'title_prebasket', '##title_prebasket');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7628, 'en', 'title_prebasket', '##title_prebasket');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7629, 'cs', 'basket_added', '##basket_added');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7630, 'en', 'basket_added', '##basket_added');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7631, 'cs', 'btn_continue_shopping', '##btn_continue_shopping');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7632, 'en', 'btn_continue_shopping', '##btn_continue_shopping');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7633, 'cs', 'btn_enter_basket', '##btn_enter_basket');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7634, 'en', 'btn_enter_basket', '##btn_enter_basket');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7635, 'cs', 'shopping_basket', 'Nákupní košík');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7636, 'en', 'shopping_basket', '##shopping_basket');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7637, 'cs', 'shipping_payment', 'Doprava a platba');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7638, 'en', 'shipping_payment', '##shipping_payment');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7639, 'cs', 'personal_info', 'Doručovací údaje');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7640, 'en', 'personal_info', '##personal_info');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7641, 'cs', 'order_success', 'Odeslání');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7642, 'en', 'order_success', '##order_success');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7643, 'cs', 'title_product_buy', '##title_product_buy');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7644, 'en', 'title_product_buy', '##title_product_buy');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7645, 'cs', 'availability', 'Dostupnost');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7646, 'en', 'availability', '##availability');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7647, 'cs', 'amount', '##amount');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7648, 'en', 'amount', '##amount');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7649, 'cs', 'unit_price_vat', '##unit_price_vat');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7650, 'en', 'unit_price_vat', '##unit_price_vat');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7651, 'cs', 'total_price_vat', 'Cena celkem');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7652, 'en', 'total_price_vat', '##total_price_vat');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7653, 'cs', 'not_available', 'Nedostupný');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7654, 'en', 'not_available', '##not_available');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7655, 'cs', 'title_voucher', '##title_voucher');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7656, 'en', 'title_voucher', '##title_voucher');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7657, 'cs', 'title_have_coupon', '##title_have_coupon');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7658, 'en', 'title_have_coupon', '##title_have_coupon');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7659, 'cs', 'title_coupon', '##title_coupon');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7660, 'en', 'title_coupon', '##title_coupon');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7661, 'cs', 'use', '##use');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7662, 'en', 'use', '##use');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7663, 'cs', 'total_price', 'Cena celkem bez DPH');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7664, 'en', 'total_price', '##total_price');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7665, 'cs', 'btn_back_to_eshop', '##btn_back_to_eshop');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7666, 'en', 'btn_back_to_eshop', '##btn_back_to_eshop');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7667, 'cs', 'continue2', '##continue2');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7668, 'en', 'continue2', '##continue2');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7669, 'cs', 'btn_order_in_eshop', '##btn_order_in_eshop');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7670, 'en', 'btn_order_in_eshop', '##btn_order_in_eshop');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7671, 'cs', 'title_remove_from_basket', '##title_remove_from_basket');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7672, 'en', 'title_remove_from_basket', '##title_remove_from_basket');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7673, 'cs', 'btn_cancel_delete', '##btn_cancel_delete');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7674, 'en', 'btn_cancel_delete', '##btn_cancel_delete');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7675, 'cs', 'btn_allow_delete', '##btn_allow_delete');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7676, 'en', 'btn_allow_delete', '##btn_allow_delete');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7677, 'cs', 'title_transport_form', '##title_transport_form');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7678, 'en', 'title_transport_form', '##title_transport_form');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7679, 'cs', 'delivery_expected', '##delivery_expected');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7680, 'en', 'delivery_expected', '##delivery_expected');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7681, 'cs', 'filter_title_price', '##filter_title_price');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7682, 'en', 'filter_title_price', '##filter_title_price');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7683, 'cs', 'free', 'zdarma');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7684, 'en', 'free', '##free');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7685, 'cs', 'title_payment_form', '##title_payment_form');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7686, 'en', 'title_payment_form', '##title_payment_form');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7687, 'cs', 'opening_hours', '##opening_hours');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7688, 'en', 'opening_hours', '##opening_hours');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7689, 'cs', 'btn_back', 'Zpět');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7690, 'en', 'btn_back', '##btn_back');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7691, 'cs', 'order_sum_products_title', 'Shrnutí objednávky');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7692, 'en', 'order_sum_products_title', '##order_sum_products_title');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7693, 'cs', 'ppl_modal_desc', '##ppl_modal_desc');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7694, 'en', 'ppl_modal_desc', '##ppl_modal_desc');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7695, 'cs', 'close_window', '##close_window');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7696, 'en', 'close_window', '##close_window');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7697, 'cs', 'post_modal_desc', '##post_modal_desc');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7698, 'en', 'post_modal_desc', '##post_modal_desc');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7699, 'cs', 'personal_modal_desc', '##personal_modal_desc');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7700, 'en', 'personal_modal_desc', '##personal_modal_desc');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7701, 'cs', 'on_delivery_modal_desc', '##onDelivery_modal_desc');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7702, 'en', 'on_delivery_modal_desc', '##onDelivery_modal_desc');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7703, 'cs', 'online_modal_desc', '##online_modal_desc');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7704, 'en', 'online_modal_desc', '##online_modal_desc');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7705, 'cs', 'cash_modal_desc', '##cash_modal_desc');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7706, 'en', 'cash_modal_desc', '##cash_modal_desc');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7707, 'cs', 'choose_billing_info', '##choose_billing_info');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7708, 'en', 'choose_billing_info', '##choose_billing_info');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7709, 'cs', 'form_label_street', 'Ulice');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7710, 'en', 'form_label_street', '##form_label_street');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7711, 'cs', 'form_label_city', 'Město');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7712, 'en', 'form_label_city', '##form_label_city');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7713, 'cs', 'form_label_zip', 'PSČ');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7714, 'en', 'form_label_zip', '##form_label_zip');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7715, 'cs', 'note', '##note');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7716, 'en', 'note', '##note');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7717, 'cs', 'title_company_form', 'Vyplnit firemní údaje');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7718, 'en', 'title_company_form', '##title_company_form');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7719, 'cs', 'form_label_company', 'Firma');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7720, 'en', 'form_label_company', '##form_label_company');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7721, 'cs', 'form_label_ic', 'IČ');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7722, 'en', 'form_label_ic', '##form_label_ic');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7723, 'cs', 'form_label_dic', 'DIČ');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7724, 'en', 'form_label_dic', '##form_label_dic');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7725, 'cs', 'title_delivery_form', 'Jiná dodací adresa');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7726, 'en', 'title_delivery_form', '##title_delivery_form');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7727, 'cs', 'form_label_state', 'Stát');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7728, 'en', 'form_label_state', '##form_label_state');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7729, 'cs', 'form_label_info', '##form_label_info');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7730, 'en', 'form_label_info', '##form_label_info');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7731, 'cs', 'title_questionary_form', '##title_questionary_form');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7732, 'en', 'title_questionary_form', '##title_questionary_form');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7733, 'cs', 'order_agree_1', '##order_agree_1');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7734, 'en', 'order_agree_1', '##order_agree_1');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7735, 'cs', 'order_agree_2', '##order_agree_2');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7736, 'en', 'order_agree_2', '##order_agree_2');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7737, 'cs', 'order_agree_3', '##order_agree_3');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7738, 'en', 'order_agree_3', '##order_agree_3');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7739, 'cs', 'order_agree_4', '##order_agree_4');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7740, 'en', 'order_agree_4', '##order_agree_4');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7741, 'cs', 'btn_send_order', 'Odeslat objednávku');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7742, 'en', 'btn_send_order', '##btn_send_order');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7743, 'cs', 'delivery', 'Doprava');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7744, 'en', 'delivery', '##delivery');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7745, 'cs', 'payment', 'Platba');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7746, 'en', 'payment', '##payment');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7747, 'cs', 'claim', '##claim');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7748, 'en', 'claim', '##claim');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7749, 'cs', 'footer_copyright', '##footer_copyright');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7750, 'en', 'footer_copyright', '##footer_copyright');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7751, 'cs', 'na-dotaz', '##Na dotaz');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7752, 'en', 'na-dotaz', '##Na dotaz');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7753, 'cs', 'order_sum_title', '##orderSumTitle');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7754, 'en', 'order_sum_title', '##orderSumTitle');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7755, 'cs', 'title_personal_recap', '##title_personal_recap');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7756, 'en', 'title_personal_recap', '##title_personal_recap');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7757, 'cs', 'address', '##address');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7758, 'en', 'address', '##address');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7759, 'cs', 'czech', '##czech');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7760, 'en', 'czech', '##czech');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7761, 'cs', 'title_order_number', '##title_order_number');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7762, 'en', 'title_order_number', '##title_order_number');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7763, 'cs', 'btn_watch_order', '##btn_watch_order');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7764, 'en', 'btn_watch_order', '##btn_watch_order');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7765, 'cs', 'title_order_social_1', '##title_order_social_1');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7766, 'en', 'title_order_social_1', '##title_order_social_1');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7767, 'cs', 'title_order_social_2', '##title_order_social_2');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7768, 'en', 'title_order_social_2', '##title_order_social_2');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7769, 'cs', 'please-enter-no-more-than-d-characters', '##Please enter no more than %d characters.');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7770, 'en', 'please-enter-no-more-than-d-characters', '##Please enter no more than %d characters.');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7771, 'cs', 'remove_address', '##remove_address');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7772, 'en', 'remove_address', '##remove_address');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7775, 'cs', 'form_label_newsletter', 'Souhlasím s odběrem newsletteru');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7776, 'en', 'form_label_newsletter', '##form_label_newsletter');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7777, 'cs', 'form_label_password2', 'Heslo znovu');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7778, 'en', 'form_label_password2', '##form_label_password2');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7779, 'cs', 'e-mail-is-required', '##E-mail is required');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7780, 'en', 'e-mail-is-required', '##E-mail is required');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7781, 'cs', 'registration_agree', 'Registrací souhlasíte se');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7782, 'en', 'registration_agree', '##registration_agree');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7783, 'cs', 'sign_link', '##sign_link');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7784, 'en', 'sign_link', '##sign_link');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7785, 'cs', 'btn_register', 'Registrovat');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7786, 'en', 'btn_register', '##btn_register');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7787, 'cs', 'title_registration', '##title_registration');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7788, 'en', 'title_registration', '##title_registration');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7789, 'cs', 'login_sale', '##login_sale');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7790, 'en', 'login_sale', '##login_sale');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7791, 'cs', 'login_news', '##login_news');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7792, 'en', 'login_news', '##login_news');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7793, 'cs', 'login_shipping', '##login_shipping');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7794, 'en', 'login_shipping', '##login_shipping');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7795, 'cs', 'login_summary', '##login_summary');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7796, 'en', 'login_summary', '##login_summary');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7797, 'cs', 'login_better', '##login_better');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7798, 'en', 'login_better', '##login_better');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7803, 'cs', 'form_profil_ok', 'Změny byly uloženy.');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7804, 'en', 'form_profil_ok', '##form_profil_ok');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7805, 'cs', 'registration_email', 'E-mail');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7806, 'en', 'registration_email', '##registration_email');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7807, 'cs', 'btn_lost_pwd', 'Odeslat');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7808, 'en', 'btn_lost_pwd', '##btn_lost_pwd');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7809, 'cs', 'registration_agree_personal_data_link', 'zpracováním osobních údajů');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7810, 'en', 'registration_agree_personal_data_link', '##registration_agree_personal_data_link');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7811, 'cs', 'form_send_reset_password', 'Na Váš e-mail byl odeslán odkaz pro nastavení nového hesla.');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7812, 'en', 'form_send_reset_password', '##form_send_reset_password');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7813, 'cs', 'btn_save', 'Uložit');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7814, 'en', 'btn_save', '##btn_save');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7815, 'cs', 'btn_create_account', '##btn_create_account');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7816, 'en', 'btn_create_account', '##btn_create_account');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7817, 'cs', 'filter_dial_select', '##filter_dial_select');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7818, 'en', 'filter_dial_select', '##filter_dial_select');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7819, 'cs', 'pname_113', 'Select CZ');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7820, 'en', 'pname_113', 'Select');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7821, 'cs', 'pvalue_6564', 'Hodnota 1');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7822, 'en', 'pvalue_6564', 'Hodnota 1');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7823, 'cs', 'pvalue_6565', 'Hodnota 2');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7824, 'en', 'pvalue_6565', 'Hodnota 2');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7825, 'cs', 'pvalue_6566', 'Hodnota 3');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7826, 'en', 'pvalue_6566', 'Hodnota 3');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7827, 'cs', 'filter_btn_remove', '##filter_btn_remove');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7828, 'en', 'filter_btn_remove', '##filter_btn_remove');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7829, 'cs', 'filter_cancel', '##filter_cancel');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7830, 'en', 'filter_cancel', '##filter_cancel');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7831, 'cs', 'pvalue_alias_6564', 'hodnota-1');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7832, 'en', 'pvalue_alias_6564', 'hodnota-1');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7833, 'cs', 'pvalue_alias_6565', 'hodnota-2');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7834, 'en', 'pvalue_alias_6565', 'hodnota-2');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7835, 'cs', 'pvalue_alias_6566', 'hodnota-3');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7836, 'en', 'pvalue_alias_6566', 'hodnota-3');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7837, 'cs', 'pvalue_6567', 'Hodnota 1');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7838, 'en', 'pvalue_6567', 'Hodnota 1');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7839, 'cs', 'pvalue_alias_6567', 'hodnota-1');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7840, 'en', 'pvalue_alias_6567', 'hodnota-1');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7841, 'cs', 'pvalue_6570', 'Hodnota 2');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7842, 'en', 'pvalue_6570', 'Hodnota 2');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7843, 'cs', 'pvalue_alias_6570', 'hodnota-2');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7844, 'en', 'pvalue_alias_6570', 'hodnota-2');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7845, 'cs', 'pvalue_6571', 'Hodnota 3');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7846, 'en', 'pvalue_6571', 'Hodnota 3');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7847, 'cs', 'pvalue_alias_6571', 'hodnota-3');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7848, 'en', 'pvalue_alias_6571', 'hodnota-3');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7849, 'cs', 'pname_114', 'Multiselect');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7850, 'en', 'pname_114', 'Multiselect');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7851, 'cs', 'catalog_seo_filter_and', 'a');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7852, 'en', 'catalog_seo_filter_and', 'and');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7853, 'cs', 'pname_tooltip_113', 'Popisek');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7854, 'en', 'pname_tooltip_113', '');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7855, 'cs', 'pvalue_filter_6564', 'Hodnota 1 (Název ve filtru)');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7856, 'en', 'pvalue_filter_6564', '');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7857, 'cs', 'pname_119', 'Číslo');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7858, 'en', 'pname_119', 'Číslo');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7859, 'cs', 'pname_tooltip_119', '_');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7860, 'en', 'pname_tooltip_119', '_');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7861, 'cs', 'pname_unit_119', 'Px');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7862, 'en', 'pname_unit_119', '');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7863, 'cs', 'bad_login', 'Neplatný e-mail a/nebo heslo.');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7864, 'en', 'bad_login', '##bad_login');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7865, 'cs', 'pname_unit_113', '');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7866, 'en', 'pname_unit_113', '');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7867, 'cs', 'mainbasket_empty_title', '##mainbasket_empty_title');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7868, 'en', 'mainbasket_empty_title', '##mainbasket_empty_title');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7869, 'cs', 'mainbasket_empty_text', '##mainbasket_empty_text');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7870, 'en', 'mainbasket_empty_text', '##mainbasket_empty_text');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7871, 'cs', 'mainbasket_empty_look', '##mainbasket_empty_look');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7872, 'en', 'mainbasket_empty_look', '##mainbasket_empty_look');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7873, 'cs', 'eshop', '##eshop');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7874, 'en', 'eshop', '##eshop');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7875, 'cs', 'title_last_blog_article', '##title_last_blog_article');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7876, 'en', 'title_last_blog_article', '##title_last_blog_article');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7877, 'cs', 'mail_exist_register', '##mail_exist_register');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7878, 'en', 'mail_exist_register', '##mail_exist_register');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7879, 'cs', 'flag_price_final_dph', '##flag_priceFinalDPH');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7880, 'en', 'flag_price_final_dph', '##flag_priceFinalDPH');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7881, 'cs', 'pvalue_filter_6565', '');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7882, 'en', 'pvalue_filter_6565', '');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7883, 'cs', 'pvalue_filter_6566', '');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7884, 'en', 'pvalue_filter_6566', '');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7885, 'cs', 'yes', '##yes');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7886, 'en', 'yes', '##yes');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7887, 'cs', 'message_error_step1', '##messageErrorStep1');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7888, 'en', 'message_error_step1', '##messageErrorStep1');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7889, 'cs', 'message_error_bad_combinaton', '##messageErrorBadCombinaton');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7890, 'en', 'message_error_bad_combinaton', '##messageErrorBadCombinaton');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7891, 'cs', 'germany', '##germany');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7892, 'en', 'germany', '##germany');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7893, 'cs', 'company_title', '##companyTitle');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7894, 'en', 'company_title', '##companyTitle');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7895, 'cs', 'dic', '##dic');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7896, 'en', 'dic', '##dic');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7897, 'cs', 'delivery_title', '##deliveryTitle');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7898, 'en', 'delivery_title', '##deliveryTitle');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7899, 'cs', 'title_company_recap', '##title_company_recap');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7900, 'en', 'title_company_recap', '##title_company_recap');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7901, 'cs', 'title_delivery_recap', '##title_delivery_recap');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7902, 'en', 'title_delivery_recap', '##title_delivery_recap');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7903, 'cs', 'error', '##error');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7904, 'en', 'error', '##error');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7905, 'cs', 'form_password_empty', '##form_password_empty');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7906, 'en', 'form_password_empty', '##form_password_empty');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7907, 'cs', 'error_voucher_bad', '##error_voucher_bad');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7908, 'en', 'error_voucher_bad', '##error_voucher_bad');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7909, 'cs', 'form_label_firstname', 'Jméno');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7910, 'en', 'form_label_firstname', '##form_label_firstname');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7911, 'cs', 'form_label_lastname', 'Příjmení');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7912, 'en', 'form_label_lastname', '##form_label_lastname');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7913, 'cs', 'availability_on_request', 'Na dotaz');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7914, 'en', 'availability_on_request', '##availability_on_request');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7915, 'cs', 'stock_over_10', 'Více než 10');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7916, 'en', 'stock_over_10', '##stock_over_10');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7917, 'cs', 'delivery_date_tomorrow', 'zítra');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7918, 'en', 'delivery_date_tomorrow', '##delivery_date_tomorrow');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7919, 'cs', 'delivery_date_at_yours', 'u vás');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7920, 'en', 'delivery_date_at_yours', '##delivery_date_at_yours');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7921, 'cs', 'form_profile_password_changed', 'Vaše heslo bylo změněno.');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7922, 'en', 'form_profile_password_changed', '##form_profile_password_changed');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7923, 'cs', 'delivery_date_today', 'již dnes');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7924, 'en', 'delivery_date_today', '##delivery_date_today');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7925, 'cs', 'order_status_progress', '##order_status_progress');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7926, 'en', 'order_status_progress', '##order_status_progress');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7927, 'cs', 'pvalue_filter_6567', '');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7928, 'en', 'pvalue_filter_6567', '');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7929, 'cs', 'pvalue_filter_6570', '');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7930, 'en', 'pvalue_filter_6570', '');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7931, 'cs', 'pvalue_filter_6571', '');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7932, 'en', 'pvalue_filter_6571', '');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7933, 'cs', 'pname_tooltip_114', '');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7934, 'en', 'pname_tooltip_114', '');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7935, 'cs', 'pname_unit_114', '');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7936, 'en', 'pname_unit_114', '');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7937, 'cs', 'title_company_info', '##title_company_info');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7938, 'en', 'title_company_info', '##title_company_info');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7939, 'cs', 'order_status_new', '##order_status_new');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7940, 'en', 'order_status_new', '##order_status_new');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7941, 'cs', 'form_reset_password', 'Vaše heslo bylo nastaveno.');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7942, 'cs', 'reset_password_expired_link', '##reset_password_expired_link');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(7943, 'en', 'reset_password_expired_link', '##reset_password_expired_link');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8346, 'cs', 'availability_in_stock', 'Skladem');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8347, 'en', 'availability_in_stock', '##availability_in_stock');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8348, 'cs', 'stock_name_alias_shop', 'Na prodejně');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8349, 'en', 'stock_name_alias_shop', '##stock_name_alias_shop');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8350, 'cs', 'stock_name_alias_supplier_store', 'U dodavatele');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8351, 'en', 'stock_name_alias_supplier_store', '##stock_name_alias_supplier_store');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8352, 'cs', 'title_delivery_info', 'Doručení');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8353, 'en', 'title_delivery_info', '##title_delivery_info');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8354, 'cs', 'stock_piece', 'ks');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8355, 'en', 'stock_piece', '##stock_piece');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8356, 'cs', 'btn_remove_address', '##btn_remove_address');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8357, 'en', 'btn_remove_address', '##btn_remove_address');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8358, 'cs', 'delivery_date_at', 'v');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8359, 'en', 'delivery_date_at', '##delivery_date_at');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8360, 'cs', 'stock_over_2', 'Více než 2');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8361, 'en', 'stock_over_2', '##stock_over_2');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8362, 'cs', 'stock_over_5', 'Více než 5');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8363, 'en', 'stock_over_5', '##stock_over_5');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8364, 'cs', 'filter_range_price', 'Cena');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8365, 'en', 'filter_range_price', '##filter_range_price');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8366, 'cs', 'price', '##price');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8367, 'en', 'price', '##price');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8416, 'cs', 'pvalue_6572', '10');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8417, 'en', 'pvalue_6572', '10');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8418, 'cs', 'pvalue_alias_6572', '6572');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8419, 'en', 'pvalue_alias_6572', '6572');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8420, 'cs', 'pvalue_filter_6572', '');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8421, 'en', 'pvalue_filter_6572', '');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8422, 'cs', 'pvalue_6573', '20');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8423, 'en', 'pvalue_6573', '20');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8424, 'cs', 'pvalue_alias_6573', '6573');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8425, 'en', 'pvalue_alias_6573', '6573');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8426, 'cs', 'pvalue_filter_6573', '');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8427, 'en', 'pvalue_filter_6573', '');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8428, 'cs', 'pvalue_6574', '88');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8429, 'en', 'pvalue_6574', '88');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8430, 'cs', 'pvalue_alias_6574', '6574');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8431, 'en', 'pvalue_alias_6574', '6574');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8432, 'cs', 'pvalue_filter_6574', '');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8433, 'en', 'pvalue_filter_6574', '');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8434, 'cs', 'pvalue_6575', '120');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8435, 'en', 'pvalue_6575', '120');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8436, 'cs', 'pvalue_alias_6575', '6575');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8437, 'en', 'pvalue_alias_6575', '6575');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8438, 'cs', 'pvalue_filter_6575', '');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8439, 'en', 'pvalue_filter_6575', '');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8440, 'cs', 'pvalue_6576', '2');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8441, 'en', 'pvalue_6576', '2');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8442, 'cs', 'pvalue_alias_6576', '6576');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8443, 'en', 'pvalue_alias_6576', '6576');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8444, 'cs', 'pvalue_filter_6576', '');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8445, 'en', 'pvalue_filter_6576', '');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8482, 'cs', 'filter_dial_cislo', '##filter_dial_cislo');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8483, 'en', 'filter_dial_cislo', '##filter_dial_cislo');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8484, 'cs', 'filter_dial_multiselect', '##filter_dial_multiselect');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8485, 'en', 'filter_dial_multiselect', '##filter_dial_multiselect');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8486, 'cs', 'help', '##help');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8487, 'en', 'help', '##help');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8584, 'cs', 'filter_range_cislo', '##filter_range_cislo');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8585, 'en', 'filter_range_cislo', '##filter_range_cislo');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8586, 'cs', 'od', '##od');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8587, 'en', 'od', '##od');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8588, 'cs', 'message_bad_login', '##message_bad_login');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8589, 'en', 'message_bad_login', '##message_bad_login');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8590, 'cs', 'message_ok_login', '##message_ok_login');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8591, 'en', 'message_ok_login', '##message_ok_login');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8592, 'cs', 'form_label_add_address', 'Přidat adresu');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8593, 'en', 'form_label_add_address', '##form_label_add_address');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8594, 'cs', 'form_label_remove_address', 'Odebrat adresu');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8595, 'en', 'form_label_remove_address', '##form_label_remove_address');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8596, 'cs', 'delivery_date_at_inf', 've');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8597, 'en', 'delivery_date_at_inf', '##delivery_date_at_inf');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8598, 'cs', 'attached_pages_title', '##attached_pages_title');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8599, 'en', 'attached_pages_title', '##attached_pages_title');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8600, 'cs', 'title_product_main_category', 'Hlavní kategorie');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8601, 'en', 'title_product_main_category', '##title_product_main_category');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8602, 'cs', 'title_product_all_variants', 'Ostatní varianty');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8603, 'en', 'title_product_all_variants', '##title_product_all_variants');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8604, 'cs', 'stock_two_pieces', 'Poslední 2 kusy');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8605, 'en', 'stock_two_pieces', '##stock_two_pieces');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8630, 'cs', 'pname_116', 'pname_116');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8631, 'en', 'pname_116', 'pname_116');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8632, 'cs', 'pname_tooltip_116', 'pname_tooltip_116');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8633, 'en', 'pname_tooltip_116', 'pname_tooltip_116');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8634, 'cs', 'pname_unit_116', 'pname_unit_116');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8635, 'en', 'pname_unit_116', 'pname_unit_116');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8696, 'cs', 'product_ean', 'EAN');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8697, 'en', 'product_ean', '##product_ean');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8698, 'cs', 'product_code', 'Kód produktu');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8699, 'en', 'product_code', '##product_code');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8700, 'cs', 'filter_flag_is_new_unit', '##filter_flag_isNew_unit');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8701, 'en', 'filter_flag_is_new_unit', '##filter_flag_isNew_unit');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8702, 'cs', 'filter_flag_is_new_description', '##filter_flag_isNew_description');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8703, 'en', 'filter_flag_is_new_description', '##filter_flag_isNew_description');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8704, 'cs', 'filter_flag_is_old_unit', '##filter_flag_isOld_unit');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8705, 'en', 'filter_flag_is_old_unit', '##filter_flag_isOld_unit');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8706, 'cs', 'filter_flag_is_old_description', '##filter_flag_isOld_description');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8707, 'en', 'filter_flag_is_old_description', '##filter_flag_isOld_description');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8710, 'cs', 'filter_range_price_unit', 'v Kč');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8711, 'en', 'filter_range_price_unit', '##filter_range_price_unit');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8712, 'cs', 'filter_range_price_description', 'Zvolte rozmezí ceny hledaného produktu');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8714, 'en', 'filter_range_price_description', '##filter_range_price_description');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8716, 'cs', 'btn_more_values', '##btn_more_values');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8717, 'en', 'btn_more_values', '##btn_more_values');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8718, 'cs', 'btn_filter', 'Filtrovat');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8719, 'en', 'btn_filter', '##btn_filter');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8722, 'cs', 'sort_cheapest', '##sort_cheapest');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8724, 'en', 'sort_cheapest', '##sort_cheapest');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8726, 'cs', 'sort_name', '##sort_name');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8728, 'en', 'sort_name', '##sort_name');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8730, 'cs', 'btn_add_to_basket', 'Vložit do košíku');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8732, 'en', 'btn_add_to_basket', '##btn_add_to_basket');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8734, 'cs', 'btn_filter_remove', 'Resetovat celý filtr');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8735, 'en', 'btn_filter_remove', '##btn_filter_remove');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8736, 'cs', 'delivery_info_address', '##delivery_info_address');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8737, 'en', 'delivery_info_address', '##delivery_info_address');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8738, 'cs', 'btn_filter_cancel', '##btn_filter_cancel');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8739, 'en', 'btn_filter_cancel', '##btn_filter_cancel');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8740, 'cs', 'paging_next', '##paging_next');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8741, 'en', 'paging_next', '##paging_next');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8742, 'cs', 'showing', '##showing');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8743, 'en', 'showing', '##showing');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8744, 'cs', 'of', '##of');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8745, 'en', 'of', '##of');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8746, 'cs', 'show_more_products', '##show_more_products');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8747, 'en', 'show_more_products', '##show_more_products');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8748, 'cs', 'pvalue_6580', '##1');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8749, 'en', 'pvalue_6580', '##1');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8750, 'cs', 'pvalue_alias_6580', '##6580');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8751, 'en', 'pvalue_alias_6580', '##6580');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8752, 'cs', 'pvalue_filter_6580', '##');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8753, 'en', 'pvalue_filter_6580', '##');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8754, 'cs', 'to', '##to');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8755, 'en', 'to', '##to');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8756, 'cs', 'pname_unit_117', '##');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8757, 'en', 'pname_unit_117', '##');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8758, 'cs', 'pname_unit_118', '##');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8759, 'en', 'pname_unit_118', '##');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8760, 'cs', 'pname_unit_120', '##');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8761, 'en', 'pname_unit_120', '##');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8762, 'cs', 'pname_tooltip_120', '##');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8763, 'en', 'pname_tooltip_120', '##');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8764, 'cs', 'pname_120', '##Wysiwyg');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8765, 'en', 'pname_120', '##Wysiwyg');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8766, 'cs', 'pname_tooltip_118', '##');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8767, 'en', 'pname_tooltip_118', '##');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8768, 'cs', 'pname_118', '##Textarea');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8769, 'en', 'pname_118', '##Textarea');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8770, 'cs', 'pname_tooltip_117', '##');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8771, 'en', 'pname_tooltip_117', '##');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8772, 'cs', 'pname_117', '##Text');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8773, 'en', 'pname_117', '##Text');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8774, 'cs', 'btn_show_more_products', '##btn_show_more_products');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8775, 'en', 'btn_show_more_products', '##btn_show_more_products');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8776, 'cs', 'pname_1', '##Parametry');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8777, 'en', 'pname_1', '##Parametry');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8778, 'cs', 'pname_tooltip_1', '##');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8779, 'en', 'pname_tooltip_1', '##');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8780, 'cs', 'pname_121', 'Kategorie zbraně');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8781, 'en', 'pname_121', 'Enter a new name');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8782, 'cs', 'pname_tooltip_121', '');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8783, 'en', 'pname_tooltip_121', '');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8784, 'cs', 'pname_unit_121', '');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8785, 'en', 'pname_unit_121', '');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8798, 'cs', 'pvalue_6582', 'C');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8799, 'en', 'pvalue_6582', 'C');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8800, 'cs', 'pvalue_alias_6582', 'c');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8801, 'en', 'pvalue_alias_6582', 'c');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8802, 'cs', 'pvalue_filter_6582', '');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8803, 'en', 'pvalue_filter_6582', '');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8804, 'cs', 'pvalue_6583', 'D');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8805, 'en', 'pvalue_6583', 'D');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8806, 'cs', 'pvalue_alias_6583', 'd');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8807, 'en', 'pvalue_alias_6583', 'd');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8808, 'cs', 'btn_old_product', '##btn_old_product');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8809, 'en', 'btn_old_product', '##btn_old_product');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8810, 'cs', 'title_prebasket_choose_variant', '##title_prebasket_choose_variant');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8811, 'en', 'title_prebasket_choose_variant', '##title_prebasket_choose_variant');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8812, 'cs', 'pvalue_filter_6583', '');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8813, 'en', 'pvalue_filter_6583', '');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8832, 'cs', 'pvalue_6584', '##x');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8833, 'en', 'pvalue_6584', '##x');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8834, 'cs', 'pname_122', '##Enter a new name');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8835, 'en', 'pname_122', '##Enter a new name');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8836, 'cs', 'pname_123', '##Enter a new name');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8837, 'en', 'pname_123', '##Enter a new name');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8838, 'cs', 'pname_124', '##Enter a new name');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8839, 'en', 'pname_124', '##Enter a new name');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8840, 'cs', 'pname_125', '##Enter a new name');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8841, 'en', 'pname_125', '##Enter a new name');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8842, 'cs', 'pname_tooltip_124', '##');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8843, 'en', 'pname_tooltip_124', '##');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8844, 'cs', 'pname_tooltip_122', '##');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8845, 'en', 'pname_tooltip_122', '##');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8846, 'cs', 'pname_unit_122', '##');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8847, 'en', 'pname_unit_122', '##');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8848, 'cs', 'pname_tooltip_123', '##');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8849, 'en', 'pname_tooltip_123', '##');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8850, 'cs', 'pname_unit_123', '##');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8851, 'en', 'pname_unit_123', '##');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8852, 'cs', 'pname_unit_124', '##');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8853, 'en', 'pname_unit_124', '##');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8854, 'cs', 'pname_tooltip_125', '##');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8855, 'en', 'pname_tooltip_125', '##');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8856, 'cs', 'pname_unit_125', '##');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8857, 'en', 'pname_unit_125', '##');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8858, 'cs', 'pname_126', '##Enter a new name');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8859, 'en', 'pname_126', '##Enter a new name');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8860, 'cs', 'pname_tooltip_126', '##');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8861, 'en', 'pname_tooltip_126', '##');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8862, 'cs', 'pname_unit_126', '##');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8863, 'en', 'pname_unit_126', '##');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8864, 'cs', 'price_from', 'cena od');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8865, 'en', 'price_from', '##price_from');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8866, 'cs', 'availability_only_some_variants', 'jen některé varianty');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8867, 'en', 'availability_only_some_variants', '##availability_only_some_variants');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8868, 'cs', 'paging_prev', '##paging_prev');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8869, 'en', 'paging_prev', '##paging_prev');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8870, 'cs', 'another_address_form', '##another_address_form');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8871, 'en', 'another_address_form', '##another_address_form');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8872, 'cs', 'form_label_agree', '##form_label_agree');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8873, 'en', 'form_label_agree', '##form_label_agree');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8874, 'cs', 'btn_send', '##btn_send');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8875, 'en', 'btn_send', '##btn_send');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8876, 'cs', 'availability_unavailable', '##availability_unavailable');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8877, 'en', 'availability_unavailable', '##availability_unavailable');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8878, 'cs', 'from', '##from');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8879, 'en', 'from', '##from');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8880, 'cs', 'cookie', '##cookie');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8881, 'en', 'cookie', '##cookie');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8882, 'cs', 'link_cookie', '##link_cookie');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8883, 'en', 'link_cookie', '##link_cookie');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8884, 'cs', 'btn_cookie', '##btn_cookie');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8885, 'en', 'btn_cookie', '##btn_cookie');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8886, 'cs', 'search_title', '##search_title');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8887, 'en', 'search_title', '##search_title');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8888, 'cs', 'search_nothing', '##search_nothing');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8889, 'en', 'search_nothing', '##search_nothing');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8890, 'cs', 'search_products', '##search_products');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8891, 'en', 'search_products', '##search_products');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8892, 'cs', 'search_show_all', '##search_show_all');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8893, 'en', 'search_show_all', '##search_show_all');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8894, 'cs', 'search_pages', '##search_pages');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8895, 'en', 'search_pages', '##search_pages');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8896, 'cs', 'filter_dial_main_category', '##filter_dial_mainCategory');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8897, 'en', 'filter_dial_main_category', '##filter_dial_mainCategory');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8898, 'cs', 'filter_main_category_description', '##filter_mainCategory_description');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8899, 'en', 'filter_main_category_description', '##filter_mainCategory_description');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8900, 'cs', 'souhlasim-s-odberem-newsletteru', '##Souhlasím s odběrem newsletteru');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8901, 'en', 'souhlasim-s-odberem-newsletteru', '##Souhlasím s odběrem newsletteru');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8902, 'cs', 'pvalue_alias_6584', '##x');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8903, 'en', 'pvalue_alias_6584', '##x');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8904, 'cs', 'pvalue_filter_6584', '##');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8905, 'en', 'pvalue_filter_6584', '##');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8924, 'cs', 'pname_127', 'Ráže');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8925, 'en', 'pname_127', 'Enter a new name');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8926, 'cs', 'pname_tooltip_127', '');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8927, 'en', 'pname_tooltip_127', '');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8928, 'cs', 'pname_unit_127', '');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8929, 'en', 'pname_unit_127', '');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8936, 'cs', 'pvalue_6586', '4,5 mm');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8937, 'en', 'pvalue_6586', '4,5 mm');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8938, 'cs', 'pvalue_alias_6586', '4-5-mm');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8939, 'en', 'pvalue_alias_6586', '4-5-mm');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8940, 'cs', 'pvalue_filter_6586', '');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8941, 'en', 'pvalue_filter_6586', '');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8942, 'cs', 'pvalue_6587', '5 mm');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8943, 'en', 'pvalue_6587', '5 mm');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8944, 'cs', 'pvalue_alias_6587', '5-mm');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8945, 'en', 'pvalue_alias_6587', '5-mm');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8946, 'cs', 'pvalue_filter_6587', '');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(8947, 'en', 'pvalue_filter_6587', '');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(9020, 'cs', 'pvalue_6588', '##5,5 mm');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(9021, 'en', 'pvalue_6588', '##5,5 mm');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(9022, 'cs', 'pvalue_alias_6588', '##5-5-mm');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(9023, 'en', 'pvalue_alias_6588', '##5-5-mm');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(9024, 'cs', 'pvalue_filter_6588', '##');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(9025, 'en', 'pvalue_filter_6588', '##');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(9026, 'cs', 'pname_128', 'Délka hlavně');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(9027, 'en', 'pname_128', 'Enter a new name');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(9028, 'cs', 'pname_tooltip_128', '');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(9029, 'en', 'pname_tooltip_128', '');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(9030, 'cs', 'pname_unit_128', 'mm');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(9031, 'en', 'pname_unit_128', '');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(9050, 'cs', 'pname_112', '##Stránky');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(9051, 'en', 'pname_112', '##Stránky');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(9052, 'cs', 'pname_tooltip_112', '##');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(9053, 'en', 'pname_tooltip_112', '##');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(9054, 'cs', 'pname_unit_112', '##');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(9055, 'en', 'pname_unit_112', '##');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(9056, 'cs', 'comp_title', 'Porovnání');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(9057, 'cs', 'pvalue_6568', '##Tag 1');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(9058, 'en', 'pvalue_6568', '##Tag 1');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(9059, 'cs', 'pvalue_alias_6568', '##tag-1');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(9060, 'en', 'pvalue_alias_6568', '##tag-1');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(9061, 'cs', 'pvalue_filter_6568', '##');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(9062, 'en', 'pvalue_filter_6568', '##');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(9063, 'cs', 'pvalue_6569', '##Tag 2');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(9064, 'en', 'pvalue_6569', '##Tag 2');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(9065, 'cs', 'pvalue_alias_6569', '##tag-2');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(9066, 'en', 'pvalue_alias_6569', '##tag-2');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(9067, 'cs', 'pvalue_filter_6569', '##');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(9068, 'en', 'pvalue_filter_6569', '##');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(9069, 'cs', 'pname_115', '##Tag');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(9070, 'en', 'pname_115', '##Tag');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(9071, 'cs', 'pname_tooltip_115', '##');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(9072, 'en', 'pname_tooltip_115', '##');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(9073, 'cs', 'pname_unit_115', '##');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(9074, 'en', 'pname_unit_115', '##');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(9075, 'cs', 'newsletter_note', 'Novinky ze světa Balistas.cz přímo do e-mailu.');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(9076, 'cs', 'newsletter_link', 'zpracováním osobních údajů');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(9077, 'cs', 'contact_title_decor', 'Potřebujete s námi něco vyřídit?');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(9078, 'cs', 'contact_title', 'napište nám');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(9085, 'cs', 'message_buy_to_free_transport', 'Nakupte ještě za %priceToFreeTransport% a máte dopravu zdarma!');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(9086, 'en', 'message_buy_to_free_transport', '##message_buy_to_free_transport');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(9088, 'cs', 'next', '##Next');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(9089, 'en', 'next', '##Next');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(9090, 'cs', 'order_login_1', '##order_login_1');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(9091, 'en', 'order_login_1', '##order_login_1');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(9092, 'cs', 'order_login_2', '##order_login_2');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(9093, 'en', 'order_login_2', '##order_login_2');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(9094, 'cs', 'order_login_3', '##order_login_3');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(9095, 'en', 'order_login_3', '##order_login_3');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(9096, 'cs', 'billing_info_title', 'Fakturační údaje');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(9097, 'en', 'billing_info_title', '##billing_info_title');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(9098, 'cs', 'title_register_form', '##title_register_form');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(9099, 'en', 'title_register_form', '##title_register_form');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(9130, 'cs', 'form_label_heureka_disable', 'Nesouhlasím se zasláním dotazníku spokojenosti');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(9131, 'cs', 'search_categories', '##search_categories');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(9132, 'en', 'search_categories', '##search_categories');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(9181, 'cs', 'product_flag_new', '##product_flag_new');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(9182, 'en', 'product_flag_new', '##product_flag_new');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(9183, 'cs', 'free_transport_text_1_buy_to', 'Nakupte ještě za');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(9184, 'cs', 'free_transport_text_1_get', 'a získáte');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(9185, 'cs', 'free_transport_text_1_delivery_free', 'dopravu zdarma');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(9186, 'cs', 'title_tags', '##title_tags');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(9187, 'en', 'title_tags', '##title_tags');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(9188, 'cs', 'pname_130', '##Enter a new name');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(9189, 'en', 'pname_130', '##Enter a new name');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(9190, 'cs', 'pname_tooltip_130', '##');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(9191, 'en', 'pname_tooltip_130', '##');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(9192, 'cs', 'pname_unit_130', '##');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(9193, 'en', 'pname_unit_130', '##');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(9194, 'cs', 'title_menu', '##title_menu');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(9195, 'en', 'title_menu', '##title_menu');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(9196, 'cs', 'tag', '##tag');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(9197, 'en', 'tag', '##tag');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(9198, 'cs', 'article_reading_time', '##article_reading_time');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(9199, 'en', 'article_reading_time', '##article_reading_time');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(9200, 'cs', 'minutes', '##minutes');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(9201, 'en', 'minutes', '##minutes');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(9202, 'cs', 'title_authors_other', '##title_authors_other');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(9203, 'en', 'title_authors_other', '##title_authors_other');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(9204, 'cs', 'authors_all', '##authors_all');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(9205, 'en', 'authors_all', '##authors_all');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(9206, 'cs', 'search_tab_trees', '##search_tab_trees');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(9207, 'en', 'search_tab_trees', '##search_tab_trees');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(9208, 'cs', 'search_tab_blogs', '##search_tab_blogs');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(9209, 'en', 'search_tab_blogs', '##search_tab_blogs');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(9210, 'cs', 'search_tab_products', '##search_tab_products');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(9211, 'en', 'search_tab_products', '##search_tab_products');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(9212, 'cs', 'search_tab_categories', '##search_tab_categories');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(9213, 'en', 'search_tab_categories', '##search_tab_categories');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(9214, 'cs', 'filter_flag_is_in_discount', '##filter_flag_isInDiscount');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(9215, 'en', 'filter_flag_is_in_discount', '##filter_flag_isInDiscount');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(9216, 'cs', 'filter_flag_is_in_discount_unit', '##filter_flag_isInDiscount_unit');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(9217, 'en', 'filter_flag_is_in_discount_unit', '##filter_flag_isInDiscount_unit');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(9218, 'cs', 'filter_flag_is_in_discount_description', '##filter_flag_isInDiscount_description');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(9219, 'en', 'filter_flag_is_in_discount_description', '##filter_flag_isInDiscount_description');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(9244, 'cs', 'pvalue_6593', '##h4');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(9245, 'en', 'pvalue_6593', '##h4');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(9246, 'cs', 'pvalue_alias_6593', '##h4');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(9247, 'en', 'pvalue_alias_6593', '##h4');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(9248, 'cs', 'pvalue_filter_6593', '##');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(9249, 'en', 'pvalue_filter_6593', '##');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(9250, 'cs', 'pname_131', '##Enter a new name');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(9251, 'en', 'pname_131', '##Enter a new name');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(9252, 'cs', 'pname_tooltip_131', '##');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(9253, 'en', 'pname_tooltip_131', '##');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(9254, 'cs', 'pname_unit_131', '##');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(9255, 'en', 'pname_unit_131', '##');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(9256, 'cs', 'filter_order_status', '##filter_order_status');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(9257, 'en', 'filter_order_status', '##filter_order_status');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(9258, 'cs', 'filter_order_date_from', '##filter_order_date_from');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(9259, 'en', 'filter_order_date_from', '##filter_order_date_from');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(9260, 'cs', 'filter_order_date_to', '##filter_order_date_to');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(9261, 'en', 'filter_order_date_to', '##filter_order_date_to');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(9262, 'cs', 'filter_order_number', '##filter_order_number');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(9263, 'en', 'filter_order_number', '##filter_order_number');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(9264, 'cs', 'order_title', '##order_title');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(9265, 'en', 'order_title', '##order_title');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(9266, 'cs', 'order_status', '##order_status');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(9267, 'en', 'order_status', '##order_status');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(9268, 'cs', 'order_number', '##order_number');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(9269, 'en', 'order_number', '##order_number');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(9270, 'cs', 'order_amount', '##order_amount');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(9271, 'en', 'order_amount', '##order_amount');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(9272, 'cs', 'order_created', '##order_created');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(9273, 'en', 'order_created', '##order_created');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(9274, 'cs', 'order_delivery', '##order_delivery');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(9275, 'en', 'order_delivery', '##order_delivery');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(9276, 'cs', 'order_payment', '##order_payment');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(9277, 'en', 'order_payment', '##order_payment');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(9278, 'cs', 'order_total_price_vat', '##order_total_price_vat');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(9279, 'en', 'order_total_price_vat', '##order_total_price_vat');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(9280, 'cs', 'btn_show_all_orders', '##btn_show_all_orders');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(9281, 'en', 'btn_show_all_orders', '##btn_show_all_orders');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(9282, 'cs', 'order_detail_title', '##order_detail_title');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(9283, 'en', 'order_detail_title', '##order_detail_title');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(9284, 'cs', 'order_item_status', '##order_item_status');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(9285, 'en', 'order_item_status', '##order_item_status');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(9286, 'cs', 'unit_price', '##unit_price');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(9287, 'en', 'unit_price', '##unit_price');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(9288, 'cs', 'total_price_vat_total', '##total_price_vat_total');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(9289, 'en', 'total_price_vat_total', '##total_price_vat_total');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(9290, 'cs', 'filter_flag_is_in_store', '##filter_flag_isInStore');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(9291, 'en', 'filter_flag_is_in_store', '##filter_flag_isInStore');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(9292, 'cs', 'filter_flag_is_in_store_unit', '##filter_flag_isInStore_unit');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(9293, 'en', 'filter_flag_is_in_store_unit', '##filter_flag_isInStore_unit');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(9294, 'cs', 'filter_flag_is_in_store_description', '##filter_flag_isInStore_description');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(9295, 'en', 'filter_flag_is_in_store_description', '##filter_flag_isInStore_description');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(9296, 'cs', 'is_in_store', '##isInStore');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(9297, 'en', 'is_in_store', '##isInStore');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(9298, 'cs', 'pname_filter_prefix_114', '##');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(9299, 'en', 'pname_filter_prefix_114', '##');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(9300, 'cs', 'newsletter_already_added', '##newsletterAlreadyAdded');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(9301, 'en', 'newsletter_already_added', '##newsletterAlreadyAdded');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(9302, 'cs', 'title_attached_articles', '##title_attached_articles');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(9303, 'en', 'title_attached_articles', '##title_attached_articles');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(9304, 'cs', 'pname_filter_prefix_113', '##');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(9305, 'en', 'pname_filter_prefix_113', '##');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(9306, 'cs', 'author', '##author');
INSERT INTO `string` (`id`, `lg`, `name`, `value`) VALUES
	(9307, 'en', 'author', '##author');

