UPDATE string SET value = 'Objednávka úspěšně odeslána.  Děkujeme za váš nákup 🐲!', usedAt = null WHERE name = 'title_order_success';
UPDATE string SET value = 'Vaši objednávku jsme přijali a začínáme pracovat na jejím odbavení.', usedAt = null WHERE name = 'order_text_completed';
UPDATE string SET value = 'Prosím, uhraďte %price', usedAt = null WHERE name = 'payment_instructions_title';
UPDATE string SET value = 'Platba se nezdařila 🚫', usedAt = null WHERE name = 'order_status_payment_failed';

INSERT INTO string (lg, name, value, usedAt) VALUES ('cs', 'payment_instructions_subtitle', 'Z<PERSON>lili jste rychlou QR platbu. Otevřete si aplikaci své banky, naskenujte QR kód, nastavte před odesláním okamžitou platbu a potvrďte.', null);
INSERT INTO string (lg, name, value, usedAt) VALUES ('cs', 'order_no', '<PERSON><PERSON>lo objednávky', null);
INSERT INTO string (lg, name, value, usedAt) VALUES ('cs', 'order_email_send', 'Potvrzení objednávky a datum i hodinu dodání najdete na své e-mailové adrese. Pokud byste přeci jen měli nějaké nejasnosti, neváhejte nás kontaktovat na naší lince %phone.', null);
INSERT INTO string (lg, name, value, usedAt) VALUES ('cs', 'store_pickup_title', 'Místo pro osobní vyzvednutí', null);
