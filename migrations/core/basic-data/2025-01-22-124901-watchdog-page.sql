SET NAMES utf8mb4;

INSERT INTO `tree` ( `mutationId`, `rootId`, `extId`, `treeParentId`, `parentId`, `level`, `path`, `sort`, `last`, `uid`, `created`, `createdTime`, `createdTimeOrder`, `edited`, `editedTime`, `template`, `type`, `publicFrom`, `publicTo`, `name`, `nameAnchor`, `nameAnchorBreadcrumb`, `nameTitle`, `nameShort`, `nameHeading`, `description`, `keywords`, `public`, `score`, `forceNoIndex`, `hideInSearch`, `hideInSitemap`, `hideInMenu`, `annotation`, `content`, `hideFirstImage`, `links`, `seoTitleFilter`, `seoAnnotationFilter`, `seoDescriptionFilter`, `videos`, `customFieldsJson`, `customContentJson`, `productAttachedId`, `hasLinkedCategories`) VALUES
	(1,	1,	NULL,	1655,	398,	2,	'1|398|',	11,	1,	'watchdog',	0,	'2025-01-22 10:04:31',	'2025-01-22 10:04:00',	32,	'2025-01-22 10:07:10',	':Front:Product:watchdog',	'common',	'2025-01-22 10:04:00',	'2125-01-22 10:04:00',	'Hlídat dostupnost',	'Hlídat dostupnost',	'',	'Hlídat dostupnost',	'',	'',	'',	'',	1,	0,	1,	1,	0,	1,	'',	'',	NULL,	NULL,	NULL,	NULL,	NULL,	NULL,	'{}',	'{}',	NULL,	NULL);

SET @watchdogId = LAST_INSERT_ID();
INSERT INTO `alias` ( `alias`, `module`, `referenceId`, `mutationId`) VALUES ('watchdog',	'tree',	@watchdogId,	1);
