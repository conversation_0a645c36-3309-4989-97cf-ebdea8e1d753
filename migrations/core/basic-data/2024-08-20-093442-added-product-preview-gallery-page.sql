SET NAMES utf8mb4;

INSERT INTO `tree` (`mutationId`, `rootId`, `extId`, `treeParentId`, `parentId`, `level`, `path`, `sort`, `last`, `uid`, `created`, `createdTime`, `createdTimeOrder`, `edited`, `editedTime`, `template`, `type`, `publicFrom`, `publicTo`, `name`, `nameAnchor`, `nameAnchorBreadcrumb`, `nameTitle`, `nameShort`, `nameHeading`, `description`, `keywords`, `public`, `score`, `forceNoIndex`, `hideInSearch`, `hideInSitemap`, `hideInMenu`, `annotation`, `content`, `hideFirstImage`, `links`, `seoTitleFilter`, `seoAnnotationFilter`, `seoDescriptionFilter`, `videos`, `customFieldsJson`, `customContentJson`, `productAttachedId`, `hasLinkedCategories`) VALUES
	(	1,	1,	NULL,	499,	398,	2,	'1|398|',	10,	1,	'productGallery',	0,	'2024-08-20 09:21:08',	'2024-08-20 09:21:00',	32,	'2024-08-20 09:24:06',	':Front:Product:gallery',	'common',	'2024-08-20 09:21:00',	'2124-08-20 09:21:00',	'Náhled produktu',	'Náhled produktu',	'',	'Náhled produktu',	'',	'',	'',	'',	1,	0,	1,	1,	0,	1,	'',	'',	NULL,	NULL,	NULL,	NULL,	NULL,	NULL,	'{}',	'{}',	NULL,	NULL);
SET @productGallery = LAST_INSERT_ID();
INSERT INTO `alias` ( `alias`, `module`, `referenceId`, `mutationId`) VALUES ('nahled-produktu',	'tree',	@productGallery,	1);
