
CREATE TABLE IF NOT EXISTS `user_mutation` (
	`id` int(11) NOT NULL AUTO_INCREMENT,
	`userId` int(11) NOT NULL,
	`mutationId` int(11) NOT NULL,
	`newsletter` tinyint(4) NOT NULL DEFAULT '0',
	PRIMARY KEY (`id`),
	<PERSON><PERSON><PERSON> `userId` (`userId`),
	<PERSON><PERSON><PERSON> `mutationId` (`mutationId`),
	CONSTRAINT `user_mutation_ibfk_3` FOREIGN KEY (`userId`) REFERENCES `user` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
	CONSTRAINT `user_mutation_ibfk_4` FOREIGN KEY (`mutationId`) REFERENCES `mutation` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
	) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 COLLATE=utf8_czech_ci;
