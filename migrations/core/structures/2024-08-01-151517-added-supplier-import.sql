-- Adminer 4.8.1 MySQL 10.6.12-MariaDB dump

SET NAMES utf8;
SET time_zone = '+00:00';
SET foreign_key_checks = 0;
SET sql_mode = 'NO_AUTO_VALUE_ON_ZERO';

SET NAMES utf8mb4;

DROP TABLE IF EXISTS `product_redirect`;
CREATE TABLE `product_redirect` (
									`id` int(11) NOT NULL AUTO_INCREMENT,
									`productId` int(11) NOT NULL,
									`erpId` int(11) NOT NULL,
									PRIMARY KEY (`id`),
									UNIQUE KEY `productId_erpId` (`productId`,`erpId`),
									CONSTRAINT `product_redirect_ibfk_1` FOREIGN KEY (`productId`) REFERENCES `product` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci;


-- 2024-08-01 13:39:38
ALTER TABLE `import_cache`
	CHANGE `type` `type` enum('product','stock','review','product_images','writer','publisher','coupon','price','supplier') COLLATE 'utf8mb4_czech_ci' NOT NULL AFTER `id`;

ALTER TABLE `supplier`
	ADD `syncChecksum` varchar(128) NULL,
	ADD `syncTime` datetime NULL AFTER `syncChecksum`;

