UPDATE `mutation` SET `customFieldsJson`='{"mutationData":[{"icon":"cz"}]}' WHERE  `id`=1;
UPDATE `mutation` SET `customFieldsJson`='{}' WHERE  `id`=2;
INSERT INTO `tree` (`id`, `mutationId`, `rootId`, `parentId`, `level`, `path`, `sort`, `last`, `uid`, `created`, `createdTime`, `createdTimeOrder`, `edited`, `editedTime`, `template`, `type`, `publicFrom`, `publicTo`, `name`, `nameAnchor`, `nameTitle`, `description`, `keywords`, `public`, `forceNoIndex`, `hideInSearch`, `hideInSitemap`, `annotation`, `content`, `hideFirstImage`, `links`, `seoTitleFilter`, `seoAnnotationFilter`, `seoDescriptionFilter`, `videos`, `hideCrossroad`, `showContactForm`, `showOnHomepage`, `customFieldsJson`, `customContentJson`, `customContentSchemeJson`, `productAttachedId`, `hasLinkedCategories`) VALUES (2, 2, 2, NULL, 0, NULL, 12, 0, 'title', 0, '2022-09-04 20:02:34', '2022-09-04 20:02:34', 3, '2022-09-05 10:06:22', ':Front:Homepage:default', 'common', '2022-09-04 20:02:34', '2122-09-04 20:02:34', 'En', 'En', 'En', '', '', 1, 0, 0, 0, '', '', NULL, '', NULL, NULL, NULL, '', 0, 0, 0, '{}', '{}', '[]', NULL, NULL);
INSERT INTO `alias` ( `alias`, `module`, `referenceId`, `mutationId`) VALUES  ('', 'tree', 2, 2);
