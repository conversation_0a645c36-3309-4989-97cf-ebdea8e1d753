-- Adminer 4.8.1 MySQL 10.6.12-MariaDB dump

SET NAMES utf8;
SET time_zone = '+00:00';
SET foreign_key_checks = 0;
SET sql_mode = 'NO_AUTO_VALUE_ON_ZERO';

SET NAMES utf8mb4;

DROP TABLE IF EXISTS `series`;
CREATE TABLE `series` (
						  `id` int(11) NOT NULL AUTO_INCREMENT,
						  `parameterValueSeriesId` int(11) NOT NULL,
						  `uid` varchar(255) DEFAULT NULL,
						  `internalName` varchar(255) NOT NULL,
						  `order` int(11) DEFAULT NULL,
						  `customFieldsJson` text DEFAULT NULL,
						  PRIMARY KEY (`id`),
						  KEY `parameterValueSeriesId` (`parameterValueSeriesId`),
						  CONSTRAINT `series_ibfk_2` FOREIGN KEY (`parameterValueSeriesId`) REFERENCES `parameter_value` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci;


DROP TABLE IF EXISTS `series_localization`;
CREATE TABLE `series_localization` (
									   `id` int(11) NOT NULL AUTO_INCREMENT,
									   `erpId` int(11) DEFAULT NULL,
									   `syncTime` datetime DEFAULT NULL,
									   `syncChecksum` varchar(128) DEFAULT NULL,
									   `mutationId` int(11) NOT NULL,
									   `seriesId` int(11) NOT NULL,
									   `name` varchar(255) NOT NULL DEFAULT '',
									   `nameAnchor` varchar(255) DEFAULT NULL,
									   `nameTitle` varchar(255) DEFAULT NULL,
									   `description` text DEFAULT NULL,
									   `keywords` text DEFAULT NULL,
									   `title` varchar(255) DEFAULT NULL,
									   `public` tinyint(1) NOT NULL,
									   `publicFrom` datetime DEFAULT NULL,
									   `publicTo` datetime DEFAULT NULL,
									   `editedTime` datetime DEFAULT NULL,
									   `createdTime` datetime DEFAULT NULL,
									   `edited` int(11) DEFAULT NULL,
									   `customFieldsJson` text DEFAULT NULL,
									   `customContentJson` text DEFAULT NULL,
									   PRIMARY KEY (`id`),
									   KEY `mutationId` (`mutationId`),
									   KEY `seriesId` (`seriesId`),
									   KEY `erpId` (`erpId`),
									   CONSTRAINT `series_localization_ibfk_1` FOREIGN KEY (`mutationId`) REFERENCES `mutation` (`id`),
									   CONSTRAINT `series_localization_ibfk_2` FOREIGN KEY (`seriesId`) REFERENCES `series` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci;


DROP TABLE IF EXISTS `series_x_product`;
CREATE TABLE `series_x_product` (
									`id` int(11) NOT NULL AUTO_INCREMENT,
									`seriesId` int(11) NOT NULL,
									`productId` int(11) NOT NULL,
									`sort` int(11) DEFAULT NULL,
									PRIMARY KEY (`id`),
									KEY `seriesId` (`seriesId`),
									KEY `productId` (`productId`),
									CONSTRAINT `series_x_product_ibfk_1` FOREIGN KEY (`seriesId`) REFERENCES `series` (`id`) ON DELETE CASCADE,
									CONSTRAINT `series_x_product_ibfk_2` FOREIGN KEY (`productId`) REFERENCES `product` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci;


-- 2024-10-24 09:56:52

SET NAMES utf8mb4;

INSERT INTO `tree` ( `mutationId`, `rootId`, `extId`, `treeParentId`, `parentId`, `level`, `path`, `sort`, `last`, `uid`, `created`, `createdTime`, `createdTimeOrder`, `edited`, `editedTime`, `template`, `type`, `publicFrom`, `publicTo`, `name`, `nameAnchor`, `nameAnchorBreadcrumb`, `nameTitle`, `nameShort`, `nameHeading`, `description`, `keywords`, `public`, `score`, `forceNoIndex`, `hideInSearch`, `hideInSitemap`, `hideInMenu`, `annotation`, `content`, `hideFirstImage`, `links`, `seoTitleFilter`, `seoAnnotationFilter`, `seoDescriptionFilter`, `videos`, `customFieldsJson`, `customContentJson`, `productAttachedId`, `hasLinkedCategories`) VALUES
	(1,	1,	NULL,	502,	1,	1,	'1|',	6,	1,	'series',	0,	'2024-10-25 10:22:52',	'2024-10-25 10:22:00',	32,	'2024-10-25 10:24:54',	':Series:Front:Series:default',	'common',	'2024-10-25 10:22:00',	'2124-10-25 10:22:00',	'Série',	'Série',	'',	'Série',	'',	'',	'',	'',	1,	0,	0,	0,	0,	0,	'',	'',	NULL,	NULL,	NULL,	NULL,	NULL,	NULL,	'{}',	'{}',	NULL,	NULL);
SET @series = LAST_INSERT_ID();
INSERT INTO `alias` ( `alias`, `module`, `referenceId`, `mutationId`) VALUES ('serie',	'tree',	@series,	1);
