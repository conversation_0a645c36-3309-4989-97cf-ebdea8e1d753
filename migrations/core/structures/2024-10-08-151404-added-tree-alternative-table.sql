SET NAMES utf8mb4;

DROP TABLE IF EXISTS `tree_alternative`;
CREATE TABLE `tree_alternative` (
									`id` int(11) NOT NULL AUTO_INCREMENT,
									`type` varchar(50) NOT NULL,
									`alt_id` int(11) NOT NULL,
									`alt_name` varchar(255) NOT NULL,
									`alt_path` varchar(255) NOT NULL,
									PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci;

ALTER TABLE `tree_alternative`
	ADD `syncTime` datetime NULL,
	ADD `syncChecksum` varchar(128) COLLATE 'utf8mb4_unicode_520_ci' NULL AFTER `syncTime`;

ALTER TABLE `tree_alternative`
	ADD `extId` varchar(128) COLLATE 'utf8mb4_unicode_520_ci' NULL;

ALTER TABLE `import_cache`
	CHANGE `type` `type` enum('product','stock','review','product_images','writer','publisher','coupon','price','supplier','category','order','heureka_review','alternative_category') COLLATE 'utf8mb4_czech_ci' NOT NULL AFTER `id`;
