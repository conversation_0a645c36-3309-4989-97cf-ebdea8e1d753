
CREATE TABLE IF NOT EXISTS `image` (
	`id` int(11) NOT NULL AUTO_INCREMENT,
	`name` varchar(250) NOT NULL,
	`filename` varchar(250) DEFAULT NULL,
	`libraryId` int(11) DEFAULT NULL,
	`sort` int(11) DEFAULT NULL,
	`sourceImage` varchar(255) DEFAULT NULL,
	`md5` varchar(255) DEFAULT NULL,
	PRIMARY KEY (`id`),
	KEY `cat_idx` (`libraryId`),
	CONSTRAINT `cat` FOREIGN KEY (`libraryId`) REFERENCES `library_tree` (`id`) ON DELETE NO ACTION ON UPDATE NO ACTION
	) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;
