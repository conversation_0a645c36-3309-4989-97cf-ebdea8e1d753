-- Adminer 4.8.1 MySQL 10.6.12-<PERSON>DB dump

SET NAMES utf8;
SET time_zone = '+00:00';
SET foreign_key_checks = 0;
SET sql_mode = 'NO_AUTO_VALUE_ON_ZERO';

SET NAMES utf8mb4;

DROP TABLE IF EXISTS `class_event_x_user`;
CREATE TABLE `class_event_x_user` (
									  `id` int(11) NOT NULL AUTO_INCREMENT,
									  `classEventId` int(11) NOT NULL,
									  `userId` int(11) NOT NULL,
									  PRIMARY KEY (`id`),
									  UNIQUE KEY `classEventId_userId` (`classEventId`,`userId`),
									  <PERSON><PERSON><PERSON> `userId` (`userId`),
									  CONSTRAINT `class_event_x_user_ibfk_2` FOREIGN KEY (`classEventId`) REFERENCES `class_event` (`id`) ON DELETE CASCADE,
									  CONSTRAINT `class_event_x_user_ibfk_3` FOREIGN KEY (`userId`) REFERENCES `user` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='Lektory';


-- 2025-02-26 12:25:01

ALTER TABLE `class_event`
	ADD `public` tinyint(1) NOT NULL DEFAULT '0';

ALTER TABLE `product`
	ADD `requalificationPossibility82` tinyint(1) NOT NULL DEFAULT '0',
	ADD `requalificationPossibility100` tinyint(1) NOT NULL DEFAULT '0' AFTER `requalificationPossibility82`;
