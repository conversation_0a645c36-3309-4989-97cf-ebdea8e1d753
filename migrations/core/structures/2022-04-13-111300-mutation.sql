

-- Export<PERSON><PERSON><PERSON> struktury pro tabulka 1691_superadmin.mutation
CREATE TABLE IF NOT EXISTS `mutation` (
	`id` int(11) NOT NULL AUTO_INCREMENT,
	`langCode` varchar(250) COLLATE utf8_bin NOT NULL,
	`public` int(11) DEFAULT '1',
	`name` varchar(250) COLLATE utf8_bin DEFAULT NULL,
	`langMenu` varchar(250) COLLATE utf8_bin DEFAULT NULL,
	`adminEmail` varchar(250) COLLATE utf8_bin DEFAULT NULL,
	`contactEmail` varchar(250) COLLATE utf8_bin DEFAULT NULL,
	`orderEmail` varchar(250) COLLATE utf8_bin DEFAULT NULL,
	`fromEmail` varchar(250) COLLATE utf8_bin DEFAULT NULL,
	`fromEmailName` varchar(250) COLLATE utf8_bin DEFAULT NULL,
	`currency` text CHARACTER SET utf8mb4 COLLATE utf8mb4_czech_ci,
	`synonyms` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_czech_ci,
	`heurekaOverenoKey` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_czech_ci DEFAULT NULL,
	PRIMARY KEY (`id`),
	UNIQUE KEY `langCode` (`langCode`)
	) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8 COLLATE=utf8_bin;
