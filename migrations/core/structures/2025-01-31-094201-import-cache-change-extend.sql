ALTER TABLE `import_cache_change`
	ADD `type` varchar(20) COLLATE 'utf8mb4_general_ci' NOT NULL AFTER `status`,
	ADD `extId` varchar(20) COLLATE 'utf8mb4_general_ci' NULL AFTER `type`;


ALTER TABLE `import_cache_change`
	RENAME TO `import_cache_odoo`;

ALTER TABLE `user`
	ADD `extId` int(11) NULL AFTER `id`;


-- Adminer 4.8.1 MySQL 10.6.12-MariaDB dump

SET NAMES utf8;
SET time_zone = '+00:00';
SET foreign_key_checks = 0;
SET sql_mode = 'NO_AUTO_VALUE_ON_ZERO';

SET NAMES utf8mb4;

DROP TABLE IF EXISTS `export_cache`;
CREATE TABLE `export_cache` (
								`id` int(11) NOT NULL AUTO_INCREMENT,
								`status` varchar(20) NOT NULL,
								`model` varchar(20) NOT NULL,
								`message` text DEFAULT NULL,
								`data` longtext DEFAULT NULL,
								`response` longtext DEFAULT NULL,
								`createdTime` datetime NOT NULL,
								`dispatchTime` datetime DEFAULT NULL,
								`exportTime` datetime DEFAULT NULL,
								PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;


-- 2025-02-05 14:05:19

ALTER TABLE `state`
	ADD `extId` varchar(20) NULL AFTER `id`;

UPDATE `state` SET `extId` = '56' WHERE `id` = '1';
UPDATE `state` SET `extId` = '201' WHERE `id` = '2';
