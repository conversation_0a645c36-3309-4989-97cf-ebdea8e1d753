ALTER TABLE `card_payment`
	ADD UNIQUE `paymentGatewayUniqueIdentifier_externalId_status` (`paymentGatewayUniqueIdentifier`, `externalId`, `status`),
	DROP INDEX `paymentGatewayUniqueIdentifier`;
ALTER TABLE `card_payment`
	ADD UNIQUE `paymentGatewayUniqueIdentifier_externalId_status_expireTime` (`paymentGatewayUniqueIdentifier`, `externalId`, `status`, `expireTime`),
	DROP INDEX `paymentGatewayUniqueIdentifier_externalId_status`;
