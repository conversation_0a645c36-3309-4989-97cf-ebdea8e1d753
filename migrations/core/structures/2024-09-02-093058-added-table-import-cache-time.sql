SET NAMES utf8;
SET time_zone = '+00:00';
SET foreign_key_checks = 0;
SET sql_mode = 'NO_AUTO_VALUE_ON_ZERO';

SET NAMES utf8mb4;

DROP TABLE IF EXISTS `import_cache_time`;
CREATE TABLE `import_cache_time` (
									 `id` int(11) NOT NULL AUTO_INCREMENT,
									 `type` varchar(255) NOT NULL,
									 `updatedAt` datetime NOT NULL,
									 `generatedAt` datetime NOT NULL,
									 `numRecords` int(11) NOT NULL,
									 `history` text NOT NULL DEFAULT '{}',
									 PRIMARY KEY (`id`),
									 UNIQUE KEY `type` (`type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci;
