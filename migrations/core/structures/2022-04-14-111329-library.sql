
CREATE TABLE IF NOT EXISTS `library_tree` (
	`id` int(11) NOT NULL AUTO_INCREMENT,
	`parentId` int(11) DEFAULT NULL,
	`level` tinyint(4) DEFAULT NULL,
	`path` varchar(255) DEFAULT NULL,
	`sort` mediumint(9) DEFAULT NULL,
	`last` tinyint(1) DEFAULT NULL,
	`created` int(11) NOT NULL,
	`createdTime` datetime NOT NULL,
	`edited` int(11) NOT NULL,
	`editedTime` datetime NOT NULL,
	`publicFrom` datetime DEFAULT NULL,
	`publicTo` datetime DEFAULT NULL,
	`name` varchar(255) DEFAULT NULL,
	`nameTitle` varchar(255) DEFAULT NULL,
	`nameAnchor` varchar(255) DEFAULT NULL,
	`uid` varchar(50) DEFAULT NULL,
	PRIMARY KEY (`id`),
	<PERSON>EY `idParent` (`parentId`),
	CONSTRAINT `library_tree_ibfk_2` FOREIGN KEY (`parentId`) REFERENCES `library_tree` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
	) ENGINE=InnoDB AUTO_INCREMENT=28 DEFAULT CHARSET=utf8;
