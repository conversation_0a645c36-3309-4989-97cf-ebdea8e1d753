ALTER TABLE `order_product`
	DROP COLUMN IF EXISTS `variantCode`,
	ADD `variantCode` varchar(255) NULL AFTER `variantId`;
ALTER TABLE `order`
	DROP COLUMN IF EXISTS `syncChecksum`,
	ADD `syncChecksum` varchar(64) NULL;
ALTER TABLE `order`
	CHANGE COLUMN IF EXISTS `extCode` `extState` varchar(2558) COLLATE 'utf8mb4_general_ci' NULL AFTER `extId`;
ALTER TABLE `order_delivery`
	DROP COLUMN IF EXISTS `deliveryMethodName`,
	ADD `deliveryMethodName` varchar(255) NULL AFTER `deliveryMethodId`;
ALTER TABLE `order_payment`
	DROP COLUMN IF EXISTS `paymentMethodName`,
	ADD `paymentMethodName` varchar(255) NULL AFTER `paymentMethodId`;
ALTER TABLE `order_product`
	DROP FOREIGN KEY `order_product_ibfk_2`,
	ADD FOREIGN KEY (`variantId`) REFERENCES `product_variant` (`id`) ON DELETE SET NULL;
ALTER TABLE `order_voucher`
	DROP COLUMN IF EXISTS `voucherType`,
	ADD `voucherType` varchar(255) COLLATE 'utf8mb4_general_ci' NULL;
