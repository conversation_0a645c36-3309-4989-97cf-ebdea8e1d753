
CREATE TABLE IF NOT EXISTS `alias_history` (
	`id` int(11) NOT NULL AUTO_INCREMENT,
	`alias` varchar(120) CHARACTER SET utf8mb4 NOT NULL,
	`module` varchar(30) NOT NULL,
	`referenceId` int(11) NOT NULL,
	`mutationId` int(11) NOT NULL DEFAULT '1',
	PRIMARY KEY (`id`),
	UNIQUE KEY `alias_lg` (`alias`,`mutationId`),
	UNIQUE KEY `alias_modul_idref_lg` (`alias`,`module`,`referenceId`,`mutationId`) USING BTREE,
	KEY `mutationId` (`mutationId`),
	CONSTRAINT `alias_history_ibfk_1` FOREIGN KEY (`mutationId`) REFERENCES `mutation` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
	) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;

