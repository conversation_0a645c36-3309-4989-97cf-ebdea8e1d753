
CREATE TABLE IF NOT EXISTS `user` (
	`id` int(11) NOT NULL AUTO_INCREMENT,
	`email` varchar(50) NOT NULL,
	`password` varchar(255) NOT NULL,
	`role` varchar(20) DEFAULT 'user',
	`firstname` varchar(120) NOT NULL,
	`lastname` varchar(120) NOT NULL,
	`phone` varchar(120) NOT NULL,
	`street` varchar(250) NOT NULL,
	`city` varchar(120) NOT NULL,
	`zip` varchar(20) NOT NULL,
	`stateId` int(11) DEFAULT NULL,
	`company` varchar(250) NOT NULL,
	`ic` varchar(20) NOT NULL,
	`dic` varchar(20) NOT NULL,
	`created` int(11) DEFAULT NULL,
	`createdTime` datetime DEFAULT NULL,
	`edited` int(11) DEFAULT NULL,
	`editedTime` datetime DEFAULT NULL,
	`lastLogin` datetime DEFAULT NULL,
	`customAddress<PERSON>son` text,
	`orderCount` int(11) DEFAULT '0',
	`priceLevelId` int(11) DEFAULT '1',
	`customFieldsJson` longtext,
	PRIMARY KEY (`id`),
	KEY `stateId` (`stateId`),
	CONSTRAINT `user_ibfk_1` FOREIGN KEY (`stateId`) REFERENCES `state` (`id`) ON DELETE SET NULL ON UPDATE CASCADE
	) ENGINE=InnoDB AUTO_INCREMENT=31 DEFAULT CHARSET=utf8;
