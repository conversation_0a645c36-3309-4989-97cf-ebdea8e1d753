DROP TABLE `elastic_search_index`;

CREATE TABLE `elastic_search_index` (
										`id` INT(11) NOT NULL AUTO_INCREMENT,
										`mutationId` INT(11) NULL DEFAULT NULL,
										`type` VARCHAR(255) NOT NULL COLLATE 'utf8mb4_czech_ci',
										`createdTime` DATETIME NULL DEFAULT NULL,
										`startTime` DATETIME NULL DEFAULT NULL,
										`finishTime` DATETIME NULL DEFAULT NULL,
										`recreate` TINYINT(1) NULL DEFAULT '0',
										`status` TEXT NULL DEFAULT NULL COLLATE 'utf8mb4_czech_ci',
										`active` INT(11) NULL DEFAULT '0',
										`errorCount` INT(11) NOT NULL DEFAULT '0',
										`errorDetail` LONGTEXT NOT NULL COLLATE 'utf8mb4_czech_ci',
										PRIMARY KEY (`id`) USING BTREE,
										INDEX `mutationId` (`mutationId`) USING BTREE,
										CONSTRAINT `elastic_search_index_ibfk_2` FOREIGN KEY (`mutationId`) REFERENCES `mutation` (`id`) ON UPDATE RESTRICT ON DELETE SET NULL
)
	COLLATE='utf8mb4_czech_ci'
ENGINE=InnoDB
AUTO_INCREMENT=1
;
