

CREATE TABLE IF NOT EXISTS `alias` (
	`id` int(11) NOT NULL AUTO_INCREMENT,
	`alias` varchar(120) CHARACTER SET utf8mb4 NOT NULL,
	`module` varchar(30) NOT NULL,
	`referenceId` int(11) NOT NULL,
	`mutationId` int(11) NOT NULL DEFAULT '1',
	PRIMARY KEY (`id`),
	UNIQUE KEY `alias_lg` (`alias`,`mutationId`),
	UNIQUE KEY `idref_modul_lg` (`referenceId`,`module`,`mutationId`) USING BTREE,
	KEY `idref` (`referenceId`),
	KEY `mutationId` (`mutationId`),
	KEY `modul` (`module`) USING BTREE,
	CONSTRAINT `alias_ibfk_1` FOREIGN KEY (`mutationId`) REFERENCES `mutation` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
	) ENGINE=InnoDB AUTO_INCREMENT=2226 DEFAULT CHARSET=utf8;



