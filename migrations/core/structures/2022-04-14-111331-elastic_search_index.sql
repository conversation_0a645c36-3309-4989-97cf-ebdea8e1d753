
CREATE TABLE IF NOT EXISTS `elastic_search_index` (
	`id` int(11) NOT NULL AUTO_INCREMENT,
	`mutationId` int(11) DEFAULT NULL,
	`type` varchar(255) COLLATE utf8mb4_czech_ci NOT NULL,
	`createdTime` datetime DEFAULT NULL,
	`startTime` datetime DEFAULT NULL,
	`finishTime` datetime DEFAULT NULL,
	`delete` tinyint(1) DEFAULT '0',
	`recreate` tinyint(1) DEFAULT '0',
	`status` text COLLATE utf8mb4_czech_ci,
	`active` int(11) DEFAULT '0',
	PRIMARY KEY (`id`),
	KEY `mutationId` (`mutationId`),
	CONSTRAINT `elastic_search_index_ibfk_2` FOREIGN KEY (`mutationId`) REFERENCES `mutation` (`id`) ON DELETE SET NULL
	) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_czech_ci;
