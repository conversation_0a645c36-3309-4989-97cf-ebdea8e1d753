CREATE TABLE `holiday`
(
	`id`         int(11)                            NOT NULL AUTO_INCREMENT,
	`name`       varchar(250) COLLATE utf8_czech_ci NOT NULL,
	`publicFrom` date                               NOT NULL,
	`publicTo`   date                               NOT NULL,
	`created`    datetime                           NOT NULL,
	`public`     int(11)                            NOT NULL,
	PRIMARY KEY (`id`),
	KEY `public_publicTo_publicFrom` (`public`, `publicTo`, `publicFrom`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8
  COLLATE = utf8_czech_ci;
