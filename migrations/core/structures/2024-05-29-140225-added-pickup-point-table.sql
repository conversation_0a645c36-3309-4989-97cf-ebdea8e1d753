-- Adminer 4.8.1 MySQL 10.6.12-<PERSON>D<PERSON> dump

SET NAMES utf8;
SET time_zone = '+00:00';
SET foreign_key_checks = 0;
SET sql_mode = 'NO_AUTO_VALUE_ON_ZERO';

SET NAMES utf8mb4;

DROP TABLE IF EXISTS `pickup_point`;
CREATE TABLE `pickup_point` (
								`id` int(11) NOT NULL AUTO_INCREMENT,
								`deliveryMethodId` int(11) DEFAULT NULL,
								`extId` varchar(255) NOT NULL,
								`syncTime` datetime DEFAULT NULL,
								`name` varchar(255) NOT NULL,
								`address` varchar(255) DEFAULT NULL,
								`price` decimal(10,4) DEFAULT NULL,
								`lat` varchar(50) DEFAULT NULL,
								`lng` varchar(50) DEFAULT NULL,
								`openingHours` text DEFAULT NULL,
								`image` text DEFAULT NULL,
								PRIMARY <PERSON>EY (`id`),
								UNIQUE KEY `extId` (`extId`),
								<PERSON><PERSON>Y `deliveryMethodId` (`deliveryMethodId`),
								CONSTRAINT `pickup_point_ibfk_1` FOREIGN KEY (`deliveryMethodId`) REFERENCES `delivery_method` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci;


-- 2024-05-29 12:00:55
