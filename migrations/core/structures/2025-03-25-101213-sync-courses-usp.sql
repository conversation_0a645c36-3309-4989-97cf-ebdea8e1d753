ALTER TABLE `mutation`
	ADD `isoCode` varchar(250) COLLATE 'utf8mb4_unicode_520_ci' NULL AFTER `langCode`;
UPDATE `mutation` SET `isoCode` = 'cs_CZ' WHERE `id` = '1';

ALTER TABLE `class_event`
	ADD `extId` varchar(20) NULL AFTER `id`;
ALTER TABLE `class_section`
	ADD `extId` varchar(20) NULL AFTER `id`;

CREATE TABLE `usp` (
					   `id` int NOT NULL AUTO_INCREMENT PRIMARY KEY,
					   `extId` varchar(20) NULL,
					   `mutationId` int(11) NOT NULL,
					   `name` varchar(255) NOT NULL,
					   FOREIGN KEY (`mutationId`) REFERENCES `mutation` (`id`)
);

ALTER TABLE `usp`
	ADD `extIdRaw` varchar(20) COLLATE 'utf8mb4_unicode_520_ci' NULL AFTER `extId`;
