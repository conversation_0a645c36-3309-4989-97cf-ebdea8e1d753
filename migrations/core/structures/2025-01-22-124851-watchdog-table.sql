-- Adminer 4.8.1 MySQL 10.6.12-MariaDB dump

SET NAMES utf8;
SET time_zone = '+00:00';
SET foreign_key_checks = 0;
SET sql_mode = 'NO_AUTO_VALUE_ON_ZERO';

SET NAMES utf8mb4;

DROP TABLE IF EXISTS `watchdog`;
CREATE TABLE `watchdog` (
							`id` int(11) NOT NULL AUTO_INCREMENT,
							`createdAt` datetime NOT NULL,
							`email` varchar(255) NOT NULL,
							`productId` int(11) NOT NULL,
							PRIMARY KEY (`id`),
							KEY `productId` (`productId`),
							CONSTRAINT `watchdog_ibfk_1` FOREIGN KEY (`productId`) REFERENCES `product` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci;


-- 2025-01-22 11:50:15
