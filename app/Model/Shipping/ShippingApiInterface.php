<?php declare(strict_types=1);

namespace App\Model\Shipping;

interface ShippingApiInterface
{
    /**
     * Get parcel status by parcel code
     *
     * @param string $parcelCode Parcel tracking number
     * @return string Parcel status (constant from PARCEL_STATUS_*)
     */
    public function getStatus(string $parcelCode): string;

    /**
     * Check if parcel is delivered
     *
     * @param string $parcelCode Parcel tracking number
     * @return bool True if parcel is delivered
     */
    public function isDelivered(string $parcelCode): bool;

    /**
     * Get carrier name
     *
     * @return string
     */
    public function getCarrierName(): string;

    /**
     * Get all available parcel statuses for this carrier
     *
     * @return array<string, string> Array of statuses [constant => description]
     */
    public function getAvailableStatuses(): array;
}
