<?php declare(strict_types=1);

namespace App\Model\Shipping;

class Ppl<PERSON>pi extends AbstractShippingApi
{
    /**
     * {@inheritdoc}
     */
    public function getStatus(string $parcelCode): string
    {
        // TODO: Implement PPL API call
        $rawData = $this->getParcelInfoFromApi($parcelCode);

        // TODO: Process response and map to general status
        $carrierStatus = $rawData['status'] ?? 'unknown';

        return $this->mapCarrierStatusToGeneral($carrierStatus);
    }

    /**
     * {@inheritdoc}
     */
    public function getCarrierName(): string
    {
        return 'PPL';
    }

    /**
     * {@inheritdoc}
     */
    protected function mapCarrierStatusToGeneral(string $carrierStatus): string
    {
        // TODO: Implement mapping of PPL specific statuses to general statuses
        $mapping = [
            // Examples - will be filled according to actual API statuses
            'created' => self::PARCEL_STATUS_CREATED,
            'picked_up' => self::PARCEL_STATUS_PICKED_UP,
            'in_transit' => self::PARCEL_STATUS_IN_TRANSIT,
            'out_for_delivery' => self::PARCEL_STATUS_OUT_FOR_DELIVERY,
            'delivered' => self::PARCEL_STATUS_DELIVERED,
            'delivery_attempt_failed' => self::PARCEL_STATUS_DELIVERY_FAILED,
            'returned' => self::PARCEL_STATUS_RETURNED,
        ];

        return $mapping[$carrierStatus] ?? self::PARCEL_STATUS_UNKNOWN;
    }

    /**
     * {@inheritdoc}
     */
    protected function getParcelInfoFromApi(string $parcelCode): array
    {
        // TODO: Implement actual PPL API call
        // Use $this->config for API keys, URLs etc.

        return [
            'status' => 'unknown',
            'parcel_code' => $parcelCode,
        ];
    }
}
