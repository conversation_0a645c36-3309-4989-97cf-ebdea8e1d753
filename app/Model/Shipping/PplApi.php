<?php declare(strict_types=1);

namespace App\Model\Shipping;

class PplApi extends AbstractShippingApi
{
    /**
     * {@inheritdoc}
     */
    public function getStatus(string $parcelCode): string
    {
        // TODO: Implementovat volání PPL API
        $rawData = $this->callCarrierApi($parcelCode);
        
        // TODO: Zpracovat odpověď a mapovat na obecný stav
        $carrierStatus = $rawData['status'] ?? 'unknown';
        
        return $this->mapCarrierStatusToGeneral($carrierStatus);
    }

    /**
     * {@inheritdoc}
     */
    public function getCarrierName(): string
    {
        return 'PPL';
    }

    /**
     * {@inheritdoc}
     */
    protected function mapCarrierStatusToGeneral(string $carrierStatus): string
    {
        // TODO: Implementovat mapování specifických stavů PPL na obecné stavy
        $mapping = [
            // Příklady - budou doplněny podle skutečných stavů z API
            'created' => self::PARCEL_STATUS_CREATED,
            'picked_up' => self::PARCEL_STATUS_PICKED_UP,
            'in_transit' => self::PARCEL_STATUS_IN_TRANSIT,
            'out_for_delivery' => self::PARCEL_STATUS_OUT_FOR_DELIVERY,
            'delivered' => self::PARCEL_STATUS_DELIVERED,
            'delivery_attempt_failed' => self::PARCEL_STATUS_DELIVERY_FAILED,
            'returned' => self::PARCEL_STATUS_RETURNED,
        ];

        return $mapping[$carrierStatus] ?? self::PARCEL_STATUS_UNKNOWN;
    }

    /**
     * {@inheritdoc}
     */
    protected function callCarrierApi(string $parcelCode): array
    {
        // TODO: Implementovat skutečné volání PPL API
        // Použít $this->config pro API klíče, URL atd.
        
        return [
            'status' => 'unknown',
            'parcel_code' => $parcelCode,
        ];
    }
}
