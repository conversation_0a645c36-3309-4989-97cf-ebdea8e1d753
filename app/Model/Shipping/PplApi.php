<?php declare(strict_types=1);

namespace App\Model\Shipping;

class Ppl<PERSON>pi extends AbstractShippingApi
{
    /**
     * {@inheritdoc}
     */
    public function getStatus(string $parcelCode): string
    {
        $rawData = $this->getParcelInfoFromApi($parcelCode);

        // Extract status from PPL API response structure
        $carrierStatus = $rawData['tracking']['status'] ?? 'unknown';

        return $this->mapCarrierStatusToGeneral($carrierStatus);
    }

    /**
     * {@inheritdoc}
     */
    public function getCarrierName(): string
    {
        return 'PPL';
    }

    /**
     * {@inheritdoc}
     */
    protected function mapCarrierStatusToGeneral(string $carrierStatus): string
    {
        // PPL API status mapping based on their documentation
        $mapping = [
            'CREATED' => self::PARCEL_STATUS_CREATED,
            'PICKED_UP' => self::PARCEL_STATUS_PICKED_UP,
            'IN_TRANSIT' => self::PARCEL_STATUS_IN_TRANSIT,
            'OUT_FOR_DELIVERY' => self::PARCEL_STATUS_OUT_FOR_DELIVERY,
            'DELIVERED' => self::PARCEL_STATUS_DELIVERED,
            'DELIVERY_FAILED' => self::PARCEL_STATUS_DELIVERY_FAILED,
            'RETURNED' => self::PARCEL_STATUS_RETURNED,
            'CANCELLED' => self::PARCEL_STATUS_CANCELLED,
        ];

        return $mapping[strtoupper($carrierStatus)] ?? self::PARCEL_STATUS_UNKNOWN;
    }

    /**
     * {@inheritdoc}
     */
    protected function getParcelInfoFromApi(string $parcelCode): array
    {
        $url = rtrim($this->config['apiUrl'], '/') . '/shipments/' . $parcelCode . '/tracking';

        $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_TIMEOUT => $this->config['timeout'] ?? 30,
            CURLOPT_HTTPHEADER => [
                'Content-Type: application/json',
                'Accept: application/json',
            ],
            CURLOPT_USERPWD => $this->config['username'] . ':' . $this->config['password'],
            CURLOPT_HTTPAUTH => CURL_HTTPAUTH_BASIC,
        ]);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);

        if ($error) {
            throw new \RuntimeException("PPL API curl error: {$error}");
        }

        if ($httpCode !== 200) {
            throw new \RuntimeException("PPL API returned HTTP {$httpCode}");
        }

        $data = json_decode($response, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new \RuntimeException("PPL API returned invalid JSON");
        }

        return $data;
    }
}
