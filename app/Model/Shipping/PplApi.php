<?php declare(strict_types=1);

namespace App\Model\Shipping;

use App\Model\Shipping\Exception\PacketNotFoundException;

class PplApi extends AbstractShippingApi
{
    // PPL status codes
    public const FORWARD_TO_PARTNER_CODE = 720;
    public const DELIVERED_CODE = 450;

    private ?\SoapClient $soapClient = null;
    /**
     * {@inheritdoc}
     */
    public function getStatus(string $parcelCode): string
    {
        $packages = $this->getPackages($parcelCode);

        if (empty($packages)) {
            return self::PARCEL_STATUS_UNKNOWN;
        }

        $package = $packages[0];

        // Get latest status from package statuses
        $latestStatus = $this->getLatestPackageStatus($package);

        return $this->mapCarrierStatusToGeneral((string)$latestStatus);
    }

    /**
     * {@inheritdoc}
     */
    public function getCarrierName(): string
    {
        return 'PPL';
    }

    /**
     * {@inheritdoc}
     */
    protected function mapCarrierStatusToGeneral(string $carrierStatus): string
    {
        // PPL API status mapping based on status codes
        $statusCode = (int)$carrierStatus;

        // Map PPL status codes to general statuses
        if ($statusCode === self::DELIVERED_CODE) {
            return self::PARCEL_STATUS_DELIVERED;
        }

        if ($statusCode === self::FORWARD_TO_PARTNER_CODE) {
            return self::PARCEL_STATUS_OUT_FOR_DELIVERY;
        }

        // Basic mapping for other common codes (these would need to be expanded based on PPL documentation)
        $mapping = [
            100 => self::PARCEL_STATUS_CREATED,
            200 => self::PARCEL_STATUS_PICKED_UP,
            300 => self::PARCEL_STATUS_IN_TRANSIT,
            400 => self::PARCEL_STATUS_OUT_FOR_DELIVERY,
            450 => self::PARCEL_STATUS_DELIVERED,
            500 => self::PARCEL_STATUS_DELIVERY_FAILED,
            600 => self::PARCEL_STATUS_RETURNED,
            700 => self::PARCEL_STATUS_CANCELLED,
            720 => self::PARCEL_STATUS_OUT_FOR_DELIVERY, // Forward to partner
        ];

        return $mapping[$statusCode] ?? self::PARCEL_STATUS_UNKNOWN;
    }

    /**
     * {@inheritdoc}
     */
    protected function getParcelInfoFromApi(string $parcelCode): array
    {
        $packages = $this->getPackages($parcelCode);
        return ['packages' => $packages];
    }

    /**
     * Get SOAP client instance
     */
    private function getSoapClient(): \SoapClient
    {
        if (!$this->soapClient) {
            $this->soapClient = new \SoapClient($this->config['apiUrl']);
        }

        return $this->soapClient;
    }

    /**
     * Login to PPL API and get auth token
     */
    private function login(): string
    {
        $soap = $this->getSoapClient();
        $result = $soap->Login([
            'Auth' => [
                'CustId' => $this->config['custId'],
                'UserName' => $this->config['username'],
                'Password' => $this->config['password']
            ]
        ]);

        if (!isset($result->LoginResult->AuthToken)) {
            throw new \RuntimeException("PPL API login failed");
        }

        return $result->LoginResult->AuthToken;
    }

    /**
     * Get packages from PPL API
     */
    private function getPackages(string $packNumber): array
    {
        $token = $this->login();

        $soap = $this->getSoapClient();
        $result = $soap->GetPackages([
            'Auth' => [
                'AuthToken' => $token
            ],
            'Filter' => [
                'PackNumbers' => [$packNumber]
            ]
        ]);

        $data = $result->GetPackagesResult->ResultData->MyApiPackageOut ?? [];
        if (!$data) {
            throw new PacketNotFoundException("No data found for package number {$packNumber}");
        }

        return is_array($data) ? $data : [$data];
    }

    /**
     * Get latest status from package statuses
     */
    private function getLatestPackageStatus($package): int
    {
        $statuses = $package->PackageStatuses->MyApiPackageOutStatus ?? [];

        if (empty($statuses)) {
            return 0; // Unknown status
        }

        // Get the latest status (assuming they are ordered by time)
        $latestStatus = is_array($statuses) ? end($statuses) : $statuses;

        return (int)($latestStatus->StaID ?? 0);
    }
}
