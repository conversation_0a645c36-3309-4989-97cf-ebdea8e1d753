<?php declare(strict_types=1);

namespace App\Model\Shipping;

abstract class AbstractShipping<PERSON><PERSON> implements ShippingApiInterface
{
    // General parcel statuses - constants for unification across carriers
    public const PARCEL_STATUS_UNKNOWN = 'unknown';
    public const PARCEL_STATUS_CREATED = 'created';
    public const PARCEL_STATUS_PICKED_UP = 'picked_up';
    public const PARCEL_STATUS_IN_TRANSIT = 'in_transit';
    public const PARCEL_STATUS_OUT_FOR_DELIVERY = 'out_for_delivery';
    public const PARCEL_STATUS_DELIVERED = 'delivered';
    public const PARCEL_STATUS_DELIVERY_FAILED = 'delivery_failed';
    public const PARCEL_STATUS_RETURNED = 'returned';
    public const PARCEL_STATUS_CANCELLED = 'cancelled';

    protected array $config;

    public function __construct(array $config)
    {
        $this->config = $config;
    }

    /**
     * {@inheritdoc}
     */
    public function isDelivered(string $parcelCode): bool
    {
        $status = $this->getStatus($parcelCode);
        return $status === self::PARCEL_STATUS_DELIVERED;
    }

    /**
     * {@inheritdoc}
     */
    public function getAvailableStatuses(): array
    {
        return [
            self::PARCEL_STATUS_UNKNOWN => 'Unknown status',
            self::PARCEL_STATUS_CREATED => 'Created',
            self::PARCEL_STATUS_PICKED_UP => 'Picked up',
            self::PARCEL_STATUS_IN_TRANSIT => 'In transit',
            self::PARCEL_STATUS_OUT_FOR_DELIVERY => 'Out for delivery',
            self::PARCEL_STATUS_DELIVERED => 'Delivered',
            self::PARCEL_STATUS_DELIVERY_FAILED => 'Delivery failed',
            self::PARCEL_STATUS_RETURNED => 'Returned',
            self::PARCEL_STATUS_CANCELLED => 'Cancelled',
        ];
    }

    /**
     * Map carrier specific status to general status
     *
     * @param string $carrierStatus Status from carrier
     * @return string General status
     */
    abstract protected function mapCarrierStatusToGeneral(string $carrierStatus): string;

    /**
     * Make API call to carrier
     *
     * @param string $parcelCode
     * @return array Raw data from carrier
     */
    abstract protected function callCarrierApi(string $parcelCode): array;
}
