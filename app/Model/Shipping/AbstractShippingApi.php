<?php declare(strict_types=1);

namespace App\Model\Shipping;

abstract class AbstractShippingApi implements ShippingApiInterface
{
    // Obecné stavy balíků - konstanty pro sjednocení např<PERSON> př<PERSON>ravci
    public const PARCEL_STATUS_UNKNOWN = 'unknown';
    public const PARCEL_STATUS_CREATED = 'created';
    public const PARCEL_STATUS_PICKED_UP = 'picked_up';
    public const PARCEL_STATUS_IN_TRANSIT = 'in_transit';
    public const PARCEL_STATUS_OUT_FOR_DELIVERY = 'out_for_delivery';
    public const PARCEL_STATUS_DELIVERED = 'delivered';
    public const PARCEL_STATUS_DELIVERY_FAILED = 'delivery_failed';
    public const PARCEL_STATUS_RETURNED = 'returned';
    public const PARCEL_STATUS_CANCELLED = 'cancelled';

    protected array $config;

    public function __construct(array $config)
    {
        $this->config = $config;
    }

    /**
     * {@inheritdoc}
     */
    public function isDelivered(string $parcelCode): bool
    {
        $status = $this->getStatus($parcelCode);
        return $status === self::PARCEL_STATUS_DELIVERED;
    }

    /**
     * {@inheritdoc}
     */
    public function getAvailableStatuses(): array
    {
        return [
            self::PARCEL_STATUS_UNKNOWN => 'Neznámý stav',
            self::PARCEL_STATUS_CREATED => 'Vytvořeno',
            self::PARCEL_STATUS_PICKED_UP => 'Převzato',
            self::PARCEL_STATUS_IN_TRANSIT => 'V přepravě',
            self::PARCEL_STATUS_OUT_FOR_DELIVERY => 'K doručení',
            self::PARCEL_STATUS_DELIVERED => 'Doručeno',
            self::PARCEL_STATUS_DELIVERY_FAILED => 'Doručení se nezdařilo',
            self::PARCEL_STATUS_RETURNED => 'Vráceno',
            self::PARCEL_STATUS_CANCELLED => 'Zrušeno',
        ];
    }

    /**
     * Mapuje specifický stav přepravce na obecný stav
     *
     * @param string $carrierStatus Stav od přepravce
     * @return string Obecný stav
     */
    abstract protected function mapCarrierStatusToGeneral(string $carrierStatus): string;

    /**
     * Provede API volání k přepravci
     *
     * @param string $parcelCode
     * @return array Raw data od přepravce
     */
    abstract protected function callCarrierApi(string $parcelCode): array;
}
