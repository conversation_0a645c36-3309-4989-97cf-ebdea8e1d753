<?php declare(strict_types=1);

namespace App\Model\Shipping;

use InvalidArgumentException;

class ShippingService
{
    // Constants for carrier types
    public const DELIVERY_MODE_PPL = 'ppl';
    public const DELIVERY_MODE_DPD = 'dpd';
    public const DELIVERY_MODE_ZASILKOVNA = 'zasilkovna';
    public const DELIVERY_MODE_BALIKOVNA = 'balikovna';

    private array $apis = [];
    private array $config;

    public function __construct(array $config)
    {
        $this->config = $config;
        $this->initializeApis();
    }

    /**
     * Get parcel status by delivery mode and parcel code
     *
     * @param string $deliveryMode Delivery type (constant DELIVERY_MODE_*)
     * @param string $parcelCode Parcel tracking number
     * @return string Parcel status
     * @throws InvalidArgumentException If delivery mode is not supported
     */
    public function getStatus(string $deliveryMode, string $parcelCode): string
    {
        $api = $this->getApiForDeliveryMode($deliveryMode);
        return $api->getStatus($parcelCode);
    }

    /**
     * Check if parcel is delivered
     *
     * @param string $deliveryMode Delivery type (constant DELIVERY_MODE_*)
     * @param string $parcelCode Parcel tracking number
     * @return bool True if parcel is delivered
     * @throws InvalidArgumentException If delivery mode is not supported
     */
    public function isDelivered(string $deliveryMode, string $parcelCode): bool
    {
        $api = $this->getApiForDeliveryMode($deliveryMode);
        return $api->isDelivered($parcelCode);
    }

    /**
     * Get list of supported delivery modes
     *
     * @return array<string, string> Array [constant => carrier name]
     */
    public function getSupportedDeliveryModes(): array
    {
        return [
            self::DELIVERY_MODE_PPL => 'PPL',
            self::DELIVERY_MODE_DPD => 'DPD',
            self::DELIVERY_MODE_ZASILKOVNA => 'Zásilkovna',
            self::DELIVERY_MODE_BALIKOVNA => 'Balíkovna',
        ];
    }

    /**
     * Get API object for given delivery mode
     *
     * @param string $deliveryMode
     * @return ShippingApiInterface
     * @throws InvalidArgumentException
     */
    private function getApiForDeliveryMode(string $deliveryMode): ShippingApiInterface
    {
        if (!isset($this->apis[$deliveryMode])) {
            throw new InvalidArgumentException("Unsupported delivery mode: {$deliveryMode}");
        }

        return $this->apis[$deliveryMode];
    }

    /**
     * Initialize API objects for all carriers
     */
    private function initializeApis(): void
    {
        $this->apis[self::DELIVERY_MODE_PPL] = new PplApi($this->config['ppl'] ?? []);
        $this->apis[self::DELIVERY_MODE_DPD] = new DpdApi($this->config['dpd'] ?? []);
        $this->apis[self::DELIVERY_MODE_ZASILKOVNA] = new ZasilkovnaApi($this->config['zasilkovna'] ?? []);
        $this->apis[self::DELIVERY_MODE_BALIKOVNA] = new BalíkovnaApi($this->config['balikovna'] ?? []);
    }
}
