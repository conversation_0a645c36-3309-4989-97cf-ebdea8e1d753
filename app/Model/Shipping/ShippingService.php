<?php declare(strict_types=1);

namespace App\Model\Shipping;

use InvalidArgumentException;

class ShippingService
{
    // Konstanty pro typy přepravců
    public const DELIVERY_MODE_PPL = 'ppl';
    public const DELIVERY_MODE_DPD = 'dpd';
    public const DELIVERY_MODE_ZASILKOVNA = 'zasilkovna';
    public const DELIVERY_MODE_BALIKOVNA = 'balikovna';

    private array $apis = [];
    private array $config;

    public function __construct(array $config)
    {
        $this->config = $config;
        $this->initializeApis();
    }

    /**
     * Získá stav balíku podle typu přepravy a čísla balíku
     *
     * @param string $deliveryMode Typ přepravy (konstanta DELIVERY_MODE_*)
     * @param string $parcelCode Číslo balíku
     * @return string Stav balíku
     * @throws InvalidArgumentException Pokud není podporován typ přepravy
     */
    public function getStatus(string $deliveryMode, string $parcelCode): string
    {
        $api = $this->getApiForDeliveryMode($deliveryMode);
        return $api->getStatus($parcelCode);
    }

    /**
     * Zjistí, zda je balík doručený
     *
     * @param string $deliveryMode Typ přepravy (konstanta DELIVERY_MODE_*)
     * @param string $parcelCode Číslo balíku
     * @return bool True pokud je balík doručený
     * @throws InvalidArgumentException Pokud není podporován typ přepravy
     */
    public function isDelivered(string $deliveryMode, string $parcelCode): bool
    {
        $api = $this->getApiForDeliveryMode($deliveryMode);
        return $api->isDelivered($parcelCode);
    }

    /**
     * Vrátí seznam podporovaných typů přepravy
     *
     * @return array<string, string> Pole [konstanta => název přepravce]
     */
    public function getSupportedDeliveryModes(): array
    {
        return [
            self::DELIVERY_MODE_PPL => 'PPL',
            self::DELIVERY_MODE_DPD => 'DPD',
            self::DELIVERY_MODE_ZASILKOVNA => 'Zásilkovna',
            self::DELIVERY_MODE_BALIKOVNA => 'Balíkovna',
        ];
    }

    /**
     * Vrátí API objekt pro daný typ přepravy
     *
     * @param string $deliveryMode
     * @return ShippingApiInterface
     * @throws InvalidArgumentException
     */
    private function getApiForDeliveryMode(string $deliveryMode): ShippingApiInterface
    {
        if (!isset($this->apis[$deliveryMode])) {
            throw new InvalidArgumentException("Nepodporovaný typ přepravy: {$deliveryMode}");
        }

        return $this->apis[$deliveryMode];
    }

    /**
     * Inicializuje API objekty pro všechny přepravce
     */
    private function initializeApis(): void
    {
        $this->apis[self::DELIVERY_MODE_PPL] = new PplApi($this->config['ppl'] ?? []);
        $this->apis[self::DELIVERY_MODE_DPD] = new DpdApi($this->config['dpd'] ?? []);
        $this->apis[self::DELIVERY_MODE_ZASILKOVNA] = new ZasilkovnaApi($this->config['zasilkovna'] ?? []);
        $this->apis[self::DELIVERY_MODE_BALIKOVNA] = new BalíkovnaApi($this->config['balikovna'] ?? []);
    }
}
