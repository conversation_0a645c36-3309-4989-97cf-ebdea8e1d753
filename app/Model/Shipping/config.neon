parameters:
	shipping:
		# PPL configuration
		ppl:
			apiUrl: 'https://api.ppl.cz/MyApi.svc?wsdl'
			custId: '%env.PPL_CUST_ID%'
			username: '%env.PPL_USERNAME%'
			password: '%env.PPL_PASSWORD%'
			timeout: 30

		# DPD configuration
		dpd:
			apiUrl: 'https://api.dpd.cz/v1/'
			username: '%env.DPD_USERNAME%'
			password: '%env.DPD_PASSWORD%'
			timeout: 30

		# Zásilkovna configuration
		zasilkovna:
			apiUrl: 'https://www.zasilkovna.cz/api/rest/'
			apiKey: '%env.ZASILKOVNA_API_KEY%'
			timeout: 30

		# Balíkovna configuration
		balikovna:
			apiUrl: 'https://www.balikovna.cz/api/v1/'
			apiKey: '%env.BALIKOVNA_API_KEY%'
			timeout: 30

services:
	# Main shipping service
	- App\Model\Shipping\ShippingService(%shipping%)

	# Individual API implementations (if needed separately)
	shipping.pplApi:
		factory: App\Model\Shipping\PplApi(%shipping.ppl%)

	shipping.dpdApi:
		factory: App\Model\Shipping\DpdApi(%shipping.dpd%)

	shipping.zasilkovnaApi:
		factory: App\Model\Shipping\ZasilkovnaApi(%shipping.zasilkovna%)

	shipping.balikovna:
		factory: App\Model\Shipping\BalíkovnaApi(%shipping.balikovna%)
