parameters:
	shipping:
		# PPL konfigurace
		ppl:
			apiUrl: 'https://api.ppl.cz/v1/'
			username: ''
			password: ''
			timeout: 30
			
		# DPD konfigurace
		dpd:
			apiUrl: 'https://api.dpd.cz/v1/'
			username: ''
			password: ''
			timeout: 30
			
		# Zásilkovna konfigurace
		zasilkovna:
			apiUrl: 'https://www.zasilkovna.cz/api/rest/'
			apiKey: ''
			timeout: 30
			
		# Balíkovna konfigurace
		balikovna:
			apiUrl: 'https://www.balikovna.cz/api/v1/'
			apiKey: ''
			timeout: 30

services:
	# Hlavní shipping servis
	- App\Model\Shipping\ShippingService(%shipping%)
	
	# Jednotlivé API implementace (pokud by byly potřeba samostatně)
	shipping.pplApi:
		factory: App\Model\Shipping\PplApi(%shipping.ppl%)
		
	shipping.dpdApi:
		factory: App\Model\Shipping\DpdApi(%shipping.dpd%)
		
	shipping.zasilkovnaApi:
		factory: App\Model\Shipping\ZasilkovnaApi(%shipping.zasilkovna%)
		
	shipping.balikovna:
		factory: App\Model\Shipping\BalíkovnaApi(%shipping.balikovna%)
