<?php

declare(strict_types=1);

use function Deployer\{after, host, import, run, set, task, upload, writeln};

import('recipe/common.php');
import('contrib/cachetool.php');

set('shared_files', [
	'app/config/config.local.neon',
	'www/sitemap.xml',
]);

set('shared_dirs', [
	'www/data',
	'www/exports',
	'nettelog/orders',
	'documents/fileLog',
]);

$env = getenv();
host('host')
	->set('hostname', $env['DEPLOYMENT_HOSTNAME'])
	->set('port', $env['DEPLOYMENT_PORT'] ?? 22)
	->set('remote_user', $env['DEPLOYMENT_USER'])
	->set('deploy_path', $env['DEPLOYMENT_PATH'])
	->set('cachetool_args', sprintf(
		'--web=SymfonyHttpClient --web-path=%s --web-url=%s',
		escapeshellarg(sprintf('%s/current/www', rtrim($env['DEPLOYMENT_PATH'], '/'))),
		escapeshellarg($env['DEPLOYMENT_URL']),
	))
	->set('php_version', $env['DEPLOYMENT_PHP_VERSION']);

const TASK_COPY_APPLICATION = 'copy-application';
task(TASK_COPY_APPLICATION, function (): void {
	$config = ['options' => [ "--exclude-from=excludeFromDeploy" ]];
	writeln('{{release_path}}');
	upload('.', '{{release_path}}', $config);
});

const TASK_CLEAR_CACHE = 'clear-cache';
task(TASK_CLEAR_CACHE, function (): void {
	run('{{bin/php}} {{release_path}}/bin/console contributte:cache:clean');
});

const TASK_RUN_MIGRATIONS = 'run-migrations';
task(TASK_RUN_MIGRATIONS, function () use ($env): void {
	if (isset($env['resetDataStorage']) && trim($env['resetDataStorage']) === '1') {
		writeln('Reset DB & Elasticsearch');
		run('{{bin/php}} {{release_path}}/bin/console migrations:reset');
		run('{{bin/php}} {{release_path}}/bin/console elastic:index:purge -f');
		run('{{bin/php}} {{release_path}}/bin/console elastic:index:create -psc');
	} else {
		writeln('Update DB scheme');
		run('{{bin/php}} {{release_path}}/bin/console migrations:continue');
	}

});

const TASK_RESTART_MESSENGER_CONSUMERS = 'restart-messenger-consumers';
task(TASK_RESTART_MESSENGER_CONSUMERS, function (): void {
	writeln('Restarting Messenger Consumers');
	run('sudo supervisorctl restart all');
});

const TASK_STOP_MESSENGER_CONSUMERS = 'stop-messenger-consumers';
task(TASK_STOP_MESSENGER_CONSUMERS, function (): void {
	writeln('Stopping Messenger Consumers');
	run('sudo supervisorctl stop all');
});

const TASK_SUPERVISOR_CONF = 'supervisor:conf';
task(TASK_SUPERVISOR_CONF, function () use ($env): void {
	$source = __DIR__.'/supervisor/'.$env['CI_ENVIRONMENT_NAME'].'.conf';
	if (isset($env['supervisorConfDir']) && file_exists($source)) {
		writeln('Symlink supervisor conf');
		$content = file_get_contents($source);
		preg_match_all('/\{\$([a-z_A-Z-]+)\}/m', $content, $matches);
		foreach ($matches[1] as $match) {
			$content = str_replace('{$'.$match.'}', $env[$match], $content);
		}

		$sourceFile = '{{release_path}}/supervisor/'.$env['CI_ENVIRONMENT_NAME'].'.conf';
		$targetFile = $env['supervisorConfDir'].'/'.$env['supervisorConfPrefix'].'-'.$env['CI_ENVIRONMENT_NAME'].'.conf';

		run('echo \'' . $content . '\' > ' . $sourceFile); // replace CI variables in .conf
		run('rm -f ' . $targetFile); // remove old symlink
		run('ln -s ' . $sourceFile . ' ' . $targetFile); // create new symlink
		run('sudo supervisorctl reread');  // reread supervisor
		run('sudo supervisorctl update');  // update supervisor
	}
});

const TASK_WARM_UP_CACHE = 'warm-up-cache';
task(TASK_WARM_UP_CACHE, function (): void {
	run('{{bin/php}} -d memory_limit=512M {{release_path}}/bin/console contributte:cache:generate');
});
const TASK_BROWSE_PAGES = 'browse_pages';
task(TASK_BROWSE_PAGES, function (): void {
	run('nohup {{bin/php}} -d memory_limit=512M {{release_path}}/bin/console dev:browse:pages > /dev/null 2>&1 &');
});

set('cachetool_url', 'https://github.com/gordalina/cachetool/releases/download/9.1.0/cachetool.phar');

task('deploy', [
	// prepare
	TASK_STOP_MESSENGER_CONSUMERS,
	'deploy:info',
	'deploy:setup',
	'deploy:lock',
	'deploy:release',

	// copy
	'deploy:copy_dirs',
	TASK_COPY_APPLICATION,

	// set up files and directories
	'deploy:shared',
	'deploy:writable',

	// todo maintenance mode?
	TASK_RUN_MIGRATIONS,
	TASK_WARM_UP_CACHE,

	// switch to deployed version
	'deploy:symlink',

	// cleanup
	'deploy:unlock',
	'deploy:cleanup',
	'deploy:success',
]);

after('deploy:symlink', TASK_SUPERVISOR_CONF);
after('deploy:symlink', TASK_CLEAR_CACHE);
after('deploy:symlink', 'cachetool:clear:opcache');
after('deploy:symlink', 'cachetool:clear:stat');
after('deploy:symlink', TASK_RESTART_MESSENGER_CONSUMERS);
#after('deploy:success', TASK_BROWSE_PAGES);

after('deploy:failed', 'deploy:unlock');
after('deploy:failed', TASK_RESTART_MESSENGER_CONSUMERS);
