
TODO:
-----

{* JS: prosim pri zatrhnuti checkboxu pridat do odkazu: &projectForm-p[]=valueCheckboxu *}
						{*
						<a n:href="pdf!, projectId=>$userProject->id, p=>[14207, 8711]" class="pdfLink circle circle--lg tooltip js-form-action" >
							{('print')|icon}
							<span class="vhide">
								{_btn_export_product_list}
							</span>
						</a>

						<a n:href="UID|userAddToProject  p=>[14207, 8711]" class="item-icon item-icon--yl-40 item-icon--yl-50--md is-active js-fancybox-form">
							<span class="circle circle--lg">
								{('plus')|icon}
							</span>
							{_btn_add_to_project}
						</a>
						*}



mereni na starem webu

<!-- Google Tag Manager -->
<script>(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
})(window,document,'script','dataLayer','GTM-KBNF59V');</script>
<!-- End Google Tag Manager -->

</head>
    <body id="hp">


    ....



</body><!-- Google Tag Manager (noscript) -->
<noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-KBNF59V"
height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
<!-- End Google Tag Manager (noscript) --></html>





REF:
 - otestovat spravnou funkci getProductForReviewFormOrder v zakaznicke sekci (poté co bude predělána objednávka)




-zaklad:
_(“”) …upravit viz sushi - sign, user presenter !!!!
proverit posilani zapom. hesla
doprava a platba v side kosiku
proverit vytvoreni slozky pro generovani nahledu obrazku
cena dopravy a paltby zdarma od…
sitemap, robots
generator sitemap
[bank]pošlete [SUM] na číslo 123 s var. symbolem [NUMBER] [/BANK]
Hledaný výraz musí mít alespoň 2 znaky - hlasky
404

diskuzni forum
urcite stranky jen pro prihlasene

strom: pri smazani rodice se nesmazou podrodice
 		nedovolit mazani poku existuji potomci…
		TOHLE
		-jak Strom strane
		-tak Strom pripojenych akci
nedoolit mazani rootu
nette 2.3
	-filtry do neonu
	-this->context->neco => this->context->getService(’neco’)
kotva na formular:
	 $url = $this->link('this', array( 'do' => $name . self::NAME_SEPARATOR . 'submit', ) );
	 $form->setAction($url."#frm-courseForm");



Úpravy
------------------
-- nové Nette --
	OK-aktualizovat
	-zapnout debug bar v RS
	-filrovni objednavek - datum - vyber datumu nepřenese datum
	-RS - detai lobjenavky - lang stav objednavky
	OK-filrovni objednavek - setPrompt
	-RS: knihovna

	Překlady
	?-po vlozeni klice do šablony, kontrola zda již existuje, pokud ne, tak zalozit do langů
	-mazani polozek


	-hlasky - překlady
	OK-detail uzivatele v RS - error
	-tecky v LI seznamu

	odstraněno:
	-automatická class required  na labelu


-- práce s tree --
	-page vs tree


-- práce s parametry --
	-pro object zavolam getParam("UID/ID")
	-uid/id


-- identity map --
	-kolekce


-- další --
	-unikatnost uid v ramci stranek
	-složky v model


--------------------------------------------------------------

-param. filtr - multiselect hodnoty

-pokud neni produkt v kategorii -> error

-validace url, nezvládá #

-obrázky u stranek - pri prvnim prokliku, pripojit obrazek  - obrazek se nenahraje, az pak se zobrazi


Newsletter
----------
OK-registrace do newsletteru
	-teď se uloží e-mail do spešl tabulky
		OK-tabulku zachovam - prejmenuji - newsletter_emails
		OK-jedine co je potreba udelat, aby kdyz si clovek edituje profil, tak aby ses podival, jestli odebira NL a podle toho mu zaskrtl priznak
    	OK-a pak zpracoval pokud zaskrnte/odskrtne odber

OK-odhlasovani - na stránku, email + hash?

-RS:
	OK-sekce v RS
		OK-vypis
		OK-vypis odeslanych
       	OK-vypis odesilanych
       	OK-podminky: pro odeslane a odesilane newslettery skryji tlacitko odeslat
       	OK-hlasky:
       		-odeslani test. news.
       		-odeslani newsl.
       		-vygenerovani voucheru
		OK-editace
		OK-zalozeni
		OK-smazani
	OK-DB
		OK-tabulka newsletter
			OK-nazev, obsah, predmět, atd....
		OK-tabulka newsletter_send
			OK-email, id newsletteru, isSend

	OK-newsletter
		OK-html editor
	 	OK-odesilatel, jmeno, eamil z configu
		OK-emogrifier...viz emaily

	OK-nahled + šablona
	OK-test. odeslani
	NE-utm parametry

	OK-odeslani - naplneni fronty pro odeslani
		OK-nactu vsechny dostupne emaily a vlozim je do fronty

OK-cron na odesilani - odesle kazdych 5 minut X emailu z fronty
	OK-nactu X radku z tabulky ...queue a odešlu, označím jako sent=1 a vlozím čas -
		OK-pri odesilani vygeneruju hash a ulozim k polozce do fronty
	OK-zjisti zda byly odeslany vsechny polozky a oznacim newsletter jako finished

# crony - newslettery
*/5 * * * *        /cron/newsletter/send
20 * * * *        /cron/newsletter/finish



Slevové kódy
------------
	OK-expirace kodu:
		OK-1.po vytvoreni objednavky, pridat priznak ze byl kod pouzit, (pokud je na jedno pouziti)
			-v pripade zruseni objednavky - musim taky uvolnit kod....
		2.pouzite kody nekam zapsat a overovat kod jestli jiz neni pouzit
			-

	-objednavka:
		OK-novy typ položky - voucher, cena minus,
		-nekam ulozim i pouzity kod

