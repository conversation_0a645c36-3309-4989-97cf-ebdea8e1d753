## delete huge rows in table
-- Optional:  SET GLOBAL innodb_file_per_table = ON;
   CREATE TABLE New LIKE Main;
   -- Optional:  ALTER TABLE New ADD PARTITION BY RANGE ...;
   -- Do this INSERT..SELECT all at once, or with chunking:
   INSERT INTO New
      SELECT * FROM Main
         WHERE ...;  -- just the rows you want to keep
   RENAME TABLE main TO Old, New TO Main;
   DROP TABLE Old;   -- Space freed up here


## reset auto increment

   SET  @num := 0;
   UPDATE import_cache SET id = @num := (@num+1);
   ALTER TABLE import_cache AUTO_INCREMENT =1;
