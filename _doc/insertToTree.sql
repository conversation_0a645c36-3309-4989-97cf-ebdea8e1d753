
begin;

DELETE FROM `tree`
WHERE `parentId` = '199' AND ((`id` = '203') OR (`id` = '204'));


SET @parentId=1,
    @level=1,
    @path='1|',
    @sort=1,
    @last=0,
    @public=1,
    @uid='',
    @created= 1,
    @edited= 1,
    @template= 'Pages:default',
    @name= 'Podpora',
    @nameTitle= 'Podpora',
    @nameAnchor= 'Podpora',
    @annotation= 'Podpora',
    @description= 'Podpora',
    @keywords= 'Podpora',
    @content= 'Podpora',
    @hideFirstImage= 0,
    @hideCrossroad= 0,
    @showContactForm= 0,
    @links= '',
    @videos= '',

    @alias= 'podpora';


INSERT INTO `tree` (`parentId`, `level`, `path`, `sort`, `last`, `public`, `uid`, `created`, `createdTime`, `edited`,
`editedTime`, `template`, `publicFrom`, `publicTo`, `name`, `nameTitle`, `nameAnchor`, `annotation`, `description`,
`keywords`, `content`, `hideFirstImage`, `links`, `videos`, `hideCrossroad`, `showContactForm`)
VALUES (@parentId, @level, @path, @sort, @last, @public, @uid, @created, NOW(), @edited, NOW(), @template, NOW(), NOW(),
@name, @nameTitle, @nameAnchor, @annotation,@description, @keywords, @content, @hideFirstImage, @links, @videos,
@hideCrossroad, @showContactForm);

INSERT INTO `alias` (`alias`, `modul`, `idref`, `lg`)
VALUES (@alias, 'tree', LAST_INSERT_ID(), 'cs');






SET @parentId=308,
    @level=1,
    @path='1|308|',
    @sort=1,
    @last=0,
    @public=1,
    @uid='',
    @created= 1,
    @edited= 1,
    @template= 'Pages:default',
    @name= 'Nástroje podpory ke stažení',
    @nameTitle= 'Nástroje podpory ke stažení',
    @nameAnchor= 'Nástroje podpory ke stažení',
    @annotation= 'Nástroje podpory ke stažení',
    @description= 'Nástroje podpory ke stažení',
    @keywords= 'Nástroje podpory ke stažení',
    @content= 'Nástroje podpory ke stažení',
    @hideFirstImage= 0,
    @hideCrossroad= 0,
    @showContactForm= 0,
    @links= '',
    @videos= '',

    @alias= 'nastroje-podpory-ke-stazeni';


INSERT INTO `tree` (`parentId`, `level`, `path`, `sort`, `last`, `public`, `uid`, `created`, `createdTime`, `edited`,
`editedTime`, `template`, `publicFrom`, `publicTo`, `name`, `nameTitle`, `nameAnchor`, `annotation`, `description`,
`keywords`, `content`, `hideFirstImage`, `links`, `videos`, `hideCrossroad`, `showContactForm`)
VALUES (@parentId, @level, @path, @sort, @last, @public, @uid, @created, NOW(), @edited, NOW(), @template, NOW(), NOW(),
@name, @nameTitle, @nameAnchor, @annotation,@description, @keywords, @content, @hideFirstImage, @links, @videos,
@hideCrossroad, @showContactForm);

INSERT INTO `alias` (`alias`, `modul`, `idref`, `lg`)
VALUES (@alias, 'tree', LAST_INSERT_ID(), 'cs');





SET @parentId=308,
    @level=1,
    @path='1|308|',
    @sort=1,
    @last=0,
    @public=1,
    @uid='',
    @created= 1,
    @edited= 1,
    @template= 'Pages:default',
    @name= 'LED technologie a Speciální zdroje',
    @nameTitle= 'LED technologie a Speciální zdroje',
    @nameAnchor= 'LED technologie a Speciální zdroje',
    @annotation= 'LED technologie a Speciální zdroje',
    @description= 'LED technologie a Speciální zdroje',
    @keywords= 'LED technologie a Speciální zdroje',
    @content= 'LED technologie a Speciální zdroje',
    @hideFirstImage= 0,
    @hideCrossroad= 0,
    @showContactForm= 0,
    @links= '',
    @videos= '',

    @alias= 'led-technologie-a-specialni-zdroje';


INSERT INTO `tree` (`parentId`, `level`, `path`, `sort`, `last`, `public`, `uid`, `created`, `createdTime`, `edited`,
`editedTime`, `template`, `publicFrom`, `publicTo`, `name`, `nameTitle`, `nameAnchor`, `annotation`, `description`,
`keywords`, `content`, `hideFirstImage`, `links`, `videos`, `hideCrossroad`, `showContactForm`)
VALUES (@parentId, @level, @path, @sort, @last, @public, @uid, @created, NOW(), @edited, NOW(), @template, NOW(), NOW(),
@name, @nameTitle, @nameAnchor, @annotation,@description, @keywords, @content, @hideFirstImage, @links, @videos,
@hideCrossroad, @showContactForm);

INSERT INTO `alias` (`alias`, `modul`, `idref`, `lg`)
VALUES (@alias, 'tree', LAST_INSERT_ID(), 'cs');





SET @parentId=308,
    @level=1,
    @path='1|308|',
    @sort=1,
    @last=0,
    @public=1,
    @uid='',
    @created= 1,
    @edited= 1,
    @template= 'Pages:default',
    @name= 'Human Centric Lighting',
    @nameTitle= 'Human Centric Lighting',
    @nameAnchor= 'Human Centric Lighting',
    @annotation= 'Human Centric Lighting',
    @description= 'Human Centric Lighting',
    @keywords= 'Human Centric Lighting',
    @content= 'Human Centric Lighting',
    @hideFirstImage= 0,
    @hideCrossroad= 0,
    @showContactForm= 0,
    @links= '',
    @videos= '',

    @alias= 'human-centric-lighting';


INSERT INTO `tree` (`parentId`, `level`, `path`, `sort`, `last`, `public`, `uid`, `created`, `createdTime`, `edited`,
`editedTime`, `template`, `publicFrom`, `publicTo`, `name`, `nameTitle`, `nameAnchor`, `annotation`, `description`,
`keywords`, `content`, `hideFirstImage`, `links`, `videos`, `hideCrossroad`, `showContactForm`)
VALUES (@parentId, @level, @path, @sort, @last, @public, @uid, @created, NOW(), @edited, NOW(), @template, NOW(), NOW(),
@name, @nameTitle, @nameAnchor, @annotation,@description, @keywords, @content, @hideFirstImage, @links, @videos,
@hideCrossroad, @showContactForm);

INSERT INTO `alias` (`alias`, `modul`, `idref`, `lg`)
VALUES (@alias, 'tree', LAST_INSERT_ID(), 'cs');






SET @parentId=199,
    @level=2,
    @path='1|199|',
    @sort=1,
    @last=0,
    @public=1,
    @uid='',
    @created= 1,
    @edited= 1,
    @template= 'Pages:default',
    @name= 'Společnost',
    @nameTitle= 'Společnost',
    @nameAnchor= 'Společnost',
    @annotation= 'Společnost',
    @description= 'Společnost',
    @keywords= 'Společnost',
    @content= 'Společnost',
    @hideFirstImage= 0,
    @hideCrossroad= 0,
    @showContactForm= 0,
    @links= '',
    @videos= '',

    @alias= 'spolecnost';


INSERT INTO `tree` (`parentId`, `level`, `path`, `sort`, `last`, `public`, `uid`, `created`, `createdTime`, `edited`,
`editedTime`, `template`, `publicFrom`, `publicTo`, `name`, `nameTitle`, `nameAnchor`, `annotation`, `description`,
`keywords`, `content`, `hideFirstImage`, `links`, `videos`, `hideCrossroad`, `showContactForm`)
VALUES (@parentId, @level, @path, @sort, @last, @public, @uid, @created, NOW(), @edited, NOW(), @template, NOW(), NOW(),
@name, @nameTitle, @nameAnchor, @annotation,@description, @keywords, @content, @hideFirstImage, @links, @videos,
@hideCrossroad, @showContactForm);

INSERT INTO `alias` (`alias`, `modul`, `idref`, `lg`)
VALUES (@alias, 'tree', LAST_INSERT_ID(), 'cs');



SET @parentId=199,
    @level=2,
    @path='1|199|',
    @sort=1,
    @last=0,
    @public=1,
    @uid='',
    @created= 1,
    @edited= 1,
    @template= 'Pages:default',
    @name= 'Lidé',
    @nameTitle= 'Lidé',
    @nameAnchor= 'Lidé',
    @annotation= 'Lidé',
    @description= 'Lidé',
    @keywords= 'Lidé',
    @content= 'Lidé',
    @hideFirstImage= 0,
    @hideCrossroad= 0,
    @showContactForm= 0,
    @links= '',
    @videos= '',

    @alias= 'lide';


INSERT INTO `tree` (`parentId`, `level`, `path`, `sort`, `last`, `public`, `uid`, `created`, `createdTime`, `edited`,
`editedTime`, `template`, `publicFrom`, `publicTo`, `name`, `nameTitle`, `nameAnchor`, `annotation`, `description`,
`keywords`, `content`, `hideFirstImage`, `links`, `videos`, `hideCrossroad`, `showContactForm`)
VALUES (@parentId, @level, @path, @sort, @last, @public, @uid, @created, NOW(), @edited, NOW(), @template, NOW(), NOW(),
@name, @nameTitle, @nameAnchor, @annotation,@description, @keywords, @content, @hideFirstImage, @links, @videos,
@hideCrossroad, @showContactForm);

INSERT INTO `alias` (`alias`, `modul`, `idref`, `lg`)
VALUES (@alias, 'tree', LAST_INSERT_ID(), 'cs');





SET @parentId=199,
    @level=2,
    @path='1|199|',
    @sort=1,
    @last=0,
    @public=1,
    @uid='',
    @created= 1,
    @edited= 1,
    @template= 'Pages:default',
    @name= 'Partneři',
    @nameTitle= 'Partneři',
    @nameAnchor= 'Partneři',
    @annotation= 'Partneři',
    @description= 'Partneři',
    @keywords= 'Partneři',
    @content= 'Partneři',
    @hideFirstImage= 0,
    @hideCrossroad= 0,
    @showContactForm= 0,
    @links= '',
    @videos= '',

    @alias= 'partneři';


INSERT INTO `tree` (`parentId`, `level`, `path`, `sort`, `last`, `public`, `uid`, `created`, `createdTime`, `edited`,
`editedTime`, `template`, `publicFrom`, `publicTo`, `name`, `nameTitle`, `nameAnchor`, `annotation`, `description`,
`keywords`, `content`, `hideFirstImage`, `links`, `videos`, `hideCrossroad`, `showContactForm`)
VALUES (@parentId, @level, @path, @sort, @last, @public, @uid, @created, NOW(), @edited, NOW(), @template, NOW(), NOW(),
@name, @nameTitle, @nameAnchor, @annotation,@description, @keywords, @content, @hideFirstImage, @links, @videos,
@hideCrossroad, @showContactForm);

INSERT INTO `alias` (`alias`, `modul`, `idref`, `lg`)
VALUES (@alias, 'tree', LAST_INSERT_ID(), 'cs');


SET @parentId=314,
    @level=3,
    @path='1|199|314|',
    @sort=1,
    @last=0,
    @public=1,
    @uid='',
    @created= 1,
    @edited= 1,
    @template= 'Pages:default',
    @name= 'Zákaznický servis',
    @nameTitle= 'Zákaznický servis',
    @nameAnchor= 'Zákaznický servis',
    @annotation= 'Zákaznický servis',
    @description= 'Zákaznický servis',
    @keywords= 'Zákaznický servis',
    @content= 'Zákaznický servis',
    @hideFirstImage= 0,
    @hideCrossroad= 0,
    @showContactForm= 0,
    @links= '',
    @videos= '',

    @alias= 'zakaznicky-servis';


INSERT INTO `tree` (`parentId`, `level`, `path`, `sort`, `last`, `public`, `uid`, `created`, `createdTime`, `edited`,
`editedTime`, `template`, `publicFrom`, `publicTo`, `name`, `nameTitle`, `nameAnchor`, `annotation`, `description`,
`keywords`, `content`, `hideFirstImage`, `links`, `videos`, `hideCrossroad`, `showContactForm`)
VALUES (@parentId, @level, @path, @sort, @last, @public, @uid, @created, NOW(), @edited, NOW(), @template, NOW(), NOW(),
@name, @nameTitle, @nameAnchor, @annotation,@description, @keywords, @content, @hideFirstImage, @links, @videos,
@hideCrossroad, @showContactForm);

INSERT INTO `alias` (`alias`, `modul`, `idref`, `lg`)
VALUES (@alias, 'tree', LAST_INSERT_ID(), 'cs');







SET @parentId=314,
    @level=3,
    @path='1|199|314|',
    @sort=1,
    @last=0,
    @public=1,
    @uid='',
    @created= 1,
    @edited= 1,
    @template= 'Pages:default',
    @name= 'HR',
    @nameTitle= 'HR',
    @nameAnchor= 'HR',
    @annotation= 'HR',
    @description= 'HR',
    @keywords= 'HR',
    @content= 'HR',
    @hideFirstImage= 0,
    @hideCrossroad= 0,
    @showContactForm= 0,
    @links= '',
    @videos= '',

    @alias= 'hr';


INSERT INTO `tree` (`parentId`, `level`, `path`, `sort`, `last`, `public`, `uid`, `created`, `createdTime`, `edited`,
`editedTime`, `template`, `publicFrom`, `publicTo`, `name`, `nameTitle`, `nameAnchor`, `annotation`, `description`,
`keywords`, `content`, `hideFirstImage`, `links`, `videos`, `hideCrossroad`, `showContactForm`)
VALUES (@parentId, @level, @path, @sort, @last, @public, @uid, @created, NOW(), @edited, NOW(), @template, NOW(), NOW(),
@name, @nameTitle, @nameAnchor, @annotation,@description, @keywords, @content, @hideFirstImage, @links, @videos,
@hideCrossroad, @showContactForm);

INSERT INTO `alias` (`alias`, `modul`, `idref`, `lg`)
VALUES (@alias, 'tree', LAST_INSERT_ID(), 'cs');



SET @parentId=314,
    @level=3,
    @path='1|199|314|',
    @sort=1,
    @last=0,
    @public=1,
    @uid='',
    @created= 1,
    @edited= 1,
    @template= 'Pages:default',
    @name= 'Marketing',
    @nameTitle= 'Marketing',
    @nameAnchor= 'Marketing',
    @annotation= 'Marketing',
    @description= 'Marketing',
    @keywords= 'Marketing',
    @content= 'Marketing',
    @hideFirstImage= 0,
    @hideCrossroad= 0,
    @showContactForm= 0,
    @links= '',
    @videos= '',

    @alias= 'Marketing';


INSERT INTO `tree` (`parentId`, `level`, `path`, `sort`, `last`, `public`, `uid`, `created`, `createdTime`, `edited`,
`editedTime`, `template`, `publicFrom`, `publicTo`, `name`, `nameTitle`, `nameAnchor`, `annotation`, `description`,
`keywords`, `content`, `hideFirstImage`, `links`, `videos`, `hideCrossroad`, `showContactForm`)
VALUES (@parentId, @level, @path, @sort, @last, @public, @uid, @created, NOW(), @edited, NOW(), @template, NOW(), NOW(),
@name, @nameTitle, @nameAnchor, @annotation,@description, @keywords, @content, @hideFirstImage, @links, @videos,
@hideCrossroad, @showContactForm);

INSERT INTO `alias` (`alias`, `modul`, `idref`, `lg`)
VALUES (@alias, 'tree', LAST_INSERT_ID(), 'cs');





SET @parentId=314,
    @level=3,
    @path='1|199|314|',
    @sort=1,
    @last=0,
    @public=1,
    @uid='',
    @created= 1,
    @edited= 1,
    @template= 'Pages:default',
    @name= 'Management',
    @nameTitle= 'Management',
    @nameAnchor= 'Management',
    @annotation= 'Management',
    @description= 'Management',
    @keywords= 'Management',
    @content= 'Management',
    @hideFirstImage= 0,
    @hideCrossroad= 0,
    @showContactForm= 0,
    @links= '',
    @videos= '',

    @alias= 'management';


INSERT INTO `tree` (`parentId`, `level`, `path`, `sort`, `last`, `public`, `uid`, `created`, `createdTime`, `edited`,
`editedTime`, `template`, `publicFrom`, `publicTo`, `name`, `nameTitle`, `nameAnchor`, `annotation`, `description`,
`keywords`, `content`, `hideFirstImage`, `links`, `videos`, `hideCrossroad`, `showContactForm`)
VALUES (@parentId, @level, @path, @sort, @last, @public, @uid, @created, NOW(), @edited, NOW(), @template, NOW(), NOW(),
@name, @nameTitle, @nameAnchor, @annotation,@description, @keywords, @content, @hideFirstImage, @links, @videos,
@hideCrossroad, @showContactForm);

INSERT INTO `alias` (`alias`, `modul`, `idref`, `lg`)
VALUES (@alias, 'tree', LAST_INSERT_ID(), 'cs');





SET @parentId=314,
    @level=1,
    @path='1|314|',
    @sort=1,
    @last=0,
    @public=1,
    @uid='',
    @created= 1,
    @edited= 1,
    @template= 'Pages:default',
    @name= 'Techická podpora',
    @nameTitle= 'Techická podpora',
    @nameAnchor= 'Techická podpora',
    @annotation= 'Techická podpora',
    @description= 'Techická podpora',
    @keywords= 'Techická podpora',
    @content= 'Techická podpora',
    @hideFirstImage= 0,
    @hideCrossroad= 0,
    @showContactForm= 0,
    @links= '',
    @videos= '',

    @alias= 'technicka-podpora';


INSERT INTO `tree` (`parentId`, `level`, `path`, `sort`, `last`, `public`, `uid`, `created`, `createdTime`, `edited`,
`editedTime`, `template`, `publicFrom`, `publicTo`, `name`, `nameTitle`, `nameAnchor`, `annotation`, `description`,
`keywords`, `content`, `hideFirstImage`, `links`, `videos`, `hideCrossroad`, `showContactForm`)
VALUES (@parentId, @level, @path, @sort, @last, @public, @uid, @created, NOW(), @edited, NOW(), @template, NOW(), NOW(),
@name, @nameTitle, @nameAnchor, @annotation,@description, @keywords, @content, @hideFirstImage, @links, @videos,
@hideCrossroad, @showContactForm);

INSERT INTO `alias` (`alias`, `modul`, `idref`, `lg`)
VALUES (@alias, 'tree', LAST_INSERT_ID(), 'cs');






SET @parentId=308,
    @level=1,
    @path='1|308|',
    @sort=1,
    @last=0,
    @public=1,
    @uid='',
    @created= 1,
    @edited= 1,
    @template= 'Pages:default',
    @name= 'Techická podpora',
    @nameTitle= 'Techická podpora',
    @nameAnchor= 'Techická podpora',
    @annotation= 'Techická podpora',
    @description= 'Techická podpora',
    @keywords= 'Techická podpora',
    @content= 'Techická podpora',
    @hideFirstImage= 0,
    @hideCrossroad= 0,
    @showContactForm= 0,
    @links= '',
    @videos= '',

    @alias= 'technicka-podpora-1';


INSERT INTO `tree` (`parentId`, `level`, `path`, `sort`, `last`, `public`, `uid`, `created`, `createdTime`, `edited`,
`editedTime`, `template`, `publicFrom`, `publicTo`, `name`, `nameTitle`, `nameAnchor`, `annotation`, `description`,
`keywords`, `content`, `hideFirstImage`, `links`, `videos`, `hideCrossroad`, `showContactForm`)
VALUES (@parentId, @level, @path, @sort, @last, @public, @uid, @created, NOW(), @edited, NOW(), @template, NOW(), NOW(),
@name, @nameTitle, @nameAnchor, @annotation,@description, @keywords, @content, @hideFirstImage, @links, @videos,
@hideCrossroad, @showContactForm);

INSERT INTO `alias` (`alias`, `modul`, `idref`, `lg`)
VALUES (@alias, 'tree', LAST_INSERT_ID(), 'cs');





SET @parentId=308,
    @level=1,
    @path='1|308|',
    @sort=1,
    @last=0,
    @public=1,
    @uid='',
    @created= 1,
    @edited= 1,
    @template= 'Pages:default',
    @name= 'Řešení na míru',
    @nameTitle= 'Řešení na míru',
    @nameAnchor= 'Řešení na míru',
    @annotation= 'Řešení na míru',
    @description= 'Řešení na míru',
    @keywords= 'Řešení na míru',
    @content= 'Řešení na míru',
    @hideFirstImage= 0,
    @hideCrossroad= 0,
    @showContactForm= 0,
    @links= '',
    @videos= '',

    @alias= 'reseni-na-miru';


INSERT INTO `tree` (`parentId`, `level`, `path`, `sort`, `last`, `public`, `uid`, `created`, `createdTime`, `edited`,
`editedTime`, `template`, `publicFrom`, `publicTo`, `name`, `nameTitle`, `nameAnchor`, `annotation`, `description`,
`keywords`, `content`, `hideFirstImage`, `links`, `videos`, `hideCrossroad`, `showContactForm`)
VALUES (@parentId, @level, @path, @sort, @last, @public, @uid, @created, NOW(), @edited, NOW(), @template, NOW(), NOW(),
@name, @nameTitle, @nameAnchor, @annotation,@description, @keywords, @content, @hideFirstImage, @links, @videos,
@hideCrossroad, @showContactForm);

INSERT INTO `alias` (`alias`, `modul`, `idref`, `lg`)
VALUES (@alias, 'tree', LAST_INSERT_ID(), 'cs');






SET @parentId=308,
    @level=1,
    @path='1|308|',
    @sort=1,
    @last=0,
    @public=1,
    @uid='',
    @created= 1,
    @edited= 1,
    @template= 'Pages:default',
    @name= 'Xpress program',
    @nameTitle= 'Xpress program',
    @nameAnchor= 'Xpress program',
    @annotation= 'Xpress program',
    @description= 'Xpress program',
    @keywords= 'Xpress program',
    @content= 'Xpress program',
    @hideFirstImage= 0,
    @hideCrossroad= 0,
    @showContactForm= 0,
    @links= '',
    @videos= '',

    @alias= 'xpress-program';


INSERT INTO `tree` (`parentId`, `level`, `path`, `sort`, `last`, `public`, `uid`, `created`, `createdTime`, `edited`,
`editedTime`, `template`, `publicFrom`, `publicTo`, `name`, `nameTitle`, `nameAnchor`, `annotation`, `description`,
`keywords`, `content`, `hideFirstImage`, `links`, `videos`, `hideCrossroad`, `showContactForm`)
VALUES (@parentId, @level, @path, @sort, @last, @public, @uid, @created, NOW(), @edited, NOW(), @template, NOW(), NOW(),
@name, @nameTitle, @nameAnchor, @annotation,@description, @keywords, @content, @hideFirstImage, @links, @videos,
@hideCrossroad, @showContactForm);

INSERT INTO `alias` (`alias`, `modul`, `idref`, `lg`)
VALUES (@alias, 'tree', LAST_INSERT_ID(), 'cs');



SET @parentId=308,
    @level=1,
    @path='1|308|',
    @sort=1,
    @last=0,
    @public=1,
    @uid='',
    @created= 1,
    @edited= 1,
    @template= 'Pages:default',
    @name= 'Co získáte registrací?',
    @nameTitle= 'Co získáte registrací?',
    @nameAnchor= 'Co získáte registrací?',
    @annotation= 'Co získáte registrací?',
    @description= 'Co získáte registrací?',
    @keywords= 'Co získáte registrací?',
    @content= 'Co získáte registrací?',
    @hideFirstImage= 0,
    @hideCrossroad= 0,
    @showContactForm= 0,
    @links= '',
    @videos= '',

    @alias= 'co-ziskate-registraci';


INSERT INTO `tree` (`parentId`, `level`, `path`, `sort`, `last`, `public`, `uid`, `created`, `createdTime`, `edited`,
`editedTime`, `template`, `publicFrom`, `publicTo`, `name`, `nameTitle`, `nameAnchor`, `annotation`, `description`,
`keywords`, `content`, `hideFirstImage`, `links`, `videos`, `hideCrossroad`, `showContactForm`)
VALUES (@parentId, @level, @path, @sort, @last, @public, @uid, @created, NOW(), @edited, NOW(), @template, NOW(), NOW(),
@name, @nameTitle, @nameAnchor, @annotation,@description, @keywords, @content, @hideFirstImage, @links, @videos,
@hideCrossroad, @showContactForm);

INSERT INTO `alias` (`alias`, `modul`, `idref`, `lg`)
VALUES (@alias, 'tree', LAST_INSERT_ID(), 'cs');




SET @parentId=308,
    @level=1,
    @path='1|308|',
    @sort=1,
    @last=0,
    @public=1,
    @uid='',
    @created= 1,
    @edited= 1,
    @template= 'Pages:default',
    @name= 'Možnosti spolupráce',
    @nameTitle= 'Možnosti spolupráce',
    @nameAnchor= 'Možnosti spolupráce',
    @annotation= 'Možnosti spolupráce',
    @description= 'Možnosti spolupráce',
    @keywords= 'Možnosti spolupráce',
    @content= 'Možnosti spolupráce',
    @hideFirstImage= 0,
    @hideCrossroad= 0,
    @showContactForm= 0,
    @links= '',
    @videos= '',

    @alias= 'moznosti-spoluprace';


INSERT INTO `tree` (`parentId`, `level`, `path`, `sort`, `last`, `public`, `uid`, `created`, `createdTime`, `edited`,
`editedTime`, `template`, `publicFrom`, `publicTo`, `name`, `nameTitle`, `nameAnchor`, `annotation`, `description`,
`keywords`, `content`, `hideFirstImage`, `links`, `videos`, `hideCrossroad`, `showContactForm`)
VALUES (@parentId, @level, @path, @sort, @last, @public, @uid, @created, NOW(), @edited, NOW(), @template, NOW(), NOW(),
@name, @nameTitle, @nameAnchor, @annotation,@description, @keywords, @content, @hideFirstImage, @links, @videos,
@hideCrossroad, @showContactForm);

INSERT INTO `alias` (`alias`, `modul`, `idref`, `lg`)
VALUES (@alias, 'tree', LAST_INSERT_ID(), 'cs');



SET @parentId=308,
    @level=1,
    @path='1|308|',
    @sort=1,
    @last=0,
    @public=1,
    @uid='',
    @created= 1,
    @edited= 1,
    @template= 'Pages:default',
    @name= 'FAQ',
    @nameTitle= 'FAQ',
    @nameAnchor= 'FAQ',
    @annotation= 'FAQ',
    @description= 'FAQ',
    @keywords= 'FAQ',
    @content= 'FAQ',
    @hideFirstImage= 0,
    @hideCrossroad= 0,
    @showContactForm= 0,
    @links= '',
    @videos= '',

    @alias= 'faq';


INSERT INTO `tree` (`parentId`, `level`, `path`, `sort`, `last`, `public`, `uid`, `created`, `createdTime`, `edited`,
`editedTime`, `template`, `publicFrom`, `publicTo`, `name`, `nameTitle`, `nameAnchor`, `annotation`, `description`,
`keywords`, `content`, `hideFirstImage`, `links`, `videos`, `hideCrossroad`, `showContactForm`)
VALUES (@parentId, @level, @path, @sort, @last, @public, @uid, @created, NOW(), @edited, NOW(), @template, NOW(), NOW(),
@name, @nameTitle, @nameAnchor, @annotation,@description, @keywords, @content, @hideFirstImage, @links, @videos,
@hideCrossroad, @showContactForm);

INSERT INTO `alias` (`alias`, `modul`, `idref`, `lg`)
VALUES (@alias, 'tree', LAST_INSERT_ID(), 'cs');







SET @parentId=308,
    @level=1,
    @path='1|308|',
    @sort=1,
    @last=0,
    @public=1,
    @uid='',
    @created= 1,
    @edited= 1,
    @template= 'Pages:default',
    @name= 'Jak funguje watchlist?',
    @nameTitle= 'Jak funguje watchlist?',
    @nameAnchor= 'Jak funguje watchlist?',
    @annotation= 'Jak funguje watchlist?',
    @description= 'Jak funguje watchlist?',
    @keywords= 'Jak funguje watchlist?',
    @content= 'Jak funguje watchlist?',
    @hideFirstImage= 0,
    @hideCrossroad= 0,
    @showContactForm= 0,
    @links= '',
    @videos= '',

    @alias= 'jak-funguje-watchlist';


INSERT INTO `tree` (`parentId`, `level`, `path`, `sort`, `last`, `public`, `uid`, `created`, `createdTime`, `edited`,
`editedTime`, `template`, `publicFrom`, `publicTo`, `name`, `nameTitle`, `nameAnchor`, `annotation`, `description`,
`keywords`, `content`, `hideFirstImage`, `links`, `videos`, `hideCrossroad`, `showContactForm`)
VALUES (@parentId, @level, @path, @sort, @last, @public, @uid, @created, NOW(), @edited, NOW(), @template, NOW(), NOW(),
@name, @nameTitle, @nameAnchor, @annotation,@description, @keywords, @content, @hideFirstImage, @links, @videos,
@hideCrossroad, @showContactForm);

INSERT INTO `alias` (`alias`, `modul`, `idref`, `lg`)
VALUES (@alias, 'tree', LAST_INSERT_ID(), 'cs');





SET @parentId=309,
    @level=1,
    @path='1|308|309|',
    @sort=1,
    @last=0,
    @public=1,
    @uid='',
    @created= 1,
    @edited= 1,
    @template= 'Pages:default',
    @name= 'Katalogy',
    @nameTitle= 'Katalogy',
    @nameAnchor= 'Katalogy',
    @annotation= 'Katalogy',
    @description= 'Katalogy',
    @keywords= 'Katalogy',
    @content= 'Katalogy',
    @hideFirstImage= 0,
    @hideCrossroad= 0,
    @showContactForm= 0,
    @links= '',
    @videos= '',

    @alias= 'katalogy';


INSERT INTO `tree` (`parentId`, `level`, `path`, `sort`, `last`, `public`, `uid`, `created`, `createdTime`, `edited`,
`editedTime`, `template`, `publicFrom`, `publicTo`, `name`, `nameTitle`, `nameAnchor`, `annotation`, `description`,
`keywords`, `content`, `hideFirstImage`, `links`, `videos`, `hideCrossroad`, `showContactForm`)
VALUES (@parentId, @level, @path, @sort, @last, @public, @uid, @created, NOW(), @edited, NOW(), @template, NOW(), NOW(),
@name, @nameTitle, @nameAnchor, @annotation,@description, @keywords, @content, @hideFirstImage, @links, @videos,
@hideCrossroad, @showContactForm);

INSERT INTO `alias` (`alias`, `modul`, `idref`, `lg`)
VALUES (@alias, 'tree', LAST_INSERT_ID(), 'cs');




SET @parentId=309,
    @level=1,
    @path='1|308|309|',
    @sort=1,
    @last=0,
    @public=1,
    @uid='',
    @created= 1,
    @edited= 1,
    @template= 'Pages:default',
    @name= 'Výpočetní software',
    @nameTitle= 'Výpočetní software',
    @nameAnchor= 'Výpočetní software',
    @annotation= 'Výpočetní software',
    @description= 'Výpočetní software',
    @keywords= 'Výpočetní software',
    @content= 'Výpočetní software',
    @hideFirstImage= 0,
    @hideCrossroad= 0,
    @showContactForm= 0,
    @links= '',
    @videos= '',

    @alias= 'vypocetni-software';


INSERT INTO `tree` (`parentId`, `level`, `path`, `sort`, `last`, `public`, `uid`, `created`, `createdTime`, `edited`,
`editedTime`, `template`, `publicFrom`, `publicTo`, `name`, `nameTitle`, `nameAnchor`, `annotation`, `description`,
`keywords`, `content`, `hideFirstImage`, `links`, `videos`, `hideCrossroad`, `showContactForm`)
VALUES (@parentId, @level, @path, @sort, @last, @public, @uid, @created, NOW(), @edited, NOW(), @template, NOW(), NOW(),
@name, @nameTitle, @nameAnchor, @annotation,@description, @keywords, @content, @hideFirstImage, @links, @videos,
@hideCrossroad, @showContactForm);

INSERT INTO `alias` (`alias`, `modul`, `idref`, `lg`)
VALUES (@alias, 'tree', LAST_INSERT_ID(), 'cs');




SET @parentId=309,
    @level=1,
    @path='1|308|309|',
    @sort=1,
    @last=0,
    @public=1,
    @uid='',
    @created= 1,
    @edited= 1,
    @template= 'Pages:default',
    @name= 'BIM',
    @nameTitle= 'BIM',
    @nameAnchor= 'BIM',
    @annotation= 'BIM',
    @description= 'BIM',
    @keywords= 'BIM',
    @content= 'BIM',
    @hideFirstImage= 0,
    @hideCrossroad= 0,
    @showContactForm= 0,
    @links= '',
    @videos= '',

    @alias= 'bim';


INSERT INTO `tree` (`parentId`, `level`, `path`, `sort`, `last`, `public`, `uid`, `created`, `createdTime`, `edited`,
`editedTime`, `template`, `publicFrom`, `publicTo`, `name`, `nameTitle`, `nameAnchor`, `annotation`, `description`,
`keywords`, `content`, `hideFirstImage`, `links`, `videos`, `hideCrossroad`, `showContactForm`)
VALUES (@parentId, @level, @path, @sort, @last, @public, @uid, @created, NOW(), @edited, NOW(), @template, NOW(), NOW(),
@name, @nameTitle, @nameAnchor, @annotation,@description, @keywords, @content, @hideFirstImage, @links, @videos,
@hideCrossroad, @showContactForm);

INSERT INTO `alias` (`alias`, `modul`, `idref`, `lg`)
VALUES (@alias, 'tree', LAST_INSERT_ID(), 'cs');






SET @parentId=309,
    @level=1,
    @path='1|308|309|',
    @sort=1,
    @last=0,
    @public=1,
    @uid='',
    @created= 1,
    @edited= 1,
    @template= 'Pages:default',
    @name= '3D Modely',
    @nameTitle= '3D Modely',
    @nameAnchor= '3D Modely',
    @annotation= '3D Modely',
    @description= '3D Modely',
    @keywords= '3D Modely',
    @content= '3D Modely',
    @hideFirstImage= 0,
    @hideCrossroad= 0,
    @showContactForm= 0,
    @links= '',
    @videos= '',

    @alias= '3d-modely';


INSERT INTO `tree` (`parentId`, `level`, `path`, `sort`, `last`, `public`, `uid`, `created`, `createdTime`, `edited`,
`editedTime`, `template`, `publicFrom`, `publicTo`, `name`, `nameTitle`, `nameAnchor`, `annotation`, `description`,
`keywords`, `content`, `hideFirstImage`, `links`, `videos`, `hideCrossroad`, `showContactForm`)
VALUES (@parentId, @level, @path, @sort, @last, @public, @uid, @created, NOW(), @edited, NOW(), @template, NOW(), NOW(),
@name, @nameTitle, @nameAnchor, @annotation,@description, @keywords, @content, @hideFirstImage, @links, @videos,
@hideCrossroad, @showContactForm);

INSERT INTO `alias` (`alias`, `modul`, `idref`, `lg`)
VALUES (@alias, 'tree', LAST_INSERT_ID(), 'cs');





SET @parentId=309,
    @level=1,
    @path='1|308|309|',
    @sort=1,
    @last=0,
    @public=1,
    @uid='',
    @created= 1,
    @edited= 1,
    @template= 'Pages:default',
    @name= 'Logo HALLA',
    @nameTitle= 'Logo HALLA',
    @nameAnchor= 'Logo HALLA',
    @annotation= 'Logo HALLA',
    @description= 'Logo HALLA',
    @keywords= 'Logo HALLA',
    @content= 'Logo HALLA',
    @hideFirstImage= 0,
    @hideCrossroad= 0,
    @showContactForm= 0,
    @links= '',
    @videos= '',

    @alias= 'logo-halla';


INSERT INTO `tree` (`parentId`, `level`, `path`, `sort`, `last`, `public`, `uid`, `created`, `createdTime`, `edited`,
`editedTime`, `template`, `publicFrom`, `publicTo`, `name`, `nameTitle`, `nameAnchor`, `annotation`, `description`,
`keywords`, `content`, `hideFirstImage`, `links`, `videos`, `hideCrossroad`, `showContactForm`)
VALUES (@parentId, @level, @path, @sort, @last, @public, @uid, @created, NOW(), @edited, NOW(), @template, NOW(), NOW(),
@name, @nameTitle, @nameAnchor, @annotation,@description, @keywords, @content, @hideFirstImage, @links, @videos,
@hideCrossroad, @showContactForm);

INSERT INTO `alias` (`alias`, `modul`, `idref`, `lg`)
VALUES (@alias, 'tree', LAST_INSERT_ID(), 'cs');





SET @parentId=309,
    @level=1,
    @path='1|308|309|',
    @sort=1,
    @last=0,
    @public=1,
    @uid='',
    @created= 1,
    @edited= 1,
    @template= 'Pages:default',
    @name= 'Reklamační řád',
    @nameTitle= 'Reklamační řád',
    @nameAnchor= 'Reklamační řád',
    @annotation= 'Reklamační řád',
    @description= 'Reklamační řád',
    @keywords= 'Reklamační řád',
    @content= 'Reklamační řád',
    @hideFirstImage= 0,
    @hideCrossroad= 0,
    @showContactForm= 0,
    @links= '',
    @videos= '',

    @alias= 'reklamacni-rad';


INSERT INTO `tree` (`parentId`, `level`, `path`, `sort`, `last`, `public`, `uid`, `created`, `createdTime`, `edited`,
`editedTime`, `template`, `publicFrom`, `publicTo`, `name`, `nameTitle`, `nameAnchor`, `annotation`, `description`,
`keywords`, `content`, `hideFirstImage`, `links`, `videos`, `hideCrossroad`, `showContactForm`)
VALUES (@parentId, @level, @path, @sort, @last, @public, @uid, @created, NOW(), @edited, NOW(), @template, NOW(), NOW(),
@name, @nameTitle, @nameAnchor, @annotation,@description, @keywords, @content, @hideFirstImage, @links, @videos,
@hideCrossroad, @showContactForm);

INSERT INTO `alias` (`alias`, `modul`, `idref`, `lg`)
VALUES (@alias, 'tree', LAST_INSERT_ID(), 'cs');


commit;
