POST products/_doc/1235
{
  "name": "PlayStation 4",
  "myId": 32,
  "isPublic" : false

}

GET products/_search

GET products/_alias

PUT products/_settings
{
  
}

POST _aliases
{
  "actions": [
    {
      "add": {
        "index": "products"
        , "alias": "products_prod"
      }
    }
  ]
}


GET products/_mapping
GET _settings

DELETE products

POST products/_doc/245
{
  "name": ["PlayStation 3", "PlStation"],
  "isPublic": true,
  "price": 1400,
  "databaseId": 124242
}

GET products/_analyze
{
  "text": ["Xbox 4 je super, protože má AMD procesor."],
  "analyzer": "myCustom"
}


PUT products
{
  "settings": {
    "analysis": {
      "analyzer": {
        "myCustom": {
          "type": "custom",
          "tokenizer": "standard",
          "filter": [
            "lowercase",
            "asciifolding"
            ]
        }
      }
    }
  }
}







GET products/_search
{
  "query": {
    "bool": {
      "must": [
        {
          "term": {
            "isPublic": {
              "value": false
            }
          }
          
        },
        {
          "match": {
            "name": {
              "query": "PlaeStation",
              "boost": 10,
              "operator": "OR",
              "fuzziness": "auto",
              "minimum_should_match":"60%"
            }
          }
        }
        ]
    }
  }
  
}


admin search:

GET products/_search
{
  "query": {
    "bool": {
      "must": [
        {
          "match": {
            "name": {
              "query": "PlStation",
              "boost": 10,
              "operator": "OR",
              "fuzziness": "auto",
              "minimum_should_match":"60%"
            }
          }
        }
      ],
      "should": [
        {
          "term": {
            "isPublic": {
              "value": true,
              "boost": 1
            }
          }
        }
      ]
    }
  }
  
}




GET products/_search
{
  "query": {
    "bool": {
      "must": [
        {
          "match": {
            "name": {
              "query": "PlStation",
              "boost": 10,
              "operator": "OR",
              "fuzziness": "auto",
              "minimum_should_match":"60%"
            }
          }
        }
      ],
      "must_not": [
        {
          "term": {
            "name": {
              "value": "PlStation",
            }
          }
        }
      ]
    }
  }
}



---
PUT products_ngram
{
  "settings": {
    "analysis": {
      "filter": {
        "customEdgeNgram": {
          "type": "edge_ngram",
          "min_gram": 2,
          "max_gram": 6
        }
      }
    }
  }
}

PUT products_stop
{
  "settings": {
    "analysis": {
      "filter": {
        "czechStopWords": {
          "type": "stop",
          "stopwords": [
            "_czech_",
            "lebo",
            "and"
            ]
        }
      }
    }
  }
}

PUT products_hunspell
{
  "settings": {
    "analysis": {
      "filter": {
        "dictionary_CZ": {
          "type": "hunspell",
          "locale": "cs_CZ"
        }
      }
    }
  }
}

PUT products_wd2
{
  "settings": {
    "analysis": {
      "filter": {
        "customWordDelimiter": {
          "type": "word_delimiter",
          "catenate_all": "true"
        }
      }
    }
  }
}


PUT products_common_grams2
{
  "settings": {
    "analysis": {
      "filter": {
        "customCommonGrams": {
          "type": "common_grams",
          "common_words": [
            "ks", "l", "litry", "litrů", "litr"
            ]
        },
        
        "czechStopWords": {
          "type": "stop",
          "stopwords": [
            "_czech_",
            "lebo",
            "and",
            "litr", "litrů", "litry"
            ]
        }
      }
    }
  }
}


PUT products_stemmer
{
  "settings": {
    "analysis": {
      "filter": {
        "customStemmer": {
          "type": "stemmer",
          "name": "Czech"
        }
          
      }
    }
  }
}

PUT products_synonym
{
  "settings": {
    "analysis": {
      "filter": {
        "customSynonym": {
          "type": "synonym",
          "synonyms": [
            "PlayStation => PS",
            "PlayStation => PSko"
            ]
        }
      }
    }
  }
}


GET products_synonym/_analyze
{
  "text": ["PlayStation 4 litry je super a růžový, protože má AMD procesor."],
  "filter": [ "customSynonym"],
  "tokenizer": "whitespace"
}


--analyzer
DELETE product_analyzer
PUT product_analyzer
{
  "settings": {
    "analysis": {
      "filter": {
        "czechSynonym": {
          "type": "synonym",
          "synonyms": [
            "PlayStation => PS",
            "PlayStation => PSko"
            ]
        },
         "customStemmer": {
          "type": "stemmer",
          "name": "Czech"
        },
         "customCommonGrams": {
          "type": "common_grams",
          "common_words": [
            "ks", "l", "litry", "litrů", "litr"
            ]
        },
        "czechStopWords": {
          "type": "stop",
          "stopwords": [
            "_czech_",
            "lebo",
            "and",
            "litr", "litrů", "litry"
            ]
        },
        "customWordDelimiter": {
          "type": "word_delimiter",
          "catenate_all": "true"
        },
        "dictionary_CZ": {
          "type": "hunspell",
          "locale": "cs_CZ"
        },
        "customEdgeNgram": {
          "type": "edge_ngram",
          "min_gram": 2,
          "max_gram": 6
        }
      },
      "analyzer": {
        "czechDictionary": {
          "filter": [
            "lowercase",
            "czechStopWords",
            "dictionary_CZ",
            "unique",
            "asciifolding"
          ],
          "type": "custom",
          "tokenizer": "standard"
        },
        "customEdgeNgram": {
          "filter":[
            "asciifolding",
            "lowercase",
            "stop",
            "customEdgeNgram",
            "unique"
            ],
            "type": "custom",
            "tokenizer": "standard"
        },
        "customCommonGrams": {
          "filter":[
            "asciifolding",
            "lowercase",
            "stop",
            "customCommonGrams",
            "unique"
            ],
            "type": "custom",
            "tokenizer": "standard"
        },
        "customWordDelimiter": {
          "filter":[
            "stop",
            "customWordDelimiter",
            "asciifolding",
            "lowercase",
            "unique"
            ],
            "type": "custom",
            "tokenizer": "standard"
        },
        "czechSynonym": {
          "filter":[
            "lowercase",
            "czechStopWords",
            "czechSynonym",
            "unique",
            "asciifolding"
            ],
            "type": "custom",
            "tokenizer": "standard"
        }
        
      }
    }
  }
}

PUT product_analyzer/_mapping
{
  "properties": {
      "availability": {
        "type": "keyword"
      },
      "price": {
        "type": "long"
      },
      "isPublic": {
        "type": "boolean"
      }
  }
  
}
  

PUT product_analyzer/_mapping
{
  "properties": {
    "name": {
      "fields": {
        "czechDictionary" : {
          "type": "text",
          "analyzer": "czechDictionary"
        },
        "czechSynonym": {
          "type": "text",
          "analyzer": "czechSynonym"
        },
        "edgeNgram": {
          "analyzer": "customEdgeNgram",
          "type": "text"
        },
        "wordSplit": {
          "type": "text",
          "analyzer": "customWordDelimiter"
        },
        "wordJoin": {
          "type": "text",
          "analyzer": "customCommonGrams"
        }
        
      },
      "type": "text"
    }
  }
}

GET product_analyzer/_mapping

POST product_analyzer/_doc/
{
  "name": "Xbox 892",
  "availability": "skladem",
  "isPublic": true,
  "price": 3400
  
}



GET product_analyzer/_search

GET product_analyzer/_search
{
  "query": {
    "bool": {
      "must": [
        {
          "multi_match": {
            "query": "playstation",
            "fields": [
              "name.czechSynonym",
              "name.czechDictionary",
              "name.edgeNgram",
              "name.wordJoin",
              "name.wordSplit"
              ],
              "type": "best_fields",
              "fuzziness": "auto"
          }
        },
        {
            "exists": {
              "field": "price"
            }
          }
        ],
      "must_not":[
        {
          "term": {
            "isPublic": true
          }
        }
        
        ]
        
    }
  }
  
}

----
agregace
-----

GET product_analyzer/_search
{
  "query": {
    "bool": {
      "must": [
        {
            "exists": {
              "field": "price"
            }
          }
        ]
     
        
    }
  },
  "aggs": {
    "price": {
      "terms": {
        "field": "price",
        "size": 1000
      }
    },
    "priceRange": {
      "range": {
        "field": "price",
        "ranges": [
          {
            "key": "aaa",
            "from": "0",
            "to": "1100"
          },
          {
            "key": "bbb",
            "from": 1100,
            "to": 5000
          }
        ]
      }
    },
    "priceHistogram": {
      "histogram": {
        "field": "price",
        "interval": 1000
      }
    }
  }
  
}

-sub agregace

GET product_analyzer/_search
{
  "query": {
    "bool": {
      "must": [
        {
            "exists": {
              "field": "price"
            }
          }
        ]
     
        
    }
  },
  "aggs": {
    "availability": {
      "aggs": {
        "price": {
          "terms": {
            "field": "price"
          }
        }
      }, 
      "terms": {
        "field": "availability",
        "size": 1000
      }
    }
  
  }
  
  
}













