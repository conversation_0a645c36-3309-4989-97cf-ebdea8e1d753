# SmartEmailing Integration Setup Guide

## Prerequisites

### 1. SmartEmailing Account Setup

1. **Get API Credentials**
   - Login to SmartEmailing admin panel
   - Go to Settings → API
   - Generate new API key
   - Copy the API key

2. **Create Custom Field for Interests**
   - In SmartEmailing admin, go to Settings → Custom fields
   - Create new field:
     - Name: `interests`
     - Type: Checkbox
     - Description: User interests for marketing segmentation
   - Save the field (options will be synced automatically from application)

3. **Create Contact Lists**
   - Go to Contacts → Contact lists
   - Create necessary lists (e.g., Newsletter, Marketing, etc.)
   - Note the list IDs for configuration

### 2. Application Configuration

1. **Set API Key in Configuration**
   ```neon
   # app/config/smartEmailing.neon
   smartEmailing:
       apiKey: "your-api-key-here"
       apiUrl: "https://app.smartemailing.cz/api/v3/"
   ```

2. **Environment Variables (Alternative)**
   ```env
   SMARTEMAILING_API_KEY=your-api-key-here
   ```

### 3. Database Setup

1. **Add User Interests**
   - Add interests to `user_interest` table
   - Each interest should have:
     - `uid`: unique identifier (e.g., "dogs", "cats", "technology")
     - `name`: Display name (e.g., "Dogs", "Cats", "Technology")
     - `sync_to_smart_emailing`: set to `true` for interests to sync
     - `smart_emailing_order`: optional order value (1, 2, 3...)

   Example SQL:
   ```sql
   INSERT INTO user_interest (uid, name, sync_to_smart_emailing, smart_emailing_order) VALUES
   ('dogs', 'Dogs', 1, 1),
   ('cats', 'Cats', 1, 2),
   ('technology', 'Technology', 1, 3),
   ('sports', 'Sports', 1, 4);
   ```

2. **Run Database Migrations**
   ```bash
   php bin/console migrations:migrate
   ```

### 4. Initial Synchronization

Run the setup command to sync everything:

```bash
php bin/console smartemailing:setup
```

This command will:
1. **Sync custom fields** - Fetches all custom fields from SmartEmailing API
2. **Setup interests field** - Creates/updates interests field configuration
3. **Sync interest options** - Creates checkbox options in SmartEmailing for each UserInterest
4. **Sync contact lists** - Imports all contact lists from SmartEmailing

### 5. Verify Setup

1. **Check SmartEmailing Admin**
   - Go to Settings → Custom fields → interests
   - Verify all interest options are created
   - Check that order matches your configuration

2. **Test User Sync**
   ```bash
   # Sync specific user
   php bin/console smartemailing:sync-user <EMAIL>

   # Sync all users
   php bin/console smartemailing:sync-all-users
   ```

3. **Check Logs**
   - Application logs: `var/log/smartemailing.log`
   - Check for any sync errors or warnings

## Regular Operations

### Daily/Hourly Sync (Cron)

Add to crontab for regular synchronization:

```cron
# Sync new/updated users every hour
0 * * * * php /path/to/app/bin/console smartemailing:sync-updated-users

# Full setup sync weekly (Sunday 2 AM)
0 2 * * 0 php /path/to/app/bin/console smartemailing:setup
```

### Manual Operations

```bash
# Subscribe user to newsletter
php bin/console smartemailing:subscribe <EMAIL>

# Unsubscribe user
php bin/console smartemailing:unsubscribe <EMAIL>

# Sync user interests
php bin/console smartemailing:sync-user-interests <EMAIL>

# Check sync status
php bin/console smartemailing:status
```

## Troubleshooting

### Common Issues

1. **"Cannot find interests field in SmartEmailing API"**
   - Create the `interests` field manually in SmartEmailing admin
   - Ensure field name is exactly `interests`

2. **"Option already exists" errors during sync**
   - Normal if interests were previously synced
   - Command will map existing options automatically

3. **User not syncing**
   - Check if user has valid email
   - Verify `smart_emailing_sync_enabled` is true for user
   - Check user's `smart_emailing_status` field

4. **Rate limiting errors**
   - SmartEmailing API has rate limits
   - Reduce batch size or add delays between requests
   - Check current limits in SmartEmailing documentation

### Debug Mode

Enable debug logging:
```neon
# app/config/smartEmailing.neon
smartEmailing:
    debug: true
    logLevel: debug
```

### Health Check

```bash
# Check API connectivity and configuration
php bin/console smartemailing:health-check
```

## Data Flow

```
User Registration/Update
    ↓
User entity saved with interests
    ↓
SmartEmailingSyncService triggered
    ↓
Custom fields mapped (including interests)
    ↓
API call to SmartEmailing
    ↓
Contact created/updated with custom field values
```

## Security Notes

- Never commit API keys to repository
- Use environment variables for sensitive data
- Implement rate limiting for bulk operations
- Regular audit of synced data
- Monitor failed sync attempts

## Contact

For SmartEmailing API documentation: https://app.smartemailing.cz/docs/api/v3/index.html
