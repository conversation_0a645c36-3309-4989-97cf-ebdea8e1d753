const basePath = {
	src: 'src/',
	dest: 'www/static/',
	assets: '../',
};

const src = {
	fonts: `${basePath.src}fonts/`,
	icons: `${basePath.src}img/icons/`,
	images: `${basePath.src}img/`,
	scripts: `${basePath.src}js/`,
	styles: `${basePath.src}css/`,
	templates: `${basePath.src}tpl/`,
	components: `${basePath.src}tpl/components/`,
	layout: `${basePath.src}tpl/layout/`,
};

const twigNamespaces = {
	components: src.components,
	layout: src.layout,
	images: src.images,
	templates: src.templates,
};

const dest = {
	fonts: `${basePath.dest}fonts/`,
	images: `${basePath.dest}img/`,
	scripts: `${basePath.dest}js/`,
	styles: `${basePath.dest}css/`,
	templates: `${basePath.dest}tpl/`,
};

const assets = {
	fonts: `${basePath.assets}fonts/`,
	images: `${basePath.assets}img/`,
	scripts: `${basePath.assets}js/`,
	dynamicScripts: `/static/js/`,
	styles: `${basePath.assets}css/`,
};

const webpack = {
	stats: {
		colors: true,
		hash: false,
		timings: true,
		assets: true,
		chunks: false,
		chunkModules: false,
		modules: false,
		children: true,
		version: false,
	},
};

const browserSync = {
	open: false,
	notify: false,
	reloadThrottle: 1000,
	watch: true,
	server: {
		baseDir: basePath.dest,
	},
};

module.exports = {
	basePath,
	src,
	dest,
	assets,
	twigNamespaces,
	webpack,
	browserSync,
	mediaQueries: {
		breakpoints: {
			xs: 375 / 16 + 'rem',
			sm: 480 / 16 + 'rem',
			md: 750 / 16 + 'rem',
			lg: 1000 / 16 + 'rem',
			xl: 1200 / 16 + 'rem',
			xxl: 1500 / 16 + 'rem',
			xxxl: 1920 / 16 + 'rem',
		},
		rules: {
			webkit: '(-webkit-min-device-pixel-ratio: 0)',
			retina: '(-webkit-min-device-pixel-ratio: 2), (min--moz-device-pixel-ratio: 2), (-o-min-device-pixel-ratio: 2/1), (min-device-pixel-ratio: 2), (min-resolution: 192dpi), (min-resolution: 2dppx)',
		},
	},
};
