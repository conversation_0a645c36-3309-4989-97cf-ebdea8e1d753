const parse = require('parse-sass-value');
const { kebabCase, mapKeys } = require('lodash');

const deepIterate = (obj, fn) => {
	Object.keys(obj).forEach((key) => {
		if (typeof obj[key] === 'object' && obj[key] !== null) {
			obj[key] = deepIterate(obj[key], fn);
		}
	});

	return fn(obj);
};

const kebabizeKeys = (obj) => mapKeys(obj, (_, key) => kebabCase(key));

const makeSassVars = (variables) => {
	return Object.keys(variables).map((name) => {
		let value = 'null';

		try {
			value = parse(variables[name]);
		} catch (error) {
			console.log(`skipping var ${name}.\n` + `: ${error.message}`);
		}

		return `$${name}: ${value};`;
	});
};

const compileSassVars = (variables) => {
	return makeSassVars(deepIterate(variables, kebabizeKeys)).join('\n');
};

module.exports = {
	compileSassVars,
};
