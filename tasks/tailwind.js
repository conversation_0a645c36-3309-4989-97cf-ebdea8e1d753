const { src, dest } = require('gulp');
const postcss = require('gulp-postcss');
const tailwindcss = require('tailwindcss');
const autoprefixer = require('autoprefixer');
const cssnano = require('cssnano');
const config = require('./helpers/getConfig.js');

module.exports = function tailwind(done) {
	const isProduction = process.env.NODE_ENV === 'production';

	return src(['tailwind.css'], {
		cwd: config.src.styles,
	}) // Vstupní Tailwind soubor
		.pipe(
			postcss([
				tailwindcss('./tailwind.config.js'), // Tailwind plugin
				autoprefixer({ grid: 'autoplace' }), // Autoprefixer
				...(isProduction ? [cssnano()] : []), // Minifikace v produkci
			]),
		)
		.pipe(
			dest(config.dest.styles, {
				sourcemaps: './',
			}),
		)
		.on('end', done);
};
