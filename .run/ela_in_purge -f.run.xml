<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="ela:in:purge -f" type="PhpLocalRunConfigurationType" factoryName="PHP Console" folderName="elastic" path="$PROJECT_DIR$/bin/console" scriptParameters="ela:in:purge -f">
    <CommandLine>
      <PhpTestInterpreterSettings>
        <option name="interpreterName" value="PHP8.2" />
      </PhpTestInterpreterSettings>
    </CommandLine>
    <method v="2" />
  </configuration>
  <configuration default="false" name="ela:in:purge -f" type="PhpLocalRunConfigurationType" factoryName="PHP Console" folderName="elastic" path="$PROJECT_DIR$/bin/console" scriptParameters="ela:in:purge -f">
    <method v="2" />
  </configuration>
</component>