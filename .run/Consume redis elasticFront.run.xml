<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="Consume redis elasticFront" type="CompoundRunConfigurationType">
    <toRun name="messenger:consume elasticFront (1)" type="PhpLocalRunConfigurationType" />
    <toRun name="messenger:consume elasticFront (10)" type="PhpLocalRunConfigurationType" />
    <toRun name="messenger:consume elasticFront (11)" type="PhpLocalRunConfigurationType" />
    <toRun name="messenger:consume elasticFront (12)" type="PhpLocalRunConfigurationType" />
    <toRun name="messenger:consume elasticFront (13)" type="PhpLocalRunConfigurationType" />
    <toRun name="messenger:consume elasticFront (14)" type="PhpLocalRunConfigurationType" />
    <toRun name="messenger:consume elasticFront (15)" type="PhpLocalRunConfigurationType" />
    <toRun name="messenger:consume elasticFront (2)" type="PhpLocalRunConfigurationType" />
    <toRun name="messenger:consume elasticFront (3)" type="PhpLocalRunConfigurationType" />
    <toRun name="messenger:consume elasticFront (4)" type="PhpLocalRunConfigurationType" />
    <toRun name="messenger:consume elasticFront (5)" type="PhpLocalRunConfigurationType" />
    <toRun name="messenger:consume elasticFront (6)" type="PhpLocalRunConfigurationType" />
    <toRun name="messenger:consume elasticFront (7)" type="PhpLocalRunConfigurationType" />
    <toRun name="messenger:consume elasticFront (8)" type="PhpLocalRunConfigurationType" />
    <toRun name="messenger:consume elasticFront (9)" type="PhpLocalRunConfigurationType" />
    <method v="2" />
  </configuration>
</component>