<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="Consume redis import" type="CompoundRunConfigurationType">
    <toRun name="consumer01" type="PhpLocalRunConfigurationType" />
    <toRun name="consumer02" type="PhpLocalRunConfigurationType" />
    <toRun name="consumer03" type="PhpLocalRunConfigurationType" />
    <toRun name="consumer04" type="PhpLocalRunConfigurationType" />
    <toRun name="consumer05" type="PhpLocalRunConfigurationType" />
    <toRun name="consumer06" type="PhpLocalRunConfigurationType" />
    <toRun name="consumer07" type="PhpLocalRunConfigurationType" />
    <toRun name="consumer08" type="PhpLocalRunConfigurationType" />
    <toRun name="consumer09" type="PhpLocalRunConfigurationType" />
    <toRun name="consumer10" type="PhpLocalRunConfigurationType" />
    <method v="2" />
  </configuration>
</component>